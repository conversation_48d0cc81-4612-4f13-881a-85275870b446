<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美国签证验证问题选择器演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #409eff;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .question-section {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
            background: #fafafa;
        }
        .question-section h4 {
            margin-top: 0;
            color: #409eff;
        }
        .error-message {
            color: #f56c6c;
            font-size: 12px;
            margin-top: 5px;
        }
        .success-message {
            color: #67c23a;
            font-size: 12px;
            margin-top: 5px;
        }
        .submit-btn {
            background: #409eff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
        }
        .submit-btn:hover {
            background: #66b1ff;
        }
        .submit-btn:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background: #f0f9ff;
            border: 1px solid #409eff;
        }
        .questions-list {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .questions-list h4 {
            margin-top: 0;
            color: #495057;
        }
        .questions-list ol {
            margin: 0;
            padding-left: 20px;
        }
        .questions-list li {
            margin: 5px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇺🇸 美国签证验证问题选择器演示</h1>
            <p>演示防重复选择功能和数据验证</p>
        </div>

        <div class="questions-list">
            <h4>📋 可选择的验证问题（15个）</h4>
            <ol id="questionsList">
                <li>您母亲的姓氏是什么?</li>
                <li>您的第一个/目前/最喜欢的宠物名称是什么?</li>
                <li>您的第一辆汽车是什么车?</li>
                <li>您在哪所小学上学?</li>
                <li>您出生的地方/城市叫什么名字?</li>
                <li>您成长的道路/街道叫称是什么?</li>
                <li>您最不喜欢的食物是什么?</li>
                <li>您的第一份工作是在哪家公司?</li>
                <li>您最喜欢的食物是什么?</li>
                <li>您在哪所高中求学?</li>
                <li>您在哪里遇见您的配偶?</li>
                <li>您的兄弟姐妹的中间名字是什么?</li>
                <li>谁是您儿时的英雄?</li>
                <li>您的第一份工作在哪个城市或地方?</li>
                <li>您申请但未就读的大学是哪所大学?</li>
            </ol>
        </div>

        <form id="usVisaForm">
            <div class="form-group">
                <label for="name">姓名 *</label>
                <input type="text" id="name" name="name" placeholder="请输入中文姓名" required>
                <div id="nameError" class="error-message"></div>
            </div>

            <div class="form-group">
                <label for="username">美签账户用户名 *</label>
                <input type="text" id="username" name="username" placeholder="请输入美签官方账户用户名" required>
                <div id="usernameError" class="error-message"></div>
            </div>

            <div class="form-group">
                <label for="password">美签账户密码 *</label>
                <input type="password" id="password" name="password" placeholder="请输入美签官方账户密码" required>
                <div id="passwordError" class="error-message"></div>
            </div>

            <div class="question-section">
                <h4>🔐 验证问题（必须选择3个不同的问题）</h4>
                
                <div class="form-group">
                    <label for="question1">安全问题1 *</label>
                    <select id="question1" name="question1" required>
                        <option value="">请选择第1个安全问题</option>
                    </select>
                    <div id="question1Error" class="error-message"></div>
                </div>
                <div class="form-group">
                    <label for="answer1">安全答案1 *</label>
                    <input type="text" id="answer1" name="answer1" placeholder="请输入第1个安全答案" required>
                    <div id="answer1Error" class="error-message"></div>
                </div>

                <div class="form-group">
                    <label for="question2">安全问题2 *</label>
                    <select id="question2" name="question2" required>
                        <option value="">请选择第2个安全问题</option>
                    </select>
                    <div id="question2Error" class="error-message"></div>
                </div>
                <div class="form-group">
                    <label for="answer2">安全答案2 *</label>
                    <input type="text" id="answer2" name="answer2" placeholder="请输入第2个安全答案" required>
                    <div id="answer2Error" class="error-message"></div>
                </div>

                <div class="form-group">
                    <label for="question3">安全问题3 *</label>
                    <select id="question3" name="question3" required>
                        <option value="">请选择第3个安全问题</option>
                    </select>
                    <div id="question3Error" class="error-message"></div>
                </div>
                <div class="form-group">
                    <label for="answer3">安全答案3 *</label>
                    <input type="text" id="answer3" name="answer3" placeholder="请输入第3个安全答案" required>
                    <div id="answer3Error" class="error-message"></div>
                </div>
            </div>

            <button type="submit" class="submit-btn">提交美国签证信息</button>
        </form>

        <div id="result" class="result" style="display: none;">
            <h4>✅ 提交成功！</h4>
            <pre id="resultData"></pre>
        </div>
    </div>

    <script>
        // 预定义的验证问题
        const US_SECURITY_QUESTIONS = [
            "您母亲的姓氏是什么?",
            "您的第一个/目前/最喜欢的宠物名称是什么?",
            "您的第一辆汽车是什么车?",
            "您在哪所小学上学?",
            "您出生的地方/城市叫什么名字?",
            "您成长的道路/街道叫称是什么?",
            "您最不喜欢的食物是什么?",
            "您的第一份工作是在哪家公司?",
            "您最喜欢的食物是什么?",
            "您在哪所高中求学?",
            "您在哪里遇见您的配偶?",
            "您的兄弟姐妹的中间名字是什么?",
            "谁是您儿时的英雄?",
            "您的第一份工作在哪个城市或地方?",
            "您申请但未就读的大学是哪所大学?"
        ];

        // 获取可用问题（排除已选择的）
        function getAvailableQuestions(excludeIndex) {
            const selectedQuestions = [];
            for (let i = 1; i <= 3; i++) {
                if (i !== excludeIndex) {
                    const value = document.getElementById(`question${i}`).value;
                    if (value) selectedQuestions.push(value);
                }
            }
            return US_SECURITY_QUESTIONS.filter(q => !selectedQuestions.includes(q));
        }

        // 更新选择器选项
        function updateQuestionOptions(selectIndex) {
            const select = document.getElementById(`question${selectIndex}`);
            const currentValue = select.value;
            const availableQuestions = getAvailableQuestions(selectIndex);
            
            // 清空选项
            select.innerHTML = `<option value="">请选择第${selectIndex}个安全问题</option>`;
            
            // 添加可用选项
            availableQuestions.forEach(question => {
                const option = document.createElement('option');
                option.value = question;
                option.textContent = question;
                if (question === currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        }

        // 初始化选择器
        function initializeSelectors() {
            for (let i = 1; i <= 3; i++) {
                updateQuestionOptions(i);
                
                // 添加变更事件监听
                document.getElementById(`question${i}`).addEventListener('change', function() {
                    // 更新其他选择器
                    for (let j = 1; j <= 3; j++) {
                        if (j !== i) {
                            updateQuestionOptions(j);
                        }
                    }
                    clearError(`question${i}Error`);
                });
            }
        }

        // 清除错误信息
        function clearError(errorId) {
            document.getElementById(errorId).textContent = '';
        }

        // 显示错误信息
        function showError(errorId, message) {
            document.getElementById(errorId).textContent = message;
        }

        // 验证表单
        function validateForm() {
            let isValid = true;
            
            // 清除所有错误信息
            const errorElements = document.querySelectorAll('.error-message');
            errorElements.forEach(el => el.textContent = '');
            
            // 验证姓名
            const name = document.getElementById('name').value.trim();
            if (!name) {
                showError('nameError', '请输入姓名');
                isValid = false;
            }
            
            // 验证用户名
            const username = document.getElementById('username').value.trim();
            if (!username) {
                showError('usernameError', '请输入美签账户用户名');
                isValid = false;
            }
            
            // 验证密码
            const password = document.getElementById('password').value.trim();
            if (!password) {
                showError('passwordError', '请输入美签账户密码');
                isValid = false;
            }
            
            // 验证问题和答案
            const selectedQuestions = [];
            for (let i = 1; i <= 3; i++) {
                const question = document.getElementById(`question${i}`).value;
                const answer = document.getElementById(`answer${i}`).value.trim();
                
                if (!question) {
                    showError(`question${i}Error`, `请选择第${i}个安全问题`);
                    isValid = false;
                }
                
                if (!answer) {
                    showError(`answer${i}Error`, `请输入第${i}个安全答案`);
                    isValid = false;
                }
                
                if (question) {
                    selectedQuestions.push(question);
                }
            }
            
            // 检查问题是否重复
            const uniqueQuestions = [...new Set(selectedQuestions)];
            if (selectedQuestions.length !== uniqueQuestions.length) {
                for (let i = 1; i <= 3; i++) {
                    showError(`question${i}Error`, '验证问题不能重复选择');
                }
                isValid = false;
            }
            
            return isValid;
        }

        // 处理表单提交
        document.getElementById('usVisaForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!validateForm()) {
                return;
            }
            
            // 构建数据
            const formData = {
                name: document.getElementById('name').value.trim(),
                us_username: document.getElementById('username').value.trim(),
                us_password: document.getElementById('password').value.trim(),
                us_qa: [
                    {
                        DISP: document.getElementById('question1').value,
                        VAL: document.getElementById('answer1').value.trim()
                    },
                    {
                        DISP: document.getElementById('question2').value,
                        VAL: document.getElementById('answer2').value.trim()
                    },
                    {
                        DISP: document.getElementById('question3').value,
                        VAL: document.getElementById('answer3').value.trim()
                    }
                ]
            };
            
            // 显示结果
            document.getElementById('resultData').textContent = JSON.stringify(formData, null, 2);
            document.getElementById('result').style.display = 'block';
            
            // 滚动到结果区域
            document.getElementById('result').scrollIntoView({ behavior: 'smooth' });
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSelectors();
            console.log('美国签证验证问题选择器演示页面已加载');
        });
    </script>
</body>
</html>
