#!/usr/bin/env python3
"""
美国签证API测试脚本
测试新增的美国签证用户信息提交接口
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8000"
LOGIN_URL = f"{BASE_URL}/api/login"
US_VISA_URL = f"{BASE_URL}/api/submit_us_visa_info"
GET_SUBMISSIONS_URL = f"{BASE_URL}/api/get_us_visa_submissions"

# 测试用户凭据
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def login():
    """登录获取token"""
    print("🔐 正在登录...")
    response = requests.post(LOGIN_URL, json=TEST_USER)
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == 1:
            token = data.get("token")
            print(f"✅ 登录成功，token: {token[:20]}...")
            return token
        else:
            print(f"❌ 登录失败: {data.get('message')}")
            return None
    else:
        print(f"❌ 登录请求失败: {response.status_code}")
        return None

def test_us_visa_submission(token):
    """测试美国签证用户信息提交"""
    print("\n📝 测试美国签证用户信息提交...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试数据
    test_data = {
        "clients": [
            {
                "name": "张三",
                "surname_pinyin": "ZHANG",
                "firstname_pinyin": "SAN",
                "dob": "1990-01-01",
                "passport": "E12345678",
                "passport_expire": "2030-01-01",
                "passport_image": None,
                "gender": "男",
                "nationality": "CHN",
                "passport_date": "2020-01-01",
                # 美国签证特殊字段
                "us_username": "test_username",
                "us_password": "test_password",
                "us_security_question_1": "What is your mother's maiden name?",
                "us_security_answer_1": "Smith",
                "us_security_question_2": "What was your first pet's name?",
                "us_security_answer_2": "Buddy",
                "us_security_question_3": "What city were you born in?",
                "us_security_answer_3": "Beijing"
            }
        ],
        "visaType": [["usa", "US_DEFAULT", "TOURIST", "B1_B2"]],
        "dateRangeList": [["2024-01-01", "2024-01-07"]],
        "travel_date": "2024-02-01",
        "customer": "张三",
        "remark": "测试美国签证提交"
    }
    
    print(f"📤 发送请求到: {US_VISA_URL}")
    print(f"📋 请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    response = requests.post(US_VISA_URL, json=test_data, headers=headers)
    
    print(f"📨 响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"📄 响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        if data.get("code") == 1:
            print("✅ 美国签证用户信息提交成功！")
            submission_id = data.get("submission_id")
            print(f"🆔 提交ID: {submission_id}")
            return submission_id
        else:
            print(f"❌ 提交失败: {data.get('message')}")
            return None
    else:
        print(f"❌ 请求失败: {response.status_code}")
        print(f"📄 错误响应: {response.text}")
        return None

def test_get_submissions(token):
    """测试获取用户的美国签证提交记录"""
    print("\n📋 测试获取美国签证提交记录...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(GET_SUBMISSIONS_URL, headers=headers)
    
    print(f"📨 响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"📄 响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        if data.get("code") == 1:
            submissions = data.get("data", [])
            print(f"✅ 获取成功，共 {len(submissions)} 条记录")
            for i, submission in enumerate(submissions, 1):
                print(f"  {i}. ID: {submission.get('submission_id')}")
                print(f"     创建时间: {submission.get('created_at')}")
                print(f"     状态: {submission.get('status')}")
                print(f"     客户数量: {submission.get('clients_count')}")
                print(f"     客户姓名: {', '.join(submission.get('client_names', []))}")
        else:
            print(f"❌ 获取失败: {data.get('message')}")
    else:
        print(f"❌ 请求失败: {response.status_code}")
        print(f"📄 错误响应: {response.text}")

def test_validation_errors(token):
    """测试验证错误情况"""
    print("\n🚫 测试验证错误情况...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 缺少必填字段的测试数据
    invalid_data = {
        "clients": [
            {
                "name": "李四",
                "surname_pinyin": "LI",
                "firstname_pinyin": "SI",
                "dob": "1985-05-15",
                "passport": "E87654321",
                "passport_expire": "2028-05-15",
                "gender": "女",
                "nationality": "CHN",
                "passport_date": "2018-05-15",
                # 故意缺少美国签证特殊字段
                "us_username": "",  # 空值
                "us_password": "test_password",
                # 缺少安全问题和答案
            }
        ],
        "visaType": [["usa", "US_DEFAULT", "TOURIST", "B1_B2"]],
        "dateRangeList": [["2024-01-01", "2024-01-07"]],
        "travel_date": "2024-02-01",
        "customer": "李四",
        "remark": "测试验证错误"
    }
    
    print("📤 发送包含验证错误的请求...")
    response = requests.post(US_VISA_URL, json=invalid_data, headers=headers)
    
    print(f"📨 响应状态码: {response.status_code}")
    
    if response.status_code == 422:
        print("✅ 正确返回验证错误状态码 422")
        print(f"📄 验证错误详情: {response.text}")
    else:
        data = response.json()
        print(f"📄 响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

def main():
    """主测试函数"""
    print("🚀 开始美国签证API测试")
    print("=" * 50)
    
    # 1. 登录
    token = login()
    if not token:
        print("❌ 无法获取token，测试终止")
        return
    
    # 2. 测试美国签证用户信息提交
    submission_id = test_us_visa_submission(token)
    
    # 3. 测试获取提交记录
    test_get_submissions(token)
    
    # 4. 测试验证错误
    test_validation_errors(token)
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
    
    if submission_id:
        print(f"✅ 成功提交的记录ID: {submission_id}")
        print("💡 可以在Redis中查看数据:")
        print(f"   redis-cli GET us_visa_submissions:{submission_id}")
    else:
        print("❌ 未成功提交任何记录")

if __name__ == "__main__":
    main()
