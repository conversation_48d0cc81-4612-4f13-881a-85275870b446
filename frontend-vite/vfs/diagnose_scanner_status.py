#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VFS集成日历扫描器状态诊断工具
"""
import sys
import os
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def check_database_status():
    """检查数据库状态"""
    print("🔍 检查数据库状态...")

    try:
        from db_utils import get_db_connection
        from psycopg2.extras import RealDictCursor

        conn = get_db_connection()
        if not conn:
            print("❌ 数据库连接失败")
            return False

        print("✅ 数据库连接成功")

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 检查表是否存在
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'date_ranges'
                )
            """)
            table_exists = cursor.fetchone()[0]

            if not table_exists:
                print("❌ 表 'date_ranges' 不存在")
                print("💡 请先创建表:")
                print("""
CREATE TABLE date_ranges (
    id SERIAL PRIMARY KEY,
    order_id VARCHAR(255) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
                """)
                conn.close()
                return False

            print("✅ 表 'date_ranges' 存在")

            # 检查数据
            cursor.execute("SELECT COUNT(*) FROM date_ranges")
            count = cursor.fetchone()[0]

            print(f"📊 表中记录数: {count}")

            if count == 0:
                print("⚠️ 表中没有数据")
                print("💡 请插入测试数据:")
                print("""
INSERT INTO date_ranges (order_id, start_date, end_date) VALUES
('TEST_ORDER_001', CURRENT_DATE, CURRENT_DATE + INTERVAL '30 days'),
('TEST_ORDER_002', CURRENT_DATE + INTERVAL '7 days', CURRENT_DATE + INTERVAL '60 days');
                """)
            else:
                # 显示前几条记录
                cursor.execute("""
                    SELECT order_id, start_date, end_date 
                    FROM date_ranges 
                    ORDER BY start_date 
                    LIMIT 5
                """)
                records = cursor.fetchall()

                print("📋 前5条记录:")
                for record in records:
                    print(f"  📅 订单: {record['order_id']} | {record['start_date']} ~ {record['end_date']}")

        conn.close()
        return count > 0

    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False


def check_redis_status():
    """检查Redis状态"""
    print("\n🔍 检查Redis状态...")

    try:
        from RedisClient import RedisClient

        redis_client = RedisClient()

        # 检查Redis连接
        test_key = "test_scanner_diagnosis"
        redis_client.set(test_key, "test_value")
        test_value = redis_client.get(test_key)

        if test_value == "test_value":
            print("✅ Redis连接成功")
            redis_client.delete(test_key)
        else:
            print("❌ Redis连接失败")
            return False

        # 检查必需的配置
        configs = {
            'rsa_str': 'RSA加密密钥',
            'login_proxy': '代理列表'
        }

        missing_configs = []

        for key, description in configs.items():
            value = redis_client.get(key)
            if value:
                if key == 'login_proxy':
                    try:
                        proxies = json.loads(value)
                        print(f"✅ {description}: 包含 {len(proxies)} 个代理")
                    except:
                        print(f"❌ {description}: 数据格式错误")
                        missing_configs.append(key)
                else:
                    print(f"✅ {description}: 长度 {len(value)} 字符")
            else:
                print(f"❌ {description}: 未找到")
                missing_configs.append(key)

        if missing_configs:
            print(f"\n⚠️ 缺少配置: {missing_configs}")
            return False

        return True

    except Exception as e:
        print(f"❌ Redis检查失败: {e}")
        return False


def check_vfs_customers_and_urns():
    """检查VFS客户和URN数据"""
    print("\n🔍 检查VFS客户和URN数据...")

    try:
        from db_utils import get_db_connection
        from RedisClient import RedisClient
        from psycopg2.extras import RealDictCursor
        import time

        # 数据库连接
        conn = get_db_connection()
        if not conn:
            print("❌ 无法连接数据库")
            return False

        # Redis连接
        redis_client = RedisClient()

        customers_with_urns = 0
        total_customers = 0

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查询VFS客户
            cursor.execute("""
                SELECT order_id, mission_code, center_code, visa_code
                FROM orders
                WHERE is_vfs_order = true
                AND order_status IN ('registe_success', 'urn_create_failed')
                ORDER BY order_id
                LIMIT 10
            """)
            customers = cursor.fetchall()

            total_customers = len(customers)
            print(f"📊 数据库中VFS客户: {total_customers} 个（显示前10个）")

            for customer in customers:
                order_id = customer['order_id']

                # 检查URN
                urn_key = f"vfs_urn:{order_id}"
                urn_data = redis_client.get(urn_key)

                if urn_data:
                    try:
                        urn_info = json.loads(urn_data)
                        create_time = urn_info.get('createTime', 0)
                        remaining_time = 2000 - (int(time.time()) - create_time)

                        if remaining_time > 0:
                            customers_with_urns += 1
                            print(f"✅ {order_id}: 有效URN | 国家: {customer['mission_code']} | 剩余: {remaining_time}秒")
                        else:
                            print(f"⚠️ {order_id}: URN已过期 | 国家: {customer['mission_code']}")
                    except:
                        print(f"❌ {order_id}: URN数据格式错误")
                else:
                    print(f"❌ {order_id}: 无URN | 国家: {customer['mission_code']}")

        conn.close()

        print(f"📈 总计: {customers_with_urns}/{total_customers} 个客户有有效URN")

        return customers_with_urns > 0

    except Exception as e:
        print(f"❌ VFS客户和URN检查失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False


def check_available_tokens():
    """检查可用Token"""
    print("\n🔍 检查可用Token...")

    try:
        from RedisClient import RedisClient
        import time

        redis_client = RedisClient()

        # 检查常见国家的Token
        countries = ["hun", "fra", "esp", "ita", "deu"]

        print(f"📊 检查 {len(countries)} 个国家的可用Token")

        countries_with_tokens = 0
        total_valid_tokens = 0

        for country in countries:
            # 使用与扫描器相同的逻辑查找Token
            pattern = f"vfs_token:*:{country}"
            token_keys = redis_client.keys(pattern)

            valid_tokens = 0
            for key in token_keys:
                try:
                    cached_data = redis_client.get(key)
                    if cached_data:
                        token_data = json.loads(cached_data)
                        # 检查Token是否还有效（至少剩余300秒）
                        remaining_time = 6000 - (int(time.time()) - token_data.get('updateTokenTime', 0))
                        if remaining_time > 300:
                            valid_tokens += 1
                            total_valid_tokens += 1
                except:
                    continue

            if valid_tokens > 0:
                countries_with_tokens += 1
                print(f"✅ {country}: {valid_tokens} 个可用Token")
            else:
                print(f"❌ {country}: 无可用Token")

        print(f"📈 总计: {countries_with_tokens}/{len(countries)} 个国家有可用Token，共 {total_valid_tokens} 个")

        return countries_with_tokens > 0 and total_valid_tokens > 0

    except Exception as e:
        print(f"❌ 可用Token检查失败: {e}")
        return False


def suggest_solutions():
    """建议解决方案"""
    print("\n💡 问题解决建议:")

    print("\n1. 如果数据库表不存在或无数据:")
    print("   - 运行: python test_integrated_calendar_scanner.py")
    print("   - 这会创建测试表和数据")

    print("\n2. 如果Redis配置缺失:")
    print("   - 检查Redis中是否有 rsa_str 密钥")
    print("   - 检查Redis中是否有 login_proxy 代理列表")

    print("\n3. 如果没有可用Token:")
    print("   - 确保各国家有有效的Token缓存")
    print("   - 格式: vfs_token:{email}:{country}")
    print("   - Token需要有至少300秒的剩余有效期")

    print("\n4. 调试模式运行:")
    print("   - 修改日志级别为DEBUG")
    print("   - 观察详细的执行过程")


def main():
    """主函数"""
    print("VFS集成日历扫描器状态诊断")
    print("=" * 50)

    # 检查各个组件
    db_ok = check_database_status()
    redis_ok = check_redis_status()
    vfs_ok = check_vfs_customers_and_urns()
    tokens_ok = check_available_tokens()

    print("\n" + "=" * 50)
    print("📊 诊断结果汇总:")
    print(f"  数据库状态: {'✅ 正常' if db_ok else '❌ 异常'}")
    print(f"  Redis配置: {'✅ 正常' if redis_ok else '❌ 异常'}")
    print(f"  VFS客户和URN: {'✅ 正常' if vfs_ok else '❌ 异常'}")
    print(f"  可用Token: {'✅ 正常' if tokens_ok else '❌ 异常'}")

    if db_ok and redis_ok and vfs_ok and tokens_ok:
        print("\n🎉 所有检查通过！VFS日历扫描器应该可以正常运行")
        print("💡 如果仍然没有扫描活动，请检查日志级别设置")
    else:
        print("\n⚠️ 发现问题，请根据上述建议进行修复")
        suggest_solutions()


if __name__ == "__main__":
    main()
