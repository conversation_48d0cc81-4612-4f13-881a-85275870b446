#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置VFS集成日历扫描器的测试数据
"""
import sys
import os
import json
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_database_table_and_data():
    """创建数据库表和测试数据"""
    print("🔧 创建数据库表和测试数据...")
    
    try:
        from db_utils import get_db_connection
        
        conn = get_db_connection()
        if not conn:
            print("❌ 无法连接数据库")
            return False
        
        with conn.cursor() as cursor:
            # 创建表（如果不存在）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS date_ranges (
                    id SERIAL PRIMARY KEY,
                    order_id VARCHAR(255) NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            print("✅ 表 'date_ranges' 已创建")
            
            # 清理旧的测试数据
            cursor.execute("DELETE FROM date_ranges WHERE order_id LIKE 'TEST_%'")
            
            # 创建测试数据
            today = datetime.now().date()
            test_data = [
                {
                    'order_id': 'TEST_ORDER_001',
                    'start_date': today,
                    'end_date': today + timedelta(days=30)
                },
                {
                    'order_id': 'TEST_ORDER_002',
                    'start_date': today + timedelta(days=7),
                    'end_date': today + timedelta(days=60)
                },
                {
                    'order_id': 'TEST_ORDER_003',
                    'start_date': today - timedelta(days=5),  # 过期日期，会被调整为今天
                    'end_date': today + timedelta(days=45)
                },
                {
                    'order_id': 'TEST_ORDER_004',
                    'start_date': today + timedelta(days=14),
                    'end_date': today + timedelta(days=90)
                },
                {
                    'order_id': 'TEST_ORDER_005',
                    'start_date': today + timedelta(days=21),
                    'end_date': today + timedelta(days=120)
                }
            ]
            
            for data in test_data:
                cursor.execute("""
                    INSERT INTO date_ranges (order_id, start_date, end_date)
                    VALUES (%(order_id)s, %(start_date)s, %(end_date)s)
                """, data)
            
            conn.commit()
            
            print(f"✅ 插入了 {len(test_data)} 条测试数据")
            
            # 验证数据
            cursor.execute("SELECT COUNT(*) FROM date_ranges")
            total_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT order_id, start_date, end_date FROM date_ranges ORDER BY start_date LIMIT 5")
            records = cursor.fetchall()
            
            print(f"📊 表中总记录数: {total_count}")
            print("📋 前5条记录:")
            for record in records:
                print(f"  📅 订单: {record[0]} | {record[1]} ~ {record[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库数据失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def create_redis_scan_data():
    """创建Redis扫描数据配置"""
    print("\n🔧 创建Redis扫描数据配置...")
    
    try:
        from RedisClientAWS import RedisClient
        
        redis_client = RedisClient()
        
        # 创建扫描数据配置
        scan_data = {
            "hun": {
                "scanAreas": [
                    {
                        "missionCode": "hun",
                        "centerCode": "PEK",
                        "centerName": "北京",
                        "visaCategoryCode": "S1",
                        "visaCategoryName": "短期签证",
                        "calendarId": "12345",
                        "seizeSeat": True
                    },
                    {
                        "missionCode": "hun",
                        "centerCode": "SHA",
                        "centerName": "上海",
                        "visaCategoryCode": "S1",
                        "visaCategoryName": "短期签证",
                        "calendarId": "12346",
                        "seizeSeat": True
                    }
                ]
            },
            "fra": {
                "scanAreas": [
                    {
                        "missionCode": "fra",
                        "centerCode": "PEK",
                        "centerName": "北京",
                        "visaCategoryCode": "T1",
                        "visaCategoryName": "旅游签证",
                        "calendarId": "67890",
                        "seizeSeat": False
                    },
                    {
                        "missionCode": "fra",
                        "centerCode": "SHA",
                        "centerName": "上海",
                        "visaCategoryCode": "T1",
                        "visaCategoryName": "旅游签证",
                        "calendarId": "67891",
                        "seizeSeat": False
                    }
                ]
            },
            "esp": {
                "scanAreas": [
                    {
                        "missionCode": "esp",
                        "centerCode": "PEK",
                        "centerName": "北京",
                        "visaCategoryCode": "T2",
                        "visaCategoryName": "商务签证",
                        "calendarId": "11111",
                        "seizeSeat": True
                    }
                ]
            }
        }
        
        redis_client.set("scanData", json.dumps(scan_data, ensure_ascii=False))
        print("✅ scanData 配置已设置")
        print(f"📊 包含国家: {list(scan_data.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建Redis扫描数据失败: {e}")
        return False

def create_redis_proxy_data():
    """创建Redis代理数据"""
    print("\n🔧 创建Redis代理数据...")
    
    try:
        from RedisClientAWS import RedisClient
        
        redis_client = RedisClient()
        
        # 创建代理列表（示例代理，实际使用时需要替换为真实代理）
        proxy_list = [
            "http://proxy1.example.com:8080",
            "http://proxy2.example.com:8080",
            "http://proxy3.example.com:8080",
            "http://proxy4.example.com:8080",
            "http://proxy5.example.com:8080"
        ]
        
        redis_client.set("worker_proxy", json.dumps(proxy_list))
        print("✅ worker_proxy 配置已设置")
        print(f"📊 包含代理: {len(proxy_list)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建Redis代理数据失败: {e}")
        return False

def create_login_users():
    """创建登录用户数据"""
    print("\n🔧 创建登录用户数据...")
    
    try:
        from RedisClientAWS import RedisClient
        
        redis_client = RedisClient()
        
        # 为每个国家创建测试登录用户
        countries = ["hun", "fra", "esp"]
        
        for country in countries:
            login_users = {}
            
            # 为每个国家创建2个测试用户
            for i in range(1, 3):
                email = f"test{i}@{country}.example.com"
                user_data = {
                    "email": email,
                    "token": f"test_token_{country}_{i}",
                    "ltsn": f"test_session_{country}_{i}",
                    "createTime": int(datetime.now().timestamp()) - 3600  # 1小时前创建
                }
                login_users[email] = json.dumps(user_data, ensure_ascii=False)
            
            # 设置到Redis
            redis_key = f"{country}LoginUser"
            for email, user_json in login_users.items():
                redis_client.hset(redis_key, email, user_json)
            
            print(f"✅ {country}LoginUser: {len(login_users)} 个用户")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建登录用户数据失败: {e}")
        return False

def verify_setup():
    """验证设置"""
    print("\n🔍 验证设置...")
    
    try:
        # 验证数据库
        from db_utils import get_db_connection
        conn = get_db_connection()
        if conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM date_ranges")
                db_count = cursor.fetchone()[0]
                print(f"✅ 数据库: {db_count} 条记录")
            conn.close()
        
        # 验证Redis
        from RedisClientAWS import RedisClient
        redis_client = RedisClient()
        
        scan_data = redis_client.get("scanData")
        if scan_data:
            countries = list(json.loads(scan_data).keys())
            print(f"✅ Redis scanData: {len(countries)} 个国家")
        
        proxy_data = redis_client.get("worker_proxy")
        if proxy_data:
            proxies = json.loads(proxy_data)
            print(f"✅ Redis代理: {len(proxies)} 个")
        
        # 检查登录用户
        user_count = 0
        for country in ["hun", "fra", "esp"]:
            users = redis_client.hgetall(f"{country}LoginUser")
            if users:
                user_count += len(users)
        
        print(f"✅ 登录用户: {user_count} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证设置失败: {e}")
        return False

def main():
    """主函数"""
    print("VFS集成日历扫描器测试数据设置")
    print("=" * 50)
    
    success_count = 0
    total_steps = 5
    
    steps = [
        ("创建数据库表和数据", create_database_table_and_data),
        ("创建Redis扫描数据", create_redis_scan_data),
        ("创建Redis代理数据", create_redis_proxy_data),
        ("创建登录用户数据", create_login_users),
        ("验证设置", verify_setup)
    ]
    
    for step_name, step_func in steps:
        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} - 完成")
            else:
                print(f"❌ {step_name} - 失败")
        except Exception as e:
            print(f"❌ {step_name} - 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 设置结果: {success_count}/{total_steps} 步骤完成")
    
    if success_count == total_steps:
        print("🎉 所有测试数据设置完成！")
        print("\n🚀 现在可以运行扫描器:")
        print("   python integrated_calendar_scanner.py")
        
        print("\n🔍 或者重新运行诊断:")
        print("   python diagnose_scanner_status.py")
        
        return True
    else:
        print("⚠️ 部分设置失败，请检查错误信息")
        return False

if __name__ == "__main__":
    main()
