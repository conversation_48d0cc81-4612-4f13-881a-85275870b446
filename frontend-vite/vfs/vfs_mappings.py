# -*- coding: utf-8 -*-
"""
VFS 映射工具：提供国家/中心/签证类型名称解析与10分钟本地缓存。
- 数据来源：Redis 的 vfs_center_data（兼容两种结构）
- 提供方法：get_mission_name, get_center_name, get_visa_name
注意：内部自带 get_visa_config 实现，避免与业务模块产生循环依赖。
"""
import json
import time
from typing import Dict, Optional

from RedisClient import RedisClient

redis_client = RedisClient()

# 本地缓存（10分钟）
_MAPPING_TTL = 600
# 全局缓存：国家名映射 + 全局中心名兜底 + 分国家的中心名
_mapping_cache: Dict[str, Dict] = {"mission": {}, "center": {}, "center_by_mission": {}}
_mapping_cache_time = 0
# 分国家中心映射的逐国时间戳（用于按需补充/刷新）
_center_mission_times: Dict[str, int] = {}


def _extract_leaf_nodes(node: dict) -> dict:
    children = node.get("data") or node.get("sub")
    cleaned = {
        k: node[k]
        for k in ("name", "missionCode", "centerCode", "code", "missionCodeName", "isoCode", "centerName")
        if k in node
    }
    if isinstance(children, list) and children:
        cleaned["children"] = [_extract_leaf_nodes(child) for child in children]
    return cleaned


def _traverse(node: dict) -> list:
    nodes = [node]
    for ch in node.get("children", []) or []:
        nodes.extend(_traverse(ch))
    return nodes


def _build_maps_from_redis() -> Dict[str, Dict]:
    global _mapping_cache, _mapping_cache_time
    now = int(time.time())
    if (
        now - _mapping_cache_time < _MAPPING_TTL
        and _mapping_cache.get("mission")
        and _mapping_cache.get("center")
    ):
        return _mapping_cache
    try:
        # 兼容两种结构：hash 与 单key
        vfs_data = redis_client.client.hgetall("vfs_center_data")
        parsed = [json.loads(val) for val in vfs_data.values()] if vfs_data else []
        roots = [_extract_leaf_nodes(entry) for entry in parsed]
        mission_map: Dict[str, str] = {}
        center_map: Dict[str, str] = {}
        center_by_mission: Dict[str, Dict[str, str]] = {}
        for root in roots:
            mcode = (root.get("missionCode") or "").strip()
            mname = root.get("missionCodeName")
            if mcode and mname:
                mission_map[mcode] = mname
            # 为该国家构建中心映射
            if mcode and mcode not in center_by_mission:
                center_by_mission[mcode] = {}
            for node in _traverse(root):
                ccode = node.get("isoCode")
                cname = node.get("centerName")
                if ccode and cname:
                    cname_clean = cname.strip() if isinstance(cname, str) else cname
                    center_map[ccode] = cname_clean  # 全局兜底
                    if mcode:
                        center_by_mission[mcode][ccode] = cname_clean
        _mapping_cache = {"mission": mission_map, "center": center_map, "center_by_mission": center_by_mission}
        _mapping_cache_time = now
    except Exception:
        # 失败则保持旧缓存
        pass
    return _mapping_cache


def get_mission_name(mission_code: str) -> str:
    maps = _build_maps_from_redis()
    return maps.get("mission", {}).get(mission_code, mission_code)


def _ensure_center_map_for_mission(mission_code: str) -> Dict[str, str]:
    """按需构建/刷新某个国家的中心映射（优先单键，其次hash hget）。"""
    mc = (mission_code or "").strip()
    now = int(time.time())
    if not mc:
        return _build_maps_from_redis().get("center", {})
    if (
        mc in _mapping_cache.get("center_by_mission", {})
        and now - _center_mission_times.get(mc, 0) < _MAPPING_TTL
    ):
        return _mapping_cache["center_by_mission"][mc]
    # 读取单国配置
    try:
        center_data_str = redis_client.get(f"vfs_center_data:{mc}") or redis_client.hget("vfs_center_data", mc)
        if center_data_str:
            data = json.loads(center_data_str)
            cmap: Dict[str, str] = {}
            for center in data.get("data", []) or []:
                ccode = center.get("isoCode")
                cname = center.get("centerName")
                if ccode and cname:
                    cmap[ccode] = cname.strip() if isinstance(cname, str) else cname
            # 写入缓存
            _mapping_cache.setdefault("center_by_mission", {})[mc] = cmap
            _center_mission_times[mc] = now
            return cmap
    except Exception:
        pass
    # 回退：使用全局（hash构建）
    return _build_maps_from_redis().get("center_by_mission", {}).get(mc, {}) or _mapping_cache.get("center", {})


def get_center_name(center_code: str, mission_code: Optional[str] = None) -> str:
    cc = (center_code or "").strip()
    if mission_code:
        cmap = _ensure_center_map_for_mission(mission_code)
        return cmap.get(cc, _mapping_cache.get("center", {}).get(cc, cc))
    maps = _build_maps_from_redis()
    return maps.get("center", {}).get(cc, cc)


# ===== 签证配置/名称 =====

def _get_visa_config(mission_code: str, center_code: str, visa_code: str) -> Optional[dict]:
    """自包含的签证配置查找，避免依赖业务模块。"""
    try:
        # 优先单key
        center_data_str = redis_client.get(f"vfs_center_data:{mission_code}")
        if not center_data_str:
            center_data_str = redis_client.hget("vfs_center_data", mission_code)
            if not center_data_str:
                return None
        center_data = json.loads(center_data_str)
        # 遍历中心
        for center in center_data.get("data", []):
            if center.get("isoCode") == center_code:
                def find(items):
                    for item in items:
                        if item.get("code") == visa_code:
                            return item
                        if "sub" in item:
                            r = find(item["sub"])
                            if r:
                                return r
                    return None
                return find(center.get("sub", []))
        return None
    except Exception:
        return None


def get_visa_name(mission_code: str, center_code: str, visa_code: str) -> str:
    cfg = _get_visa_config(mission_code, center_code, visa_code)
    if cfg and isinstance(cfg, dict):
        return cfg.get("name") or visa_code
    return visa_code
