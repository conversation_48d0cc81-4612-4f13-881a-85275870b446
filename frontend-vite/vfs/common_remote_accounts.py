# -*- coding: utf-8 -*-
"""
公共远程账号获取模块
当前提供德国(deu)账号从远程Redis读取的方法，供多个模块复用。
"""
import json
import random
import time
import logging
from typing import Optional, Dict

from RedisClient import RedisClient

logger = logging.getLogger('VFS_AccountManager')


def get_ita_account_from_remote() -> Optional[Dict]:
    """从远程Redis读取德国账号信息。
    返回token_data结构: {email, token, phone, ltsn?, missionCode:'ita', updateTokenTime: now-60}
    随机挑选哈希 itaLoginUser 中的一条记录（值为JSON字符串）。
    """
    try:
        remote = RedisClient(host='***********', port=6379, password='TicketsCache#2023', db=0)
        accounts = remote.hgetall('itaLoginUserSHI')
        if not accounts:
            logger.warning("ITA远程Redis未返回任何账号数据 | hash=itaLoginUserSHI")
            return None
        acct = random.choice(accounts)
        if isinstance(acct, str):
            try:
                acct = json.loads(acct)
            except Exception:
                logger.error("ITA远程账号值非JSON且无法解析")
                return None
        email = acct.get('email')
        token = acct.get('token')
        phone = acct.get('phone')
        ltsn = acct.get('ltsn') if isinstance(acct.get('ltsn'), str) else None
        urn = acct.get('urn') if isinstance(acct.get('urn'), str) else None
        cf_clearance = acct.get('cf_clearance') if isinstance(acct.get('cf_clearance'), str) else None
        login_proxy = acct.get('login_proxy') if isinstance(acct.get('login_proxy'), str) else None
        cf_user_agent = acct.get('cf_user_agent') if isinstance(acct.get('cf_user_agent'), str) else None
        if not email or not token:
            logger.error("DEU远程账号缺少必要字段 email/token")
            return None
        token_data = {
            'email': email,
            'token': token,
            'phone': phone,
            'ltsn': ltsn,
            'urn': urn,
            'cf_clearance': cf_clearance,
            'login_proxy': login_proxy,
            'cf_user_agent': cf_user_agent,
            'missionCode': 'ita',
            'updateTokenTime': int(time.time()) - 60
        }
        logger.info(f"ITA远程账号获取成功 | email={email} | token_data={token_data}")
        return token_data
    except Exception as e:
        logger.error(f"ITA远程账号获取异常: {e}")
        return None


def get_deu_account_from_remote() -> Optional[Dict]:
    """从远程Redis读取德国账号信息。
    返回token_data结构: {email, token, phone, ltsn?, missionCode:'deu', updateTokenTime: now-60}
    随机挑选哈希 deuLoginUser 中的一条记录（值为JSON字符串）。
    """
    try:
        remote = RedisClient(host='************', port=6379, password='TicketsCache#2023', db=0)
        accounts = remote.hgetall('deuLoginUser')
        if not accounts:
            logger.warning("DEU远程Redis未返回任何账号数据 | hash=deuLoginUser")
            return None
        acct = random.choice(accounts)
        if isinstance(acct, str):
            try:
                acct = json.loads(acct)
            except Exception:
                logger.error("DEU远程账号值非JSON且无法解析")
                return None
        email = acct.get('email')
        token = acct.get('token')
        phone = acct.get('phone')
        ltsn = acct.get('ltsn') if isinstance(acct.get('ltsn'), str) else None
        urn = acct.get('urn') if isinstance(acct.get('urn'), str) else None
        if not email or not token:
            logger.error("DEU远程账号缺少必要字段 email/token")
            return None
        token_data = {
            'email': email,
            'token': token,
            'phone': phone,
            'ltsn': ltsn,
            'urn': urn,
            'missionCode': 'deu',
            'updateTokenTime': int(time.time()) - 60
        }
        logger.info(f"DEU远程账号获取成功 | email={email} | token_data={token_data}")
        return token_data
    except Exception as e:
        logger.error(f"DEU远程账号获取异常: {e}")
        return None
