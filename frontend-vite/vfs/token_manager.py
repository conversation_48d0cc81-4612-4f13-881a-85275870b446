# -*- coding: utf-8 -*-
"""
VFS Token管理模块 - 提供Token缓存和获取功能
"""
import json
import time
import logging
from RedisClient import RedisClient

# 配置日志
logger = logging.getLogger('Token_Manager')

# Redis客户端
redis_client = RedisClient()


def get_cached_token(email, country):
    """从Redis获取缓存的Token"""
    try:
        cache_key = f"vfs_token:{email}:{country}"
        cached_data = redis_client.get(cache_key)
        if cached_data:
            token_data = json.loads(cached_data)
            # 检查是否过期（6000秒有效期）
            remaining_time = 6000 - (int(time.time()) - token_data.get('updateTokenTime', 0))
            if remaining_time > 0:
                print(f"使用缓存Token | 邮箱: {email} | 剩余时间: {remaining_time}秒")
                return token_data
            else:
                # Token过期，删除缓存
                redis_client.delete(cache_key)
                print(f"缓存Token已过期 | 邮箱: {email}")
        return None
    except Exception as e:
        print(f"获取缓存Token失败 | 邮箱: {email} | 异常: {str(e)}")
        return None


def cache_token(email, country, token_data):
    """将Token缓存到Redis"""
    try:
        cache_key = f"vfs_token:{email}:{country}"
        # 设置过期时间为6000秒
        redis_client.setex(cache_key, 6000, json.dumps(token_data))
        print(f"Token已缓存 | 邮箱: {email} | 有效期: 6000秒")
        return True
    except Exception as e:
        print(f"缓存Token失败 | 邮箱: {email} | 异常: {str(e)}")
        return False


def get_available_token_for_country(country):
    """获取指定国家的可用Token"""
    try:
        # 从Redis缓存中查找可用的Token
        pattern = f"vfs_token:*:{country}"
        token_keys = redis_client.keys(pattern)

        for key in token_keys:
            try:
                cached_data = redis_client.get(key)
                if cached_data:
                    token_data = json.loads(cached_data)
                    # 检查Token是否还有效（至少剩余300秒）
                    remaining_time = 6000 - (int(time.time()) - token_data.get('updateTokenTime', 0))
                    if remaining_time > 300:
                        print(f"找到可用Token | 国家: {country} | 邮箱: {token_data.get('email')} | 剩余时间: {remaining_time}秒")
                        return token_data
            except json.JSONDecodeError:
                # 删除无效的缓存
                redis_client.delete(key)
                continue

        print(f"未找到国家 {country} 的可用Token")
        return None

    except Exception as e:
        print(f"获取可用Token失败 | 国家: {country} | 异常: {str(e)}")
        return None


def get_all_cached_tokens():
    """获取所有缓存的Token信息"""
    try:
        pattern = "vfs_token:*"
        token_keys = redis_client.keys(pattern)
        tokens = []

        for key in token_keys:
            try:
                cached_data = redis_client.get(key)
                if cached_data:
                    token_data = json.loads(cached_data)
                    remaining_time = 6000 - (int(time.time()) - token_data.get('updateTokenTime', 0))

                    tokens.append({
                        "email": token_data.get('email'),
                        "country": token_data.get('missionCode'),
                        "remaining_time": remaining_time,
                        "status": "valid" if remaining_time > 0 else "expired"
                    })
            except json.JSONDecodeError:
                # 删除无效的缓存
                redis_client.delete(key)
                continue

        return tokens

    except Exception as e:
        print(f"获取所有缓存Token失败 | 异常: {str(e)}")
        return []


def clean_expired_tokens():
    """清理过期的Token"""
    try:
        pattern = "vfs_token:*"
        token_keys = redis_client.keys(pattern)
        cleaned_count = 0

        for key in token_keys:
            try:
                cached_data = redis_client.get(key)
                if cached_data:
                    token_data = json.loads(cached_data)
                    remaining_time = 6000 - (int(time.time()) - token_data.get('updateTokenTime', 0))

                    if remaining_time <= 0:
                        redis_client.delete(key)
                        cleaned_count += 1
                        print(f"清理过期Token | 邮箱: {token_data.get('email')}")
            except json.JSONDecodeError:
                # 删除无效的缓存
                redis_client.delete(key)
                cleaned_count += 1
                continue

        if cleaned_count > 0:
            print(f"清理过期Token完成 | 清理数量: {cleaned_count}")

        return cleaned_count

    except Exception as e:
        print(f"清理过期Token失败 | 异常: {str(e)}")
        return 0


def get_token_statistics():
    """获取Token统计信息"""
    try:
        tokens = get_all_cached_tokens()
        stats = {
            "total": len(tokens),
            "valid": len([t for t in tokens if t["status"] == "valid"]),
            "expired": len([t for t in tokens if t["status"] == "expired"]),
            "by_country": {}
        }

        for token in tokens:
            country = token["country"]
            if country not in stats["by_country"]:
                stats["by_country"][country] = {"total": 0, "valid": 0, "expired": 0}

            stats["by_country"][country]["total"] += 1
            if token["status"] == "valid":
                stats["by_country"][country]["valid"] += 1
            else:
                stats["by_country"][country]["expired"] += 1

        return stats

    except Exception as e:
        print(f"获取Token统计失败 | 异常: {str(e)}")
        return {"total": 0, "valid": 0, "expired": 0, "by_country": {}}


if __name__ == "__main__":
    # 测试Token管理功能
    print("Token管理模块测试")
    print("=" * 40)

    # 获取统计信息
    stats = get_token_statistics()
    print(f"Token统计: 总计={stats['total']}, 有效={stats['valid']}, 过期={stats['expired']}")

    # 清理过期Token
    cleaned = clean_expired_tokens()
    print(f"清理过期Token: {cleaned}个")

    # 显示各国家Token情况
    for country, country_stats in stats["by_country"].items():
        print(f"国家 {country}: 总计={country_stats['total']}, 有效={country_stats['valid']}")
