# -*- coding: utf-8 -*-
"""
waitlist_cancel_worker

持续扫描数据库，查找以下订单并调用 CancelWaitlist 接口取消：
- order_status = 'deleted'
- mission_code = 'deu'
- center_code ∈ ['GRSG', 'GR NN', 'GR HZ']

成功取消后发送企微通知（取消成功）。

依赖：
- db_utils.get_db_connection
- vfs_calendar_scanner.fetch_cf_bypass_params
- order_urn_manager: get_deu_account_from_remote, send_wecom_notify_waitlist_success,
  get_clients_for_order, encryption, get_current_timestamp
- RedisClient: 作为互斥锁，避免重复取消同一订单
"""
from __future__ import annotations

import time
import json
import random
import logging
from typing import Optional, Dict

from curl_cffi import requests

# 复用现有模块
from db_utils import get_db_connection
from RedisClient import RedisClient
from vfs_calendar_scanner import fetch_cf_bypass_params
from common_remote_accounts import get_deu_account_from_remote
from vfs_toolkit import (
    send_wecom_notify_waitlist_success,
    get_clients_for_order,
    encryption,
    get_current_timestamp,
)

# Logger 配置
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s | %(levelname)-8s | %(threadName)-15s | %(funcName)-20s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
logger = logging.getLogger(__name__)

# 常量
CANCEL_URL = "https://lift-apicn.vfsglobal.com/appointment/Cancel"
DEU_TARGET_CENTERS = {"GRSG", "GR NN", "GR HZ"}  # 按用户要求
SCAN_INTERVAL_SECONDS = 60
LOCK_TTL_SECONDS = 180

redis_client = RedisClient()


def _acquire_cancel_lock(order_id: str, ttl: int = LOCK_TTL_SECONDS) -> bool:
    try:
        key = f"vfs_cancel_lock:{order_id}"
        ok = redis_client.client.set(key, str(time.time()), nx=True, ex=ttl)
        if ok:
            logger.debug(f"获取取消锁成功 | 订单: {order_id}")
            return True
        logger.info(f"已有进程在处理取消 | 订单: {order_id}")
        return False
    except Exception as e:
        logger.warning(f"获取取消锁异常 | 订单: {order_id} | 错误: {e}")
        return False


def _build_headers(token_data: Dict[str, str], mission_code: str, cookie_header: str, r_auth: str) -> Dict[str, str]:
    return {
        "Content-Type": "application/json;charset=UTF-8",
        "Authorize": r_auth,
        "authorize": token_data.get("token", ""),
        "route": f"chn/zh/{mission_code}",
        "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "macOS",
        "origin": "https://visa.vfsglobal.com",
        "sec-fetch-site": "same-site",
        "sec-fetch-mode": "cors",
        "sec-fetch-dest": "empty",
        "referer": "https://visa.vfsglobal.com/",
        "accept-encoding": "gzip, deflate, br",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "cookie": cookie_header,
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    }


def _random_r_auth() -> str:
    # 生成随机授权头（与项目中保持一致长度）
    rand_len = 597
    rand_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    body = "".join(random.choice(rand_chars) for _ in range(rand_len))
    return f"EAAAAN{body}="


def get_cached_token(email, country):
    """从Redis获取缓存的Token"""
    try:
        cache_key = f"vfs_token:{email}:{country}"
        cached_data = redis_client.get(cache_key)
        if cached_data:
            token_data = json.loads(cached_data)
            # 检查是否过期（6000秒有效期）
            if int(time.time()) - token_data.get('updateTokenTime', 0) < 6000:
                remaining_time = 6000 - (int(time.time()) - token_data.get('updateTokenTime', 0))
                logger.debug(f"使用缓存Token | 邮箱: {email} | 国家: {country} | 剩余时间: {remaining_time}秒")
                return token_data
            else:
                # Token过期，删除缓存
                redis_client.delete(cache_key)
                logger.debug(f"缓存Token已过期 | 邮箱: {email} | 国家: {country}")
        return None
    except Exception as e:
        logger.error(f"获取缓存Token失败 | 邮箱: {email} | 国家: {country} | 异常: {str(e)}")
        return None


def _cancel_waitlist_for_order(order: dict) -> bool:
    """对单个订单执行 Cancel。成功返回 True。"""
    order_id = order.get("order_id")
    mission_code = (order.get("mission_code") or "").lower()
    center_code = order.get("center_code")
    visa_code = order.get("visa_code")
    urn = order.get("urn")

    if not urn:
        logger.info(f"订单 {order_id} 无 URN，跳过取消")
        return False

    # 获取德国远程账号
    if mission_code in ['deu', 'ita']:
        token_data = get_deu_account_from_remote()
    else:
        token_data = get_cached_token(order.get("vfs_account"), mission_code)
    if not token_data:
        logger.warning(f"订单 {order_id} 获取 DEU 远程账号失败，跳过")
        return False

    # 获取 CF 参数
    cf_params = fetch_cf_bypass_params(None, logger_prefix="CancelWL")
    ua_from_cf = cf_params.get("user_agent") if cf_params else None
    cf_clearance = cf_params.get("cookie") if cf_params else None
    cf_proxy = cf_params.get("proxy") if cf_params else None

    # 组装 cookie
    ltsn = token_data.get("ltsn") or ""
    cookie_header = ltsn
    if cf_clearance:
        cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"

    r_auth = _random_r_auth()
    headers = _build_headers(token_data, mission_code, cookie_header, r_auth)
    if ua_from_cf:
        headers["user-agent"] = ua_from_cf

    payload = {
        "countryCode": "chn",
        "missionCode": mission_code,
        "centerCode": center_code,
        "loginUser": token_data.get("email"),
        "visaCategoryCode": visa_code,
        "urn": urn,
    }
    # 若存在订单账号，按约定追加 LogINUseR
    vfs_account = order.get("vfs_account")
    if vfs_account:
        payload["LogINUseR"] = vfs_account

    proxies = {"http": cf_proxy, "https": cf_proxy} if cf_proxy else None

    try:
        max_retries = 3
        resp = None
        for attempt in range(max_retries):
            logger.info(f"🔁 Cancel请求 | 订单:{order_id} | center:{center_code} | visa:{visa_code} | 尝试 {attempt+1}/{max_retries}")
            try:
                resp = requests.post(
                    CANCEL_URL,
                    json=payload,
                    headers=headers,
                    proxies=proxies,
                    impersonate='chrome136',
                    timeout=30,
                    verify=False,
                )
            except Exception as req_e:
                logger.warning(f"Cancel 请求异常 | 订单:{order_id} | 错误:{req_e} | 尝试 {attempt+1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep((attempt + 1) * 2)
                    continue
                else:
                    return False
            logger.info(f"Cancel响应 | 订单:{order_id} | 状态码:{resp.status_code} | 尝试 {attempt+1}/{max_retries}")
            if resp.status_code != 200:
                if attempt < max_retries - 1:
                    logger.warning(f"Cancel 非200 | 订单:{order_id} | 状态码:{resp.status_code} | 重试中...")
                    time.sleep((attempt + 1) * 2)
                    continue
                else:
                    logger.warning(f"Cancel 非200 | 订单:{order_id} | 文本:{resp.text}")
                    return False
            break
        try:
            data = resp.json()
            logger.warning(f"Cancel JSON | 订单:{order_id} | 文本:{data}")
        except Exception:
            logger.warning(f"Cancel 响应非JSON | 订单:{order_id} | 文本:{resp.text}")
            return False

        try:
            first_client = (get_clients_for_order(order_id) or [{}])[0]
            send_wecom_notify_waitlist_success(order_id, mission_code, center_code, visa_code, first_client, "取消成功")
            logger.info(f"✅ 取消成功并已发送企微通知 | 订单:{order_id}")
        except Exception as ne:
            logger.warning(f"取消成功但通知失败 | 订单:{order_id} | 错误:{ne}")
        # 清空数据库中的 URN 与 vfs_account
        _update_order_stateus(order_id)
        return True
    except Exception as e:
        logger.error(f"CancelWaitlist 调用异常 | 订单:{order_id} | 错误:{e}")
        return False


def _update_order_stateus(order_id: str) -> bool:
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            logger.error(f"失败，无法获取数据库连接 | 订单:{order_id}")
            return False
        with conn.cursor() as cur:
            cur.execute(
                "UPDATE orders SET order_status = 'appointment_canceled' WHERE order_id = %s",
                (order_id,)
            )
        conn.commit()
        logger.info(f"已设置状态appointment_canceled | 订单:{order_id} | appointment_canceled")
        return True
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"设置数据库状态appointment_canceled异常 | 订单:{order_id} | 错误:{e}")
        return False
    finally:
        try:
            if conn:
                conn.close()
        except Exception:
            pass


def _fetch_deleted_deu_orders() -> list[dict]:
    """查询符合条件的订单："""
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("无法获取数据库连接")
            return []
        sql = (
            """
            SELECT order_id, mission_code, center_code, visa_code, urn, vfs_account
            FROM orders
            WHERE is_vfs_order = true
              AND order_status = 'waiting_for_cancel'
              AND urn IS NOT NULL AND urn <> ''
            """
        )
        with conn.cursor() as cur:
            cur.execute(sql)
            rows = cur.fetchall()
        # rows 可能是元组，统一转 dict（字段顺序与 SELECT 对齐）
        orders = []
        for r in rows:
            if isinstance(r, dict):
                orders.append(r)
            else:
                order = {
                    "order_id": r[0],
                    "mission_code": r[1],
                    "center_code": r[2],
                    "visa_code": r[3],
                    "urn": r[4],
                    "vfs_account": r[5] if len(r) > 5 else None,
                }
                orders.append(order)
        logger.info(f"查询到待取消的订单数: {len(orders)}")
        return orders
    except Exception as e:
        logger.error(f"查询订单失败: {e}")
        return []
    finally:
        try:
            if conn:
                conn.close()
        except Exception:
            pass


def worker_loop():
    logger.info("waitlist_cancel_worker 启动")
    while True:
        try:
            orders = _fetch_deleted_deu_orders()
            if not orders:
                logger.debug("无符合条件的订单")
            for order in orders:
                oid = order.get("order_id")
                ccode = order.get("center_code")
                if ccode not in DEU_TARGET_CENTERS:
                    continue
                if not _acquire_cancel_lock(oid):
                    continue
                _cancel_waitlist_for_order(order)
                # 锁通过 TTL 自动过期
            time.sleep(SCAN_INTERVAL_SECONDS)
        except Exception as e:
            logger.error(f"主循环异常: {e}")
            time.sleep(10)


if __name__ == "__main__":
    worker_loop()
