#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试VFS日历扫描器
"""
import sys
import os
import json
import time
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_vfs_scanner():
    """测试VFS日历扫描器的各个组件"""
    print("🧪 VFS日历扫描器测试")
    print("=" * 50)

    try:
        # 导入必要的模块
        from vfs_calendar_scanner import (
            get_vfs_customers_with_urns,
            get_date_ranges_for_customer,
            get_all_date_ranges_from_db,
            get_login_user_for_country,
            get_available_token_for_country,
            get_cached_urn,
            get_cached_token,
            gen_proxy
        )

        print("✅ 模块导入成功")

        # 1. 测试获取VFS客户
        print("\n🔍 测试获取VFS客户...")
        customers = get_vfs_customers_with_urns()
        print(f"📊 找到 {len(customers)} 个有URN的VFS客户")

        if customers:
            for i, customer in enumerate(customers[:3]):
                print(f"  👤 客户 {i+1}: {customer['order_id']} | "
                      f"国家: {customer['mission_code']} | "
                      f"中心: {customer['center_code']} | "
                      f"URN剩余: {customer.get('urn_remaining_time', 0)}秒")

        # 2. 测试获取日期范围
        print("\n📅 测试获取日期范围...")
        all_date_ranges = get_all_date_ranges_from_db()
        print(f"📊 找到 {len(all_date_ranges)} 个日期范围")

        if all_date_ranges:
            for i, dr in enumerate(all_date_ranges[:3]):
                print(f"  📋 范围 {i+1}: {dr['order_id']} | "
                      f"{dr['start_date']} ~ {dr['end_date']}")

        # 2.1 测试客户专属日期范围
        if customers:
            print("\n📅 测试客户专属日期范围...")
            test_customer = customers[0]
            customer_ranges = get_date_ranges_for_customer(test_customer)
            print(f"📊 客户 {test_customer['order_id']} 的日期范围: {len(customer_ranges)} 个")

            if customer_ranges:
                for i, dr in enumerate(customer_ranges[:2]):
                    print(f"  📋 范围 {i+1}: {dr['order_id']} | "
                          f"{dr['start_date']} ~ {dr['end_date']}")

        # 3. 测试获取登录用户和Token
        if customers:
            print("\n👥 测试获取登录用户和Token...")
            test_countries = list(set(c['mission_code'] for c in customers))

            for country in test_countries[:3]:
                print(f"\n  🌍 测试国家: {country}")

                # 测试直接获取可用Token
                token_data = get_available_token_for_country(country)
                if token_data:
                    print(f"    ✅ 可用Token: {token_data.get('email', 'Unknown')}")
                    print(f"    🔑 Token: {token_data.get('token', 'None')[:20]}...")
                    print(f"    🎫 URN: {token_data.get('urn', 'None')[:20]}...")

                    remaining_time = 6000 - (int(time.time()) - token_data.get('updateTokenTime', 0))
                    print(f"    ⏰ 剩余时间: {remaining_time}秒")
                else:
                    print(f"    ❌ {country}: 无可用Token")

                # 测试获取登录用户（应该返回相同结果）
                user = get_login_user_for_country(country)
                if user:
                    print(f"    ✅ 登录用户验证: {user.get('email', 'Unknown')}")
                else:
                    print(f"    ❌ 登录用户验证失败")

        # 4. 测试URN缓存
        if customers:
            print("\n🔑 测试URN缓存...")
            for customer in customers[:3]:
                urn_data = get_cached_urn(customer['order_id'])
                if urn_data:
                    remaining = 10000 - (int(datetime.now().timestamp()) - urn_data.get('createTime', 0))
                    print(f"  ✅ {customer['order_id']}: URN有效 | 剩余: {remaining}秒")
                else:
                    print(f"  ❌ {customer['order_id']}: URN无效或过期")

        # 5. 测试代理生成
        print("\n🌐 测试代理生成...")
        proxy = gen_proxy()
        if proxy:
            print(f"  ✅ 代理: {proxy[:50]}...")
        else:
            print("  ❌ 无可用代理")

        # 6. 测试完整扫描流程（模拟）
        if customers and all_date_ranges:
            print("\n🎯 测试完整扫描流程（模拟）...")

            # 选择一个客户
            test_customer = customers[0]
            print(f"  📋 选择客户: {test_customer['order_id']}")

            # 获取客户的日期范围
            customer_ranges = get_date_ranges_for_customer(test_customer)
            if not customer_ranges:
                print("  ❌ 客户没有可用的日期范围")
                return False

            test_range = customer_ranges[0]
            print(f"  📅 选择日期范围: {test_range['start_date']} ~ {test_range['end_date']}")

            # 获取登录用户
            test_user = get_login_user_for_country(test_customer['mission_code'])
            if test_user:
                print(f"  👤 选择用户: {test_user.get('email', 'Unknown')}")
            else:
                print("  ❌ 无可用用户，无法进行扫描测试")
                return False

            # 获取代理
            test_proxy = gen_proxy()
            print(f"  🌐 选择代理: {test_proxy[:30] + '...' if test_proxy else 'None'}")

            print("  ✅ 扫描流程准备完成")
            print("  💡 实际扫描需要运行 vfs_calendar_scanner.py")

        print("\n" + "=" * 50)
        print("🎉 所有测试完成！")

        # 总结
        summary = {
            "VFS客户": len(customers),
            "日期范围": len(all_date_ranges),
            "代理可用": bool(proxy)
        }

        print("📊 测试总结:")
        for key, value in summary.items():
            print(f"  {key}: {value}")

        if customers and all_date_ranges and proxy:
            print("\n🚀 系统准备就绪，可以运行:")
            print("   python vfs_calendar_scanner.py")
            return True
        else:
            print("\n⚠️ 系统未完全准备就绪，请检查:")
            if not customers:
                print("   - VFS客户数据")
            if not all_date_ranges:
                print("   - 日期范围数据")
            if not proxy:
                print("   - 代理配置")
            return False

    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保所有依赖模块都已正确安装")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False


def test_single_scan():
    """测试单次扫描"""
    print("\n🔬 单次扫描测试")
    print("-" * 30)

    try:
        from vfs_calendar_scanner import (
            get_vfs_customers_with_urns,
            get_date_ranges_for_customer,
            get_login_user_for_country,
            get_available_token_for_country,
            scan_calendar_for_customer,
            request_time_slots_for_date,
            gen_proxy
        )

        # 获取测试数据
        customers = get_vfs_customers_with_urns()

        if not customers:
            print("❌ 缺少VFS客户数据")
            return False

        # 选择测试目标
        test_customer = customers[0]

        # 获取客户的日期范围
        date_ranges = get_date_ranges_for_customer(test_customer)
        if not date_ranges:
            print(f"❌ 客户 {test_customer['order_id']} 没有日期范围数据")
            return False

        test_range = date_ranges[0]

        # 获取登录用户
        test_user = get_login_user_for_country(test_customer['mission_code'])
        if not test_user:
            print(f"❌ 国家 {test_customer['mission_code']} 无可用登录用户")
            return False

        # 获取代理
        test_proxy = gen_proxy()

        print(f"🎯 开始扫描测试:")
        print(f"  订单: {test_customer['order_id']}")
        print(f"  国家: {test_customer['mission_code']}")
        print(f"  日期: {test_range['start_date']} ~ {test_range['end_date']}")
        print(f"  用户: {test_user.get('email', 'Unknown')}")

        # 执行扫描
        scan_result = scan_calendar_for_customer(test_customer, test_range, test_user, test_proxy)

        if scan_result and len(scan_result) == 2:
            available_dates, r_auth = scan_result
            if available_dates:
                print(f"✅ 扫描成功！找到 {len(available_dates)} 个可用日期")
                for i, date in enumerate(available_dates[:3]):
                    print(f"  📅 日期 {i+1}: {date}")
                if len(available_dates) > 3:
                    print(f"  📅 ... 还有 {len(available_dates) - 3} 个日期")

                # 测试时间段请求
                if available_dates and r_auth:
                    print(f"\n🔍 测试时间段请求...")
                    selected_date = available_dates[0]
                    time_slots = request_time_slots_for_date(test_customer, selected_date, test_user, test_proxy, r_auth)
                    if time_slots:
                        print(f"✅ 时间段请求成功！找到 {len(time_slots)} 个时间段")
                    else:
                        print("❌ 时间段请求失败")
            else:
                print("❌ 扫描完成，但未找到可用日期")
        else:
            print("❌ 扫描失败")

        return True

    except Exception as e:
        print(f"❌ 单次扫描测试失败: {e}")
        return False


def main():
    """主函数"""
    success = test_vfs_scanner()

    if success:
        # 询问是否进行单次扫描测试
        try:
            response = input("\n🤔 是否进行单次扫描测试？(y/N): ").strip().lower()
            if response in ['y', 'yes']:
                test_single_scan()
        except KeyboardInterrupt:
            print("\n👋 测试结束")

    return success


if __name__ == "__main__":
    main()
