# -*- coding: utf-8 -*-
"""
VFS集成日历扫描器
功能：从数据库获取日期范围，扫描可用日期，随机选择进行时间段请求
"""
from curl_cffi import requests
import time
import json
import threading
import random
import string
import base64
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
import logging
import psycopg2
from psycopg2.extras import RealDictCursor

# 导入现有模块
from RedisClient import RedisClient
from db_utils import get_db_connection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(threadName)-15s | %(funcName)-20s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 初始化Redis客户端
redis_client = RedisClient()

# 全局变量
scan_data = {}
scan_countries = []
rsa_string = ""
delegate = []


def get_current_timestamp():
    """获取当前时间戳"""
    now = datetime.now()
    return now.strftime("%Y-%m-%dT%H:%M:%S")


def format_rsa_string(compact_key: str) -> str:
    """格式化RSA字符串"""
    if not compact_key:
        return ""
    base64_content = compact_key.replace("|", "\n")
    return f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"


def encryption(text):
    """RSA加密"""
    try:
        if not rsa_string:
            return ""
        public_key = serialization.load_pem_public_key(rsa_string.encode())
        encrypted = public_key.encrypt(text.encode(), padding.PKCS1v15())
        return base64.b64encode(encrypted).decode()
    except Exception as e:
        logger.error(f"RSA加密失败: {e}")
        return ""


def generate_random_string(length=31):
    """生成随机字符串"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))


def convert_date_to_calendar_format(date_str):
    """转换日期格式 YYYY-MM-DD -> DD/MM/YYYY"""
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        return date_obj.strftime("%d/%m/%Y")
    except Exception as e:
        logger.error(f"日期格式转换失败: {date_str}, 错误: {e}")
        return date_str


def get_today_if_past(date_str):
    """如果日期小于今天，则返回今天的日期"""
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        today = datetime.now().date()

        if date_obj.date() < today:
            return today.strftime("%Y-%m-%d")
        return date_str
    except Exception as e:
        logger.error(f"日期比较失败: {date_str}, 错误: {e}")
        return date_str


def get_date_ranges_from_db():
    """从数据库获取日期范围"""
    try:
        logger.debug("🔗 尝试连接数据库...")
        conn = get_db_connection()
        if not conn:
            logger.error("❌ 无法获取数据库连接")
            return []

        logger.debug("✅ 数据库连接成功")
        date_ranges = []

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 首先检查表是否存在
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = 'date_ranges'
                )
            """)
            table_exists = cursor.fetchone()[0]

            if not table_exists:
                logger.error("❌ 表 'date_ranges' 不存在")
                conn.close()
                return []

            logger.debug("✅ 表 'date_ranges' 存在")

            query = """
                SELECT id, order_id, start_date, end_date
                FROM date_ranges
                ORDER BY start_date
            """
            cursor.execute(query)
            ranges = cursor.fetchall()

            logger.info(f"📊 从数据库查询到 {len(ranges)} 条原始记录")

            for range_data in ranges:
                # 处理日期，如果小于今天则使用今天
                start_date = get_today_if_past(str(range_data['start_date']))
                end_date = str(range_data['end_date'])

                date_ranges.append({
                    'id': range_data['id'],
                    'order_id': range_data['order_id'],
                    'start_date': start_date,
                    'end_date': end_date,
                    'start_date_calendar': convert_date_to_calendar_format(start_date),
                    'end_date_calendar': convert_date_to_calendar_format(end_date)
                })

                logger.debug(f"📅 处理日期范围 | 订单: {range_data['order_id']} | "
                             f"原始: {range_data['start_date']} ~ {range_data['end_date']} | "
                             f"处理后: {start_date} ~ {end_date}")

        conn.close()
        logger.info(f"✅ 从数据库获取到 {len(date_ranges)} 个有效日期范围")

        # 显示前3个日期范围作为示例
        for i, dr in enumerate(date_ranges[:3]):
            logger.info(f"  📋 示例 {i+1}: 订单 {dr['order_id']} | {dr['start_date']} ~ {dr['end_date']}")

        if len(date_ranges) > 3:
            logger.info(f"  📋 ... 还有 {len(date_ranges) - 3} 个日期范围")

        return date_ranges

    except Exception as e:
        logger.error(f"❌ 获取数据库日期范围失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return []


def refresh_global_config():
    """刷新全局配置"""
    global scan_data, scan_countries, rsa_string, delegate

    first_run = True

    while True:
        try:
            if not first_run:
                time.sleep(10)
            first_run = False

            # 刷新RSA密钥
            source_rsa_str = redis_client.get('rsa_str')
            if source_rsa_str:
                rsa_string = format_rsa_string(source_rsa_str)
                logger.info(f"✅ RSA密钥已加载 | 长度: {len(rsa_string)} 字符")
            else:
                logger.warning("⚠️ RSA密钥未找到")

            # 刷新扫描数据
            scan_data_str = redis_client.get("scanData")
            if scan_data_str:
                scan_data = json.loads(scan_data_str)
                # 排除德国和意大利
                scan_countries = [country for country in scan_data.keys()
                                  if country not in ['deu', 'ita']]
                logger.info(f"✅ 扫描数据已加载 | 总国家: {len(scan_data)} | 可用国家: {len(scan_countries)}")
                logger.info(f"📍 可用国家列表: {scan_countries}")
            else:
                logger.warning("⚠️ 扫描数据(scanData)未找到")

            # 刷新代理列表
            delegate_str = redis_client.get("login_proxy")
            if delegate_str:
                delegate = json.loads(delegate_str)
                logger.info(f"✅ 代理列表已加载 | 代理数: {len(delegate)}")
            else:
                logger.warning("⚠️ 代理列表(login_proxy)未找到")

            logger.info(f"🔄 配置刷新完成 | 国家数: {len(scan_countries)} | 代理数: {len(delegate)} | RSA: {bool(rsa_string)}")

        except Exception as e:
            logger.error(f"❌ 刷新全局配置失败: {e}")


def scan_calendar_dates(scan_area, login_user, proxy, date_range):
    """扫描日历可用日期"""
    try:
        formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"📅 开始日历扫描 | {formatted_time} | 订单: {date_range['order_id']} | "
                    f"国家: {scan_area.get('missionCode')} | 中心: {scan_area.get('centerCode')} | "
                    f"签证类型: {scan_area.get('visaCategoryCode')} | "
                    f"日期范围: {date_range['start_date_calendar']} ~ {date_range['end_date_calendar']} | "
                    f"用户: {login_user.get('email', 'Unknown')[:20]}... | "
                    f"代理: {proxy[:30] + '...' if proxy else 'None'}")

        # 生成随机授权头
        r_auth = f"EAAAAN{generate_random_string(597)}="

        url = "https://lift-apicn.vfsglobal.com/appointment/slots"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": login_user.get("token"),
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{scan_area.get('missionCode')}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": login_user.get('ltsn', '')
        }

        data = {
            "countryCode": "chn",
            "missionCode": scan_area.get("missionCode"),
            "centerCode": scan_area.get("centerCode"),
            "visaCategoryCode": scan_area.get("visaCategoryCode"),
            "loginUser": login_user.get("email"),
            "calendarId": scan_area.get("calendarId"),
            "fromDate": date_range['start_date_calendar'],  # DD/MM/YYYY格式
            "toDate": date_range['end_date_calendar'],      # DD/MM/YYYY格式
            "cultureCode": "zh-CN"
        }

        proxies = {"http": proxy, "https": proxy} if proxy else None

        logger.debug(f"📡 发送日历API请求 | 订单: {date_range['order_id']} | "
                     f"URL: {url} | 请求数据: {json.dumps(data, ensure_ascii=False)}")

        start_time = time.time()
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
            timeout=30
        )
        end_time = time.time()
        request_duration = round((end_time - start_time) * 1000, 2)  # 毫秒

        formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"📡 日历API响应 | 订单: {date_range['order_id']} | "
                    f"状态码: {response.status_code} | 耗时: {request_duration}ms | "
                    f"响应大小: {len(response.content)}字节")

        if response.status_code == 200:
            result = response.json()

            logger.debug(f"📋 日历API完整响应 | 订单: {date_range['order_id']} | "
                         f"响应: {json.dumps(result, ensure_ascii=False)}")

            if isinstance(result, dict) and result.get('calendars') and len(result.get('calendars')) > 0:
                # 提取可用日期
                calendars = result.get('calendars')
                available_dates = list(dict.fromkeys([item.get('date') for item in calendars]))

                # 统计每个日期的可用数量
                date_stats = {}
                for calendar_item in calendars:
                    date = calendar_item.get('date')
                    if date not in date_stats:
                        date_stats[date] = 0
                    date_stats[date] += 1

                logger.info(f"✅ 日历扫描成功 | {formatted_time} | 订单: {date_range['order_id']} | "
                            f"日期范围: {date_range['start_date']} ~ {date_range['end_date']} | "
                            f"国家: {scan_area['missionCode']} | 领区: {scan_area.get('centerName', 'Unknown')} | "
                            f"签证类型: {scan_area.get('visaCategoryName', scan_area.get('visaCategoryCode'))} | "
                            f"可用日期: {len(available_dates)}个 | 总日历项: {len(calendars)}个")

                # 详细显示前5个可用日期
                for i, date in enumerate(available_dates[:5]):
                    count = date_stats.get(date, 0)
                    logger.info(f"  📅 可用日期 {i+1}: {date} | 可用项数: {count}")

                if len(available_dates) > 5:
                    logger.info(f"  📅 ... 还有 {len(available_dates) - 5} 个可用日期")

                return available_dates
            else:
                # 分析无可用日期的原因
                error_info = ""
                if result.get('error'):
                    error_info = f" | 错误: {result.get('error')}"
                elif result.get('message'):
                    error_info = f" | 消息: {result.get('message')}"

                logger.info(f"❌ 日历无可用日期 | {formatted_time} | 订单: {date_range['order_id']} | "
                            f"日期范围: {date_range['start_date']} ~ {date_range['end_date']} | "
                            f"国家: {scan_area['missionCode']} | 领区: {scan_area.get('centerName', 'Unknown')}{error_info}")
                return []
        else:
            # 记录HTTP错误详情
            try:
                error_response = response.json()
                error_detail = f" | 错误详情: {json.dumps(error_response, ensure_ascii=False)}"
            except:
                error_detail = f" | 响应内容: {response.text[:200]}..."

            logger.warning(f"⚠️ 日历扫描失败 | {formatted_time} | 订单: {date_range['order_id']} | "
                           f"状态码: {response.status_code} | 国家: {scan_area['missionCode']}{error_detail}")
            return []

    except Exception as e:
        logger.error(f"日历扫描异常 | 订单: {date_range.get('order_id', 'Unknown')} | "
                     f"扫描区域: {scan_area.get('missionCode', 'Unknown')} | 错误: {e}")
        return []


def request_time_slots(scan_area, login_user, proxy, selected_date, date_range):
    """请求指定日期的时间段"""
    try:
        formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"⏰ 开始时间段请求 | {formatted_time} | 订单: {date_range['order_id']} | "
                    f"选中日期: {selected_date} | 国家: {scan_area.get('missionCode')} | "
                    f"中心: {scan_area.get('centerCode')} | 签证类型: {scan_area.get('visaCategoryCode')} | "
                    f"用户: {login_user.get('email', 'Unknown')[:20]}... | "
                    f"代理: {proxy[:30] + '...' if proxy else 'None'}")

        # 生成随机授权头
        r_auth = f"EAAAAN{generate_random_string(597)}="

        url = "https://lift-apicn.vfsglobal.com/appointment/slots"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": login_user.get("token"),
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{scan_area.get('missionCode')}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": login_user.get('ltsn', '')
        }

        # 转换选中日期为DD/MM/YYYY格式
        selected_date_calendar = convert_date_to_calendar_format(selected_date)

        data = {
            "countryCode": "chn",
            "missionCode": scan_area.get("missionCode"),
            "centerCode": scan_area.get("centerCode"),
            "visaCategoryCode": scan_area.get("visaCategoryCode"),
            "loginUser": login_user.get("email"),
            "calendarId": scan_area.get("calendarId"),
            "appointmentDate": selected_date_calendar,  # 指定日期
            "cultureCode": "zh-CN"
        }

        proxies = {"http": proxy, "https": proxy} if proxy else None

        logger.debug(f"📡 发送时间段API请求 | 订单: {date_range['order_id']} | "
                     f"URL: {url} | 请求数据: {json.dumps(data, ensure_ascii=False)}")

        start_time = time.time()
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
            timeout=30
        )
        end_time = time.time()
        request_duration = round((end_time - start_time) * 1000, 2)  # 毫秒

        formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"📡 时间段API响应 | 订单: {date_range['order_id']} | "
                    f"状态码: {response.status_code} | 耗时: {request_duration}ms | "
                    f"响应大小: {len(response.content)}字节")

        if response.status_code == 200:
            result = response.json()

            logger.debug(f"📋 时间段API完整响应 | 订单: {date_range['order_id']} | "
                         f"响应: {json.dumps(result, ensure_ascii=False)}")

            if isinstance(result, dict) and result.get('timeSlots') and len(result.get('timeSlots')) > 0:
                time_slots = result.get('timeSlots')

                # 统计总可用数量
                total_available = sum(slot.get('availableSlots', 0) for slot in time_slots)

                logger.info(f"✅ 时间段获取成功 | {formatted_time} | 订单: {date_range['order_id']} | "
                            f"日期: {selected_date} | 国家: {scan_area['missionCode']} | "
                            f"领区: {scan_area.get('centerName', 'Unknown')} | "
                            f"可用时间段: {len(time_slots)}个 | 总可用数量: {total_available}")

                # 显示时间段详情
                for i, slot in enumerate(time_slots):
                    start_time = slot.get('startTime', 'Unknown')
                    end_time = slot.get('endTime', 'Unknown')
                    available_slots = slot.get('availableSlots', 0)
                    slot_id = slot.get('slotId', 'Unknown')

                    logger.info(f"  ⏰ 时间段 {i+1}: {start_time} - {end_time} | "
                                f"可用数量: {available_slots} | SlotID: {slot_id}")

                return time_slots
            else:
                # 分析无时间段的原因
                error_info = ""
                if result.get('error'):
                    error_info = f" | 错误: {result.get('error')}"
                elif result.get('message'):
                    error_info = f" | 消息: {result.get('message')}"

                logger.info(f"❌ 时间段无可用 | {formatted_time} | 订单: {date_range['order_id']} | "
                            f"日期: {selected_date} | 国家: {scan_area['missionCode']}{error_info}")
                return []
        else:
            # 记录HTTP错误详情
            try:
                error_response = response.json()
                error_detail = f" | 错误详情: {json.dumps(error_response, ensure_ascii=False)}"
            except:
                error_detail = f" | 响应内容: {response.text[:200]}..."

            logger.warning(f"⚠️ 时间段请求失败 | {formatted_time} | 订单: {date_range['order_id']} | "
                           f"日期: {selected_date} | 状态码: {response.status_code}{error_detail}")
            return []

    except Exception as e:
        logger.error(f"时间段请求异常 | 订单: {date_range.get('order_id', 'Unknown')} | "
                     f"日期: {selected_date} | 错误: {e}")
        return []


def integrated_scan_worker():
    """集成扫描工作器"""
    logger.info("集成扫描工作器启动")

    while True:
        try:
            if not scan_countries or not scan_data:
                logger.info(f"⏳ 等待配置数据加载... | 国家数: {len(scan_countries)} | 扫描数据: {bool(scan_data)}")
                time.sleep(5)
                continue

            # 从数据库获取日期范围
            date_ranges = get_date_ranges_from_db()
            if not date_ranges:
                logger.info("❌ 没有找到日期范围数据，等待30秒...")
                time.sleep(30)
                continue
            print(f"从数据库获取到 {len(date_ranges)} 个日期范围")

            # 随机选择一个日期范围
            date_range = random.choice(date_ranges)
            logger.info(f"🎯 选择扫描目标 | 订单: {date_range['order_id']} | "
                        f"日期范围: {date_range['start_date']} ~ {date_range['end_date']} | "
                        f"总日期范围数: {len(date_ranges)}")

            # 随机选择一个国家
            country = random.choice(scan_countries)
            country_data = scan_data.get(country, {})

            if not country_data:
                logger.debug(f"国家 {country} 无配置数据，跳过")
                time.sleep(1)
                continue

            # 随机选择一个扫描区域
            scan_areas = country_data.get('scanAreas', [])
            if not scan_areas:
                logger.debug(f"国家 {country} 无扫描区域，跳过")
                time.sleep(1)
                continue

            scan_area = random.choice(scan_areas)
            logger.info(f"🌍 选择扫描区域 | 国家: {country} | "
                        f"中心: {scan_area.get('centerCode')} | "
                        f"签证类型: {scan_area.get('visaCategoryCode')} | "
                        f"可选区域数: {len(scan_areas)}")

            # 获取登录用户
            login_users = redis_client.hgetall(f"{country}LoginUser")
            if not login_users:
                logger.debug(f"国家 {country} 没有登录用户")
                time.sleep(5)
                continue

            # 随机选择一个登录用户
            login_user_email = random.choice(list(login_users.keys()))
            login_user = json.loads(login_users[login_user_email])

            # 选择代理
            proxy = random.choice(delegate) if delegate else None

            # 1. 扫描日历可用日期
            scan_start_time = time.time()
            available_dates = scan_calendar_dates(scan_area, login_user, proxy, date_range)
            scan_duration = round((time.time() - scan_start_time) * 1000, 2)

            if available_dates:
                # 2. 随机选择一个可用日期
                selected_date = random.choice(available_dates)
                logger.info(f"🎲 随机选择日期 | 订单: {date_range['order_id']} | "
                            f"选择日期: {selected_date} | 可选日期数: {len(available_dates)} | "
                            f"日历扫描耗时: {scan_duration}ms")

                # 3. 请求该日期的时间段
                timeslot_start_time = time.time()
                time_slots = request_time_slots(scan_area, login_user, proxy, selected_date, date_range)
                timeslot_duration = round((time.time() - timeslot_start_time) * 1000, 2)

                if time_slots:
                    total_slots = sum(slot.get('availableSlots', 0) for slot in time_slots)
                    logger.info(f"🎉 完整扫描成功 | 订单: {date_range['order_id']} | "
                                f"日期: {selected_date} | 时间段: {len(time_slots)}个 | "
                                f"总可用数量: {total_slots} | 时间段扫描耗时: {timeslot_duration}ms | "
                                f"总耗时: {scan_duration + timeslot_duration}ms")
                else:
                    logger.info(f"⚠️ 日期有效但无时间段 | 订单: {date_range['order_id']} | "
                                f"日期: {selected_date} | 时间段扫描耗时: {timeslot_duration}ms")
            else:
                logger.info(f"❌ 日期范围内无可用日期 | 订单: {date_range['order_id']} | "
                            f"范围: {date_range['start_date']} ~ {date_range['end_date']} | "
                            f"日历扫描耗时: {scan_duration}ms")

            # 短暂休息
            rest_time = random.uniform(2, 5)
            logger.debug(f"💤 扫描完成，休息 {rest_time:.1f} 秒")
            time.sleep(rest_time)

        except Exception as e:
            logger.error(f"集成扫描工作器异常: {e}")
            time.sleep(5)


def show_scan_statistics():
    """显示扫描统计信息"""
    while True:
        try:
            time.sleep(300)  # 每5分钟显示一次统计

            # 获取日期范围统计
            date_ranges = get_date_ranges_from_db()

            logger.info("=" * 80)
            logger.info("VFS集成扫描器统计报告")
            logger.info("=" * 80)
            logger.info(f"数据库日期范围: {len(date_ranges)}个")
            logger.info(f"扫描国家数量: {len(scan_countries)}")
            logger.info(f"可用代理数量: {len(delegate)}")

            # 按订单分组统计
            order_stats = {}
            for dr in date_ranges:
                order_id = dr['order_id']
                if order_id not in order_stats:
                    order_stats[order_id] = []
                order_stats[order_id].append(dr)

            logger.info(f"涉及订单数量: {len(order_stats)}")

            # 显示前5个订单的日期范围
            for order_id, ranges in list(order_stats.items())[:5]:
                logger.info(f"  📋 订单 {order_id}: {len(ranges)}个日期范围")
                for r in ranges[:2]:  # 只显示前2个范围
                    logger.info(f"    📅 {r['start_date']} ~ {r['end_date']}")
                if len(ranges) > 2:
                    logger.info(f"    📅 ... 还有{len(ranges)-2}个范围")

            if len(order_stats) > 5:
                logger.info(f"  ... 还有{len(order_stats)-5}个订单")

            logger.info("=" * 80)

        except Exception as e:
            logger.error(f"统计信息显示异常: {e}")


def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("VFS集成日历扫描器启动")
    logger.info("功能: 从数据库获取日期范围，扫描日历，请求时间段")
    logger.info("版本: v1.0")
    logger.info("=" * 60)

    try:
        # 启动配置刷新线程
        config_thread = threading.Thread(target=refresh_global_config, name="ConfigRefresh")
        config_thread.daemon = True
        config_thread.start()
        logger.info("配置刷新线程已启动")

        # 启动统计显示线程
        stats_thread = threading.Thread(target=show_scan_statistics, name="Statistics")
        stats_thread.daemon = True
        stats_thread.start()
        logger.info("统计显示线程已启动")

        # 等待配置加载
        time.sleep(5)

        # 启动多个集成扫描工作线程
        worker_count = 6  # 可以根据需要调整
        for i in range(worker_count):
            worker_thread = threading.Thread(target=integrated_scan_worker, name=f"IntegratedScanWorker-{i+1}")
            worker_thread.daemon = True
            worker_thread.start()

        logger.info(f"已启动 {worker_count} 个集成扫描工作线程")
        logger.info("系统进入运行状态，按 Ctrl+C 停止服务")

        # 主线程保持运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭服务...")
        logger.info("VFS集成日历扫描器已停止")
    except Exception as e:
        logger.error(f"主线程异常: {e}")
        logger.error("VFS集成日历扫描器异常退出")


if __name__ == "__main__":
    main()
