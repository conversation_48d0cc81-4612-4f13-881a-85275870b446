# -*- coding: utf-8 -*-
"""
VFS订单账号管理器 - 为每个VFS订单单独创建专属账号
验证码方案：本地 CF Turnstile 服务（保留现有逻辑）
"""
from curl_cffi import requests
import time
import json
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys
from RedisClient import RedisClient
import random
from datetime import datetime, timedelta
# from queue import Queue  # 不再使用队列，改为直接启动线程
import urllib.parse

import re
import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from pypinyin import pinyin, Style
import logging
from logging.handlers import RotatingFileHandler
import os
import psycopg2
from psycopg2.extras import RealDictCursor
from db_utils import update_order_by_email_activated, update_order_account
from order_urn_manager import get_orders_need_urn, create_urn_for_order

# 日志配置


def setup_logger():
    """配置专业的日志系统"""
    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 创建logger
    logger = logging.getLogger('VFS_AccountManager')
    logger.setLevel(logging.INFO)

    # 避免重复添加handler
    if logger.handlers:
        return logger

    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(threadName)-15s | %(funcName)-20s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 文件handler - 轮转日志
    file_handler = RotatingFileHandler(
        os.path.join(log_dir, 'vfs_account_manager.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # 控制台handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 添加handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    # 防止日志向root冒泡导致重复输出
    logger.propagate = False

    return logger


# 初始化日志和配置
logger = setup_logger()
redis_client = RedisClient()

# 数据库配置
DATABASE = {
    "host": "localhost",
    "port": 5432,
    "dbname": "user_db",
    "user": "qwyvisa",
    "password": "HAfhqh1fn0fbua8vb7v!aa"
}


def get_db_connection():
    """获取数据库连接"""
    try:
        conn = psycopg2.connect(**DATABASE)
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败 | 异常: {str(e)}")
        return None


# 轮询周期配置常量（秒）
REGISTRATION_POLL_INTERVAL = 10
TOKEN_REFRESH_POLL_INTERVAL = 10
STATISTICS_POLL_INTERVAL = 10


# 全局锁管理 - 按账号+国家锁定
refresh_locks = {}  # 格式: {email_country: lock_object}
refresh_lock_mutex = threading.Lock()  # 保护refresh_locks的互斥锁

# 全局锁管理
phone_locks = {}  # 手机号锁定字典
phone_lock_mutex = threading.Lock()  # 保护phone_locks的互斥锁
refresh_in_progress = {}  # 正在刷新的账号
refresh_lock_mutex = threading.Lock()  # 保护refresh_in_progress的互斥锁

# 中文姓名库
first_names = [
    "伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟",
    "涛", "明", "超", "秀英", "平", "刚", "桂英", "鑫", "涛", "建国", "志强", "敏", "小红",
    "婷婷", "玉兰", "梅", "明华", "英杰", "俊杰", "嘉豪", "嘉怡", "子豪", "子轩", "子涵",
    "宇轩", "宇航", "宇翔", "子琪", "雅静", "雅婷", "雅涵", "文婷", "文静", "文豪", "文轩"
]

last_names = [
    "赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈",
    "褚", "卫", "蒋", "沈", "韩", "杨", "朱", "秦", "尤", "许",
    "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏",
    "陶", "姜", "戚", "谢", "邹", "喻", "柏", "水", "窦", "章"
]


def get_refresh_lock(email, country):
    """获取账号+国家的刷新锁"""
    lock_key = f"{email}_{country}"

    with refresh_lock_mutex:
        if lock_key not in refresh_locks:
            refresh_locks[lock_key] = threading.Lock()
        return refresh_locks[lock_key]


def is_refresh_in_progress(email, country):
    """检查账号+国家是否正在刷新中"""
    lock_key = f"{email}_{country}"

    with refresh_lock_mutex:
        if lock_key in refresh_locks:
            # 尝试获取锁，如果获取不到说明正在刷新
            lock = refresh_locks[lock_key]
            acquired = lock.acquire(blocking=False)
            if acquired:
                lock.release()
                return False
            else:
                return True
        return False


def get_current_timestamp():
    """获取当前时间戳"""
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    """格式化RSA密钥"""
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


def encryption(t):
    """加密函数"""
    source_rsa_str = redis_client.get('rsa_str')
    rsa_string = format_rsa_string(source_rsa_str)
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


def get_delegate():
    """获取代理配置"""
    try:
        delegate = json.loads(redis_client.get("login_proxy"))
        return delegate
    except:
        return []


def to_pinyin(hanzi):
    """将汉字转换为拼音"""
    return ''.join([item[0] for item in pinyin(hanzi, style=Style.NORMAL)])


def generate_random_password():
    """生成随机密码"""
    import string
    # 密码包含大小写字母、数字和特殊字符
    uppercase = string.ascii_uppercase
    lowercase = string.ascii_lowercase
    digits = string.digits
    special_chars = "@#$%&*"

    # 确保密码包含各种字符类型
    password = [
        random.choice(uppercase),
        random.choice(lowercase),
        random.choice(digits),
        random.choice(special_chars)
    ]

    # 填充剩余长度
    all_chars = uppercase + lowercase + digits + special_chars
    for _ in range(8):  # 总长度12位
        password.append(random.choice(all_chars))

    # 打乱顺序
    random.shuffle(password)
    return ''.join(password)


def generate_email():
    """生成随机邮箱"""
    email_formats = []

    # 生成不同格式的邮箱
    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        email_formats.append(f"{first_name_pinyin}.{last_name_pinyin}")

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        email_formats.append(f"{last_name_pinyin}{first_name_pinyin}")

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        month = str(random.randint(1, 12)).zfill(2)
        day = str(random.randint(1, 28)).zfill(2)
        email_formats.append(f"{last_name_pinyin}{first_name_pinyin}{month}{day}")

    domains = ['nextdomain10.xyz']
    return f"{random.choice(email_formats)}@{random.choice(domains)}"


def get_phone_number():
    """获取手机号"""
    url = "http://api.haozhuma.com/sms/?api=getPhone&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False, timeout=60)
    if response.status_code == 200:
        data = response.json()
        if data.get("phone") != None:
            return data.get("phone")
    return None


def register_account(country, capResult):
    """注册账号"""
    emailid = generate_email()
    password = generate_random_password()  # 生成随机密码
    phone = get_phone_number()
    if phone == None:
        return False
    phone = str(phone)
    logger.info(f"手机号获取成功 | 号码: {phone}")

    # 取消接收
    cancel_url = f"http://api.haozhuma.com/sms/?api=cancelRecv&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    requests.get(cancel_url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False, timeout=60)

    try:
        url = "https://lift-apicn.vfsglobal.com/user/registration"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }
        data = {
            "emailid": emailid,
            "password": encryption(password),  # 使用随机密码
            "confirmPassword": encryption(password),  # 使用随机密码
            "instructionAgreed": True,
            "missioncode": country,
            "countrycode": "chn",
            "languageCode": "zh",
            "dialcode": "86",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "contact": phone,
            "cultureCode": "zh-CN",
            "intTransPerDataAgreed": True,
            "processPerDataAgreed": True,
            "termAndConditionAgreed": True,
            "IsSpecialUser": False
        }

        delegate = get_delegate()
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['firefox133', 'firefox135']),
            verify=False,
            timeout=60,
        )

        if response.status_code == 200:
            data = response.json()
            if "already registered with VFS" in response.text or (data.get("error") != None and data.get("error").get("code") == "414"):
                logger.warning(f"账号注册失败 | 邮箱: {emailid} | 原因: 邮箱已注册 | 错误: {data.get('error')}")
                return False
            elif data.get("error") == None:
                logger.info(f"账号注册成功 | 邮箱: {emailid} | 密码: {password} | 状态: 等待激活")
                # 存储到waitactive等待激活
                redis_client.hset(
                    "waitactive",
                    emailid,
                    json.dumps(
                        {
                            "email": emailid,
                            "password": password,  # 保存明文密码用于后续更新数据库
                            "token": "",
                            "phone": phone,
                            "missionCode": country,
                            "email_server": "54.177.132.64",
                            "email_type": "local_email",
                            "redis": "aws",
                            "redis_name": "vfs_accounts",
                            "countryCode": "chn",
                            "redis_key": emailid,
                            "get_otp_time": int(time.time()),
                        }
                    ),
                )
                return {"email": emailid, "phone": phone, "password": password}
            else:
                logger.error(f"账号注册失败 | 邮箱: {emailid} | 错误: {data.get('error')}")
                return False
        else:
            logger.error(f"账号注册失败 | 邮箱: {emailid} | HTTP状态码: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"账号注册异常 | 邮箱: {emailid} | 异常: {str(e)}")
        return False


def get_phone_status(phone):
    """获取手机号状态"""
    url = f"http://api.haozhuma.com/sms/?api=getPhone&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False, timeout=60)
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == "0":
            return True
        elif data.get("msg") == "指定专属码手机号不在线":
            return "remove_phone"
        else:
            return False
    return False


def get_sms_code(phone):
    """获取短信验证码"""
    url = f"http://api.haozhuma.com/sms/?api=getMessage&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False, timeout=60)
    if response.status_code == 200:
        data = response.json()
        if data.get("yzm"):
            return data.get("yzm")
    return ""


def get_cached_token(email, country):
    """从Redis获取缓存的Token"""
    try:
        cache_key = f"vfs_token:{email}:{country}"
        cached_data = redis_client.get(cache_key)
        if cached_data:
            token_data = json.loads(cached_data)
            # 检查是否过期（2000秒有效期）
            if int(time.time()) - token_data.get('updateTokenTime', 0) < 2000:
                logger.info(f"使用缓存Token | 邮箱: {email} | 剩余时间: {2000 - (int(time.time()) - token_data.get('updateTokenTime', 0))}秒")
                return token_data
            else:
                # Token过期，删除缓存
                redis_client.delete(cache_key)
                logger.info(f"缓存Token已过期 | 邮箱: {email}")
        return None
    except Exception as e:
        logger.error(f"获取缓存Token失败 | 邮箱: {email} | 异常: {str(e)}")
        return None


def cache_token(email, country, token_data):
    """将Token缓存到Redis"""
    try:
        cache_key = f"vfs_token:{email}:{country}"
        # 设置过期时间为2000秒
        redis_client.setex(cache_key, 2000, json.dumps(token_data))
        logger.info(f"Token已缓存 | 邮箱: {email} | 有效期: 2000秒")
        return True
    except Exception as e:
        logger.error(f"缓存Token失败 | 邮箱: {email} | 异常: {str(e)}")
        return False


def login_and_get_token(user, country, capResult=None):
    """登录并获取Token"""
    try:
        email = user.get('email')
        password = user.get('password', 'Kq123456@')  # 使用用户的密码，如果没有则使用默认密码

        # 先检查缓存的Token
        cached_token = get_cached_token(email, country)
        if cached_token:
            # 更新用户信息
            user.update(cached_token)
            return "Token"

        logger.info(f"开始登录 | 邮箱: {email} | 国家: {country}")
        url = "https://lift-apicn.vfsglobal.com/user/login"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "origin": "https://visa.vfsglobal.com",
            "referer": "http://visa.vfsglobal.com/",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{country}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }
        # 懒加载验证码，仅在需要发起登录请求时生成
        if capResult is None:
            cap_tmp = None
            for attempt in range(3):
                cap_tmp = gen_turnstile_token()
                if cap_tmp:
                    logger.info(f"验证码获取成功 | 邮箱: {email} | 尝试: {attempt + 1}/10 {cap_tmp}")
                    break
            if not cap_tmp:
                logger.error(f"验证码获取失败 | 邮箱: {email} |  已尝试3次")
                return False
            capResult = cap_tmp

        data = {
            "missioncode": country,
            "countrycode": "chn",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "username": email,
            "password": encryption(password),
        }

        delegate = get_delegate()
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        response = requests.post(
            url,
            data=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['firefox133', 'firefox135']),
            verify=False,
            timeout=60,
        )

        if response.status_code == 429:
            logger.warning(f"登录触发限流(429) | 邮箱: {email} | 国家: {country}")
            return "HTTP_429"
        if response.status_code == 200:
            res = response.json()
            if res.get("error") != None:
                logger.warning(f"登录失败 | 邮箱: {email} | 错误: {res.get('error')}")
                return False
            elif res.get("accessToken") != None:
                # 直接获取到token
                update_token_time = int(time.time())
                token_data = {
                    "email": email,
                    "token": res.get("accessToken"),
                    "updateTokenTime": update_token_time,
                    "phone": res.get("contactNumber"),
                    "missionCode": country,
                    "ltsn": response.headers.get('set-cookie').split(';')[0] if response.headers.get('set-cookie') else ""
                }

                # 更新用户信息
                user.update(token_data)

                # 缓存Token
                cache_token(email, country, token_data)

                logger.info(f"Token获取成功 | 邮箱: {email} | Token: {res['accessToken'][:20]}... | 手机: {res.get('contactNumber')}")
                return "Token"
            else:
                # 需要OTP验证
                user["phone"] = res.get("contactNumber")
                # 复用本次验证码 token，供后续 OTP 验证
                try:
                    user["capResult"] = capResult
                except Exception:
                    pass
                logger.info(f"需要OTP验证 | 邮箱: {email} | 手机: {res.get('contactNumber')}")
                return "SMS"
        else:
            logger.error(f"登录失败 | 邮箱: {email} | HTTP状态码: {response.status_code}")
        return False
    except Exception as e:
        logger.error(f"登录异常 | 邮箱: {user.get('email', 'unknown')} | 异常: {str(e)}")
        return False


def validate_otp(user, country, otp, capResult):
    """验证OTP获取Token"""
    try:
        email = user.get('email')
        password = user.get('password', 'Kq123456@')  # 使用用户的密码

        logger.info(f"开始OTP验证 | 邮箱: {email} | OTP: {otp}")
        url = "https://lift-apicn.vfsglobal.com/user/login"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "origin": "https://visa.vfsglobal.com",
            "referer": "http://visa.vfsglobal.com/",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{country}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }

        data = {
            "missioncode": country,
            "countrycode": "chn",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "otp": str(otp),
            "username": email,
            "password": encryption(password),
        }

        delegate = get_delegate()
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        response = requests.post(
            url,
            data=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['firefox133', 'firefox135']),
            verify=False,
            timeout=60,
        )

        if response.status_code == 429:
            logger.warning(f"OTP验证触发限流(429) | 邮箱: {email} | 国家: {country}")
            return "HTTP_429"
        if response.status_code == 200:
            response_data = response.json()
            if response_data["error"] is None:
                update_token_time = int(time.time())
                token_data = {
                    "email": email,
                    "token": response_data["accessToken"],
                    "updateTokenTime": update_token_time,
                    "phone": response_data.get("contactNumber"),
                    "missionCode": country,
                    "ltsn": response.headers.get('set-cookie').split(';')[0] if response.headers.get('set-cookie') else ""
                }

                # 更新用户信息
                user.update(token_data)

                # 缓存Token
                cache_token(email, country, token_data)

                logger.info(f"OTP验证成功 | 邮箱: {email} | Token: {response_data['accessToken'][:20]}...")
                return True
            else:
                logger.warning(f"OTP验证失败 | 邮箱: {email} | 错误: {response_data.get('error')}")
                return False
        else:
            logger.error(f"OTP验证失败 | 邮箱: {email} | HTTP状态码: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"OTP验证异常 | 邮箱: {user.get('email', 'unknown')} | 异常: {str(e)}")
        return False


def get_orders_need_accounts():
    """从PostgreSQL orders表中获取需要创建账号的订单列表"""
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("无法获取数据库连接")
            return []

        orders_need_accounts = []

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查询需要创建账号的VFS订单
            query = """
                SELECT order_id, mission_code, center_code, visa_type, visa_code
                FROM orders
                WHERE is_vfs_order = true
                AND mission_code IS NOT NULL
                AND mission_code != ''
                AND order_status = 'wait_registe'
                AND (vfs_account IS NULL OR vfs_account = '')
            """
            cursor.execute(query)
            orders = cursor.fetchall()

            for order in orders:
                # 排除不需要注册/登录的国家
                excluded_countries = {'spain', 'deu', 'ita'}  # 西班牙、德国
                if order['mission_code'].lower() not in excluded_countries:
                    orders_need_accounts.append(dict(order))
                    logger.debug(f"订单需要账号 | 订单: {order['order_id']} | 国家: {order['mission_code']}")

        conn.close()

        logger.info(f"订单账号需求分析完成 | 需要创建账号的订单数: {len(orders_need_accounts)}")
        return orders_need_accounts

    except Exception as e:
        logger.error(f"获取需要创建账号的订单列表失败 | 异常: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []


def get_orders_need_login():
    """从PostgreSQL orders表中获取需要登录获取Token的订单列表"""
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("无法获取数据库连接")
            return []

        orders_need_login = []

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查询已有账号但可能需要登录的VFS订单
            query = """
                SELECT order_id, mission_code, vfs_account, vfs_password
                FROM orders
                WHERE is_vfs_order = true
                AND mission_code IS NOT NULL
                AND mission_code != ''
                AND order_status in ('registe_success', 'waiting_for_payment', 'payed',  'wait_download_pdf')
                AND vfs_account IS NOT NULL
                AND vfs_account != ''
                AND vfs_password IS NOT NULL
                AND vfs_password != ''
            """
            cursor.execute(query)
            orders = cursor.fetchall()

            for order in orders:
                # 排除不需要登录的国家
                excluded_countries = {'spain', 'deu', 'ita'}
                if order['mission_code'].lower() not in excluded_countries:
                    # 检查Redis中是否有有效的Token
                    email = order['vfs_account']
                    country = order['mission_code']

                    cached_token = get_cached_token(email, country)
                    if not cached_token:
                        # 没有缓存的Token，需要登录
                        orders_need_login.append(dict(order))
                        logger.debug(f"订单需要登录 | 订单: {order['order_id']} | 邮箱: {email} | 国家: {country}")
                    else:
                        # 检查Token是否即将过期（剩余时间少于1800秒，即30分钟）
                        remaining_time = 2000 - (int(time.time()) - cached_token.get('updateTokenTime', 0))
                        if remaining_time < 1800:
                            orders_need_login.append(dict(order))
                            logger.debug(f"订单Token即将过期需要刷新 | 订单: {order['order_id']} | 邮箱: {email} | 剩余: {remaining_time}秒")

        conn.close()

        logger.info(f"订单登录需求分析完成 | 需要登录的订单数: {len(orders_need_login)}")
        return orders_need_login

    except Exception as e:
        logger.error(f"获取需要登录的订单列表失败 | 异常: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []


def refresh_expired_tokens():
    """刷新过期的Token"""
    try:
        # 查找即将过期的Token（剩余时间少于300秒）
        pattern = "vfs_token:*"
        token_keys = redis_client.keys(pattern)

        for key in token_keys:
            try:
                cached_data = redis_client.get(key)
                if cached_data:
                    token_data = json.loads(cached_data)
                    remaining_time = 2000 - (int(time.time()) - token_data.get('updateTokenTime', 0))

                    if remaining_time < 300:  # 剩余时间少于5分钟
                        email = token_data.get('email')
                        country = token_data.get('missionCode')

                        logger.info(f"Token即将过期，开始刷新 | 邮箱: {email} | 国家: {country} | 剩余: {remaining_time}秒")

                        # 从数据库获取密码
                        conn = get_db_connection()
                        if conn:
                            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                                query = """
                                    SELECT order_id, vfs_password
                                    FROM orders
                                    WHERE vfs_account = %s AND is_vfs_order = true
                                    LIMIT 1
                                """
                                cursor.execute(query, (email,))
                                result = cursor.fetchone()
                            conn.close()

                            if result:
                                user_data = {
                                    "email": email,
                                    "password": result['vfs_password'],
                                    "missionCode": country
                                }
                                order_id_for_refresh = result['order_id']
                                login_result = login_and_get_token(user_data, country)
                                if login_result == "HTTP_429":
                                    purge_account_for_order(order_id_for_refresh, email, country, context="token_refresh")
                                    continue
                                if login_result == "Token":
                                    logger.info(f"Token刷新成功 | 邮箱: {email}")
                                elif login_result == "SMS":
                                    logger.info(f"Token刷新需要短信验证 | 邮箱: {email} | 尝试自动获取OTP")
                                    sms_res = attempt_sms_login(user_data, country, user_data.get("capResult"))
                                    if sms_res is True:
                                        logger.info(f"Token刷新成功(OTP) | 邮箱: {email}")
                                    elif sms_res == "HTTP_429":
                                        purge_account_for_order(order_id_for_refresh, email, country, context="token_refresh")
                                        continue
                                    else:
                                        logger.warning(f"Token刷新失败(OTP未获取到) | 邮箱: {email}")
                                else:
                                    logger.warning(f"Token刷新失败 | 邮箱: {email}")

            except json.JSONDecodeError:
                # 删除无效的缓存
                redis_client.delete(key)
                continue
    except Exception as e:
        logger.error(f"刷新过期Token异常 | 异常: {str(e)}")


def attempt_sms_login(user, country, capResult=None, max_wait=90, poll_interval=5):
    """在需要OTP的情况下，自动轮询短信验证码并完成登录。"""
    try:
        phone = user.get("phone")
        email = user.get("email")
        if not phone:
            logger.warning(f"OTP登录失败 | 邮箱: {email} | 未提供手机号")
            return False

        logger.info(f"开始轮询短信验证码 | 邮箱: {email} | 手机尾号: {str(phone)[-4:]} | 最长等待: {max_wait}s")
        start = time.time()
        otp_code = ""
        while time.time() - start < max_wait:
            otp_code = get_sms_code(phone)
            if otp_code:
                masked = otp_code[:2] + "***" + otp_code[-1:]
                logger.info(f"收到短信验证码 | 邮箱: {email} | OTP: {masked}")
                break
            time.sleep(poll_interval)

        if not otp_code:
            logger.warning(f"未在时限内获取到短信验证码 | 邮箱: {email}")
            return False

        # 懒加载验证码：若未传入则复用 user['capResult'] 或内部生成
        if not capResult:
            capResult = user.get("capResult")
        if not capResult:
            cap_tmp = None
            for attempt in range(3):
                cap_tmp = gen_turnstile_token()
                if cap_tmp:
                    logger.info(f"验证码获取成功 | 邮箱: {email} | 尝试: {attempt + 1}/10 {cap_tmp}")
                    break
            if not cap_tmp:
                logger.error(f"验证码获取失败(OTP验证前) | 邮箱: {email} | 已尝试3次")
                return False
            capResult = cap_tmp

        res = validate_otp(user, country, otp_code, capResult)
        if res is True:
            return True
        if res == "HTTP_429":
            return "HTTP_429"
        else:
            logger.warning(f"OTP验证失败 | 邮箱: {email}")
            return False
    except Exception as e:
        logger.error(f"OTP自动登录异常 | 邮箱: {user.get('email', 'unknown')} | 异常: {str(e)}")
        return False


def acquire_order_lock(stage, order_id, ttl=240):
    """获取订单阶段的Redis分布式锁，成功返回True。"""
    try:
        key = f"vfs:lock:{stage}:{order_id}"
        # 使用底层redis client以支持 NX
        return bool(redis_client.client.set(key, "1", ex=ttl, nx=True))
    except Exception as e:
        logger.error(f"获取锁失败 | {stage}-{order_id} | 异常: {str(e)}")
        return False


def release_order_lock(stage, order_id):
    """释放订单阶段的Redis分布式锁。"""
    try:
        key = f"vfs:lock:{stage}:{order_id}"
        redis_client.delete(key)
    except Exception as e:
        logger.error(f"释放锁失败 | {stage}-{order_id} | 异常: {str(e)}")


def purge_account_for_order(order_id, email, country, context="login"):
    """在遇到 HTTP 429 等风控场景时，清理该订单绑定账号并回退状态。
    - 清理 Redis token 缓存
    - 清理待激活池(waitactive)中的记录（若存在）
    - 数据库清空 vfs_account/vfs_password 并将 order_status 置为 wait_registe
    """
    try:
        # 1) 删除 Redis Token 缓存
        try:
            cache_key = f"vfs_token:{email}:{country}"
            redis_client.delete(cache_key)
        except Exception as e:
            logger.debug(f"清理Token缓存异常 | 订单: {order_id} | 邮箱: {email} | 异常: {str(e)}")

        # 2) 删除待激活池中的记录
        try:
            # 某些 Redis 客户端可能无 hdel，做兼容
            if hasattr(redis_client, 'hdel'):
                redis_client.hdel("waitactive", email)
        except Exception:
            pass

        # 3) 同步数据库：清空账号，回退状态
        ok = update_order_account(order_id, None, None, "wait_registe")
        if ok:
            logger.warning(f"已清理账号(429) | 上下文: {context} | 订单: {order_id} | 邮箱: {email} | 国家: {country} | 状态回退为 wait_registe")
        else:
            logger.warning(f"清理账号(429)数据库更新失败 | 上下文: {context} | 订单: {order_id} | 邮箱: {email} | 国家: {country}")
        return ok
    except Exception as e:
        logger.error(f"清理账号(429)异常 | 上下文: {context} | 订单: {order_id} | 邮箱: {email} | 国家: {country} | 异常: {str(e)}")
        return False


def process_orders_with_executor(orders, func, max_workers, name_prefix, stage, per_task_timeout=120):
    """使用线程池并发处理订单任务，并记录结果，带Redis分布式锁避免重复并发。"""
    success = 0
    fail = 0
    skipped = 0
    if not orders:
        return

    def submit_wrapper(order):
        order_id = order.get('order_id')
        if not acquire_order_lock(stage, order_id):
            nonlocal skipped
            skipped += 1
            logger.debug(f"跳过任务（进行中） | {name_prefix}-{order_id}")
            return None

        def run_and_release():
            try:
                return func(order)
            finally:
                release_order_lock(stage, order_id)
        return run_and_release

    try:
        with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix=name_prefix) as executor:
            submitters = []
            submit_orders = []
            for order in orders:
                runner = submit_wrapper(order)
                if runner is not None:
                    submitters.append(runner)
                    submit_orders.append(order)

            futures = []
            for order, runner in zip(submit_orders, submitters):
                f = executor.submit(runner)
                futures.append((f, order))

            for f, order in futures:
                try:
                    result = f.result(timeout=per_task_timeout)
                    if result:
                        success += 1
                    else:
                        fail += 1
                except Exception as e:
                    fail += 1
                    logger.error(f"任务执行异常 | {name_prefix}-{order.get('order_id')} | 异常: {str(e)}")
    except Exception as e:
        logger.error(f"线程池批处理异常 | 名称前缀: {name_prefix} | 异常: {str(e)}")
    finally:
        total = len(orders)
        logger.info(f"批处理完成 | {name_prefix} | 总数: {total} | 成功: {success} | 失败: {fail} | 跳过: {skipped}")


# 数据库更新函数已移至 db_utils.py 模块


def get_orders_with_accounts():
    """获取已有账号的订单统计"""
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("无法获取数据库连接")
            return {}

        stats = {}

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 统计各国家的订单账号情况
            query = """
                SELECT
                    mission_code,
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN vfs_account IS NOT NULL AND vfs_account != '' THEN 1 END) as with_accounts,
                    COUNT(CASE WHEN vfs_account IS NULL OR vfs_account = '' THEN 1 END) as without_accounts
                FROM orders
                WHERE is_vfs_order = true
                AND mission_code IS NOT NULL
                AND mission_code != ''
                AND order_status = 'normal'
                GROUP BY mission_code
            """
            cursor.execute(query)
            results = cursor.fetchall()

            for row in results:
                country = row['mission_code'].lower()
                stats[country] = {
                    'total_orders': row['total_orders'],
                    'with_accounts': row['with_accounts'],
                    'without_accounts': row['without_accounts']
                }

        conn.close()
        return stats
    except Exception as e:
        logger.error(f"获取订单账号统计失败 | 异常: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return {}


def generate_custom_version():
    octet_3 = random.randint(0, 999)
    octet_4 = random.randint(0, 999)

    # 使用 f-string 格式化字符串
    ip_address = f"139.0.{octet_3}.{octet_4}"

    return ip_address


def gen_turnstile_token():
    cf_server = '************'
    url = "https://visa.vfsglobal.com/chn/zh/deu/login"
    sitekey = "0x4AAAAAABhlz7Ei4byodYjs"
    chrome_ua = generate_custom_version()
    data = {
        "url": url,
        'sitekey': sitekey,
        'token': 'ashd91789h89j9idh17892hd98j90j',
        'token': 'ashd91789h89j9idh17892hd98j90j',
        'proxy': "",
        'user_agent': f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_ua} Safari/537.36 Edg/{chrome_ua}",
        'ua_version': chrome_ua,
        'sec_ch_ua': '"Chromium";v="139", "Not=A?Brand";v="24", "Microsoft Edge";v="139"',
        'Language': "en-US,en;q=0.9",
    }
    resp = requests.post(
        f"http://{cf_server}:5006/get_cookie",
        json=data,
        headers={"Content-Type": "application/json"},
        timeout=90  # 减少超时时间，让停止更快响应
    )

    if resp.status_code == 200:
        try:
            response_data = resp.json()
            if 'data' in response_data and 'token' in response_data['data']:
                token = response_data['data']['token']
                print(f"Token获取成功!")
                return token
            else:
                print(f"响应格式错误: {resp.text}")
                return None
        except Exception as json_error:
            print(f"JSON解析错误: {json_error}, 响应: {resp.text}")
            return None
    else:
        print(f"HTTP错误 {resp.status_code}: {resp.text}")
        return None


def register_account_for_order(order):
    """为指定订单注册账号"""
    try:
        order_id = order['order_id']
        country = order['mission_code']
        logger.info(f"开始为订单注册账号 | 订单: {order_id} | 国家: {country}")

        capResult = None
        for attempt in range(3):
            capResult = gen_turnstile_token()
            if capResult:
                logger.info(f"验证码获取成功 | 订单: {order_id} | 尝试: {attempt + 1}/10 {capResult}")
                break
        if not capResult:
            logger.error(f"验证码获取失败 | 订单: {order_id} |  已尝试3次")
            return False

        # 注册账号
        result = register_account(country, capResult)
        if result and isinstance(result, dict):
            # 注册成功，更新订单的账号信息
            email = result['email']
            password = result['password']
            update_success = update_order_account(order_id, email, password, "wait_active")
            if update_success:
                logger.info(f"订单账号注册完成 | 订单: {order_id} | 邮箱: {email} | 密码: {password} | 结果: 成功")
                return True
            else:
                logger.warning(f"订单账号注册完成但更新失败 | 订单: {order_id} | 邮箱: {email}")
                return False
        else:
            logger.warning(f"订单账号注册失败 | 订单: {order_id}")
            return False
    except Exception as e:
        logger.error(f"订单账号注册异常 | 订单: {order.get('order_id', 'unknown')} | 异常: {str(e)}")
        return False


def login_for_order(order):
    """为指定订单进行登录获取Token"""
    try:
        order_id = order['order_id']
        country = order['mission_code']
        email = order['vfs_account']
        password = order['vfs_password']

        logger.info(f"开始为订单登录 | 订单: {order_id} | 邮箱: {email} | 国家: {country}")

        # 构造用户数据进行登录
        user_data = {
            "email": email,
            "password": password,
            "missionCode": country
        }

        # 尝试登录
        login_result = login_and_get_token(user_data, country)
        if login_result == "HTTP_429":
            purge_account_for_order(order_id, email, country, context="login")
            return False
        if login_result == "Token":
            logger.info(f"订单登录成功 | 订单: {order_id} | 邮箱: {email} | Token已缓存")
            return True
        elif login_result == "SMS":
            logger.info(f"订单登录需要短信验证 | 订单: {order_id} | 邮箱: {email} | 尝试自动获取OTP")
            sms_res = attempt_sms_login(user_data, country, user_data.get("capResult"))
            if sms_res is True:
                logger.info(f"订单登录成功(OTP) | 订单: {order_id} | 邮箱: {email} | Token已缓存")
                return True
            elif sms_res == "HTTP_429":
                purge_account_for_order(order_id, email, country, context="login")
                return False
            else:
                logger.warning(f"订单登录失败(OTP未获取到) | 订单: {order_id} | 邮箱: {email}")
                return False
        else:
            logger.warning(f"订单登录失败 | 订单: {order_id} | 邮箱: {email}")
            return False

    except Exception as e:
        logger.error(f"订单登录异常 | 订单: {order.get('order_id', 'unknown')} | 异常: {str(e)}")
        return False


# Token刷新功能已移除，现在为每个订单单独创建账号，不需要维护账号池


# 账号清理相关函数已移除，现在为每个订单单独创建账号


def show_order_account_statistics():
    """显示订单账号和Token统计信息"""
    try:
        order_stats = get_orders_with_accounts()

        if not order_stats:
            logger.info("暂无订单账号统计数据")
            return {}

        # 计算总计
        total_orders = sum(stats['total_orders'] for stats in order_stats.values())
        total_with_accounts = sum(stats['with_accounts'] for stats in order_stats.values())
        total_without_accounts = sum(stats['without_accounts'] for stats in order_stats.values())

        logger.info("=" * 80)
        logger.info("VFS订单账号和Token统计报告")
        logger.info("=" * 80)
        logger.info(f"总体统计 | 总订单: {total_orders} | 已有账号: {total_with_accounts} | 缺少账号: {total_without_accounts}")
        logger.info("-" * 80)

        for country, stats in sorted(order_stats.items()):
            account_rate = (stats['with_accounts'] / stats['total_orders'] * 100) if stats['total_orders'] > 0 else 0

            # 获取该国家的Token统计
            token_count = 0
            valid_token_count = 0
            try:
                pattern = f"vfs_token:*:{country}"
                token_keys = redis_client.keys(pattern)
                token_count = len(token_keys)

                current_time = int(time.time())
                for key in token_keys:
                    try:
                        cached_data = redis_client.get(key)
                        if cached_data:
                            token_data = json.loads(cached_data)
                            remaining_time = 2000 - (current_time - token_data.get('updateTokenTime', 0))
                            if remaining_time > 0:
                                valid_token_count += 1
                    except json.JSONDecodeError:
                        continue
            except Exception:
                pass

            logger.info(f"国家: {country.upper():8} | 总订单: {stats['total_orders']:3} | 有账号: {stats['with_accounts']:3} | 缺账号: {stats['without_accounts']:3} | 完成率: {account_rate:5.1f}% | Token: {valid_token_count}/{token_count}")

        # 显示Token总体统计
        try:
            from token_manager import get_token_statistics
            token_stats = get_token_statistics()
            logger.info("-" * 80)
            logger.info(f"Token统计 | 总计: {token_stats['total']} | 有效: {token_stats['valid']} | 过期: {token_stats['expired']}")
        except Exception:
            pass

        # 显示URN统计
        try:
            pattern = "vfs_urn:*"
            urn_keys = redis_client.keys(pattern)
            total_urns = len(urn_keys)
            valid_urns = 0

            current_time = int(time.time())
            for key in urn_keys:
                try:
                    cached_data = redis_client.get(key)
                    if cached_data:
                        urn_data = json.loads(cached_data)
                        remaining_time = 10000 - (current_time - urn_data.get('createTime', 0))
                        if remaining_time > 0:
                            valid_urns += 1
                except json.JSONDecodeError:
                    continue

            logger.info(f"URN统计 | 总计: {total_urns} | 有效: {valid_urns} | 过期: {total_urns - valid_urns}")
        except Exception:
            pass

        logger.info("=" * 80)

        return order_stats
    except Exception as e:
        logger.error(f"获取订单账号统计失败 | 异常: {str(e)}")
        return {}


def registration_worker():
    """订单账号注册、登录和URN创建工作线程（线程池重构版）"""
    logger.info(f"订单账号注册、登录和URN创建工作线程启动 | 周期: {REGISTRATION_POLL_INTERVAL}秒 | 并发: 注册3/登录5/URN3")

    while True:
        try:
            # 1. 处理需要创建账号的订单
            orders_need_accounts = get_orders_need_accounts()
            if orders_need_accounts:
                logger.info(f"发现需要创建账号的订单 | 数量: {len(orders_need_accounts)}")
                process_orders_with_executor(
                    orders_need_accounts,
                    register_account_for_order,
                    max_workers=3,
                    name_prefix="Register",
                    stage="register",
                    per_task_timeout=200
                )

            # 2. 处理需要登录的订单
            orders_need_login = get_orders_need_login()
            if orders_need_login:
                logger.info(f"发现需要登录的订单 | 数量: {len(orders_need_login)}")
                process_orders_with_executor(
                    orders_need_login,
                    login_for_order,
                    max_workers=5,
                    name_prefix="Login",
                    stage="login",
                    per_task_timeout=200
                )

            # 3. 处理需要创建URN的订单
            orders_need_urn = get_orders_need_urn()
            if orders_need_urn:
                logger.info(f"发现需要创建URN的订单 | 数量: {len(orders_need_urn)}")
                process_orders_with_executor(
                    orders_need_urn,
                    create_urn_for_order,
                    max_workers=3,
                    name_prefix="URN",
                    stage="urn",
                    per_task_timeout=200
                )

            if not orders_need_accounts and not orders_need_login and not orders_need_urn:
                logger.debug("当前没有需要处理的订单")

            # 固定周期调度
            time.sleep(REGISTRATION_POLL_INTERVAL)

        except Exception as e:
            logger.error(f"注册、登录和URN创建工作线程异常 | 异常: {str(e)}")
            time.sleep(REGISTRATION_POLL_INTERVAL)


def token_refresh_worker():
    """Token刷新工作线程"""
    logger.info("Token刷新工作线程启动 | 检查周期: 300秒")

    while True:
        try:
            refresh_expired_tokens()
            # 每隔5分钟检查一次Token状态
            time.sleep(TOKEN_REFRESH_POLL_INTERVAL)
        except Exception as e:
            logger.error(f"Token刷新工作线程异常 | 异常: {str(e)}")
            time.sleep(TOKEN_REFRESH_POLL_INTERVAL)


def statistics_worker():
    """统计信息工作线程"""
    logger.info("订单账号统计工作线程启动 | 显示周期: 300秒")

    while True:
        try:
            show_order_account_statistics()
            # 每隔5分钟显示一次统计信息
            time.sleep(STATISTICS_POLL_INTERVAL)
        except Exception as e:
            logger.error(f"统计工作线程异常 | 异常: {str(e)}")
            time.sleep(STATISTICS_POLL_INTERVAL)


def main():
    """主函数 - 启动异步工作线程"""
    logger.info("=" * 60)
    logger.info("VFS订单账号管理器启动")
    logger.info("功能: 为每个VFS订单单独创建账号、管理Token和创建URN")
    logger.info("版本: v4.0 (订单专属账号+Token管理+URN创建版本)")
    logger.info("=" * 60)

    try:
        # 启动订单账号注册、登录和URN创建工作线程
        registration_thread = threading.Thread(target=registration_worker, name="OrderRegistrationLoginURNWorker")
        registration_thread.daemon = True
        registration_thread.start()
        logger.info("订单账号注册、登录和URN创建工作线程已启动")

        # 启动Token刷新工作线程
        token_refresh_thread = threading.Thread(target=token_refresh_worker, name="TokenRefreshWorker")
        token_refresh_thread.daemon = True
        token_refresh_thread.start()
        logger.info("Token刷新工作线程已启动")

        # 启动统计信息工作线程
        stats_thread = threading.Thread(target=statistics_worker, name="StatisticsWorker")
        stats_thread.daemon = True
        stats_thread.start()
        logger.info("订单账号统计工作线程已启动")

        logger.info("所有工作线程启动完成，系统进入运行状态")
        logger.info("系统将为每个VFS订单自动创建专属账号、管理Token和创建URN")
        logger.info("按 Ctrl+C 停止服务")

        # 主线程保持运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭服务...")
        logger.info("VFS账号管理器已停止")
    except Exception as e:
        logger.error(f"主线程异常 | 异常: {str(e)}")
        logger.error("VFS账号管理器异常退出")


if __name__ == "__main__":
    main()
