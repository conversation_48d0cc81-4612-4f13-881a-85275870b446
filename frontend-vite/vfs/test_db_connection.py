#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_db_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        # 尝试导入数据库工具
        print("📦 导入数据库模块...")
        from db_utils import get_db_connection
        print("✅ db_utils 导入成功")
        
        # 尝试连接数据库
        print("🔗 尝试连接数据库...")
        conn = get_db_connection()
        
        if conn is None:
            print("❌ 数据库连接返回 None")
            print("💡 可能的原因:")
            print("   - 数据库服务未启动")
            print("   - 连接配置错误")
            print("   - 网络问题")
            return False
        
        print("✅ 数据库连接成功")
        
        # 测试基本查询
        print("📊 测试基本查询...")
        with conn.cursor() as cursor:
            cursor.execute("SELECT version()")
            version = cursor.fetchone()
            print(f"✅ PostgreSQL版本: {version[0]}")
            
            # 检查当前数据库
            cursor.execute("SELECT current_database()")
            db_name = cursor.fetchone()
            print(f"✅ 当前数据库: {db_name[0]}")
            
            # 列出所有表
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """)
            tables = cursor.fetchall()
            print(f"📋 数据库中的表 ({len(tables)}个):")
            for table in tables:
                print(f"   - {table[0]}")
        
        conn.close()
        print("✅ 数据库连接测试完成")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请检查:")
        print("   - db_utils.py 文件是否存在")
        print("   - psycopg2 是否已安装")
        return False
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        print("💡 请检查:")
        print("   - 数据库服务是否运行")
        print("   - 连接参数是否正确")
        print("   - 用户权限是否足够")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def test_date_ranges_table():
    """测试 date_ranges 表"""
    print("\n📅 测试 date_ranges 表...")
    
    try:
        from db_utils import get_db_connection
        
        conn = get_db_connection()
        if not conn:
            print("❌ 无法连接数据库")
            return False
        
        with conn.cursor() as cursor:
            # 检查表是否存在
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'date_ranges'
                )
            """)
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                print("❌ 表 'date_ranges' 不存在")
                print("🔧 创建表...")
                
                cursor.execute("""
                    CREATE TABLE date_ranges (
                        id SERIAL PRIMARY KEY,
                        order_id VARCHAR(255) NOT NULL,
                        start_date DATE NOT NULL,
                        end_date DATE NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
                print("✅ 表 'date_ranges' 已创建")
            else:
                print("✅ 表 'date_ranges' 存在")
            
            # 检查表结构
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'date_ranges'
                ORDER BY ordinal_position
            """)
            columns = cursor.fetchall()
            print("📋 表结构:")
            for col in columns:
                print(f"   - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})")
            
            # 检查数据
            cursor.execute("SELECT COUNT(*) FROM date_ranges")
            count = cursor.fetchone()[0]
            print(f"📊 表中记录数: {count}")
            
            if count > 0:
                cursor.execute("""
                    SELECT order_id, start_date, end_date 
                    FROM date_ranges 
                    ORDER BY start_date 
                    LIMIT 5
                """)
                records = cursor.fetchall()
                print("📋 前5条记录:")
                for record in records:
                    print(f"   - 订单: {record[0]} | {record[1]} ~ {record[2]}")
        
        conn.close()
        return count > 0
        
    except Exception as e:
        print(f"❌ date_ranges 表测试失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def test_orders_table():
    """测试 orders 表"""
    print("\n👥 测试 orders 表...")
    
    try:
        from db_utils import get_db_connection
        
        conn = get_db_connection()
        if not conn:
            print("❌ 无法连接数据库")
            return False
        
        with conn.cursor() as cursor:
            # 检查表是否存在
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'orders'
                )
            """)
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                print("❌ 表 'orders' 不存在")
                print("🔧 创建表...")
                
                cursor.execute("""
                    CREATE TABLE orders (
                        order_id VARCHAR(255) PRIMARY KEY,
                        mission_code VARCHAR(10),
                        center_code VARCHAR(10),
                        visa_code VARCHAR(10),
                        travel_date DATE,
                        is_vfs_order BOOLEAN DEFAULT FALSE,
                        order_status VARCHAR(50),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
                print("✅ 表 'orders' 已创建")
            else:
                print("✅ 表 'orders' 存在")
            
            # 检查VFS订单
            cursor.execute("""
                SELECT COUNT(*) FROM orders 
                WHERE is_vfs_order = true 
                AND order_status IN ('registe_success', 'urn_create_failed')
            """)
            vfs_count = cursor.fetchone()[0]
            print(f"📊 VFS订单数: {vfs_count}")
            
            if vfs_count > 0:
                cursor.execute("""
                    SELECT order_id, mission_code, center_code, visa_code
                    FROM orders 
                    WHERE is_vfs_order = true 
                    AND order_status IN ('registe_success', 'urn_create_failed')
                    ORDER BY order_id 
                    LIMIT 5
                """)
                records = cursor.fetchall()
                print("📋 前5个VFS订单:")
                for record in records:
                    print(f"   - 订单: {record[0]} | 国家: {record[1]} | 中心: {record[2]} | 签证: {record[3]}")
        
        conn.close()
        return vfs_count > 0
        
    except Exception as e:
        print(f"❌ orders 表测试失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def create_test_data():
    """创建测试数据"""
    print("\n🔧 创建测试数据...")
    
    try:
        from db_utils import get_db_connection
        from datetime import datetime, timedelta
        
        conn = get_db_connection()
        if not conn:
            print("❌ 无法连接数据库")
            return False
        
        with conn.cursor() as cursor:
            # 创建测试订单
            test_orders = [
                ('VFS_TEST_001', 'hun', 'PEK', 'S1', True, 'registe_success'),
                ('VFS_TEST_002', 'fra', 'SHA', 'T1', True, 'registe_success'),
                ('VFS_TEST_003', 'esp', 'PEK', 'T2', True, 'registe_success')
            ]
            
            for order_id, mission_code, center_code, visa_code, is_vfs, status in test_orders:
                cursor.execute("""
                    INSERT INTO orders (order_id, mission_code, center_code, visa_code, is_vfs_order, order_status)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON CONFLICT (order_id) DO UPDATE SET
                        mission_code = EXCLUDED.mission_code,
                        center_code = EXCLUDED.center_code,
                        visa_code = EXCLUDED.visa_code,
                        is_vfs_order = EXCLUDED.is_vfs_order,
                        order_status = EXCLUDED.order_status
                """, (order_id, mission_code, center_code, visa_code, is_vfs, status))
            
            # 创建测试日期范围
            today = datetime.now().date()
            test_ranges = [
                ('VFS_TEST_001', today, today + timedelta(days=30)),
                ('VFS_TEST_002', today + timedelta(days=7), today + timedelta(days=60)),
                ('VFS_TEST_003', today + timedelta(days=14), today + timedelta(days=90)),
                ('GENERAL_RANGE_001', today, today + timedelta(days=45))
            ]
            
            for order_id, start_date, end_date in test_ranges:
                cursor.execute("""
                    INSERT INTO date_ranges (order_id, start_date, end_date)
                    VALUES (%s, %s, %s)
                """, (order_id, start_date, end_date))
            
            conn.commit()
        
        conn.close()
        print(f"✅ 创建了 {len(test_orders)} 个测试订单和 {len(test_ranges)} 个日期范围")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def main():
    """主函数"""
    print("VFS数据库连接测试")
    print("=" * 50)
    
    # 1. 测试基本连接
    if not test_db_connection():
        print("\n❌ 数据库连接失败，无法继续测试")
        return False
    
    # 2. 测试表
    date_ranges_ok = test_date_ranges_table()
    orders_ok = test_orders_table()
    
    # 3. 如果没有数据，创建测试数据
    if not date_ranges_ok or not orders_ok:
        print("\n🤔 检测到缺少测试数据")
        response = input("是否创建测试数据？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            create_test_data()
            
            # 重新测试
            print("\n🔄 重新测试...")
            test_date_ranges_table()
            test_orders_table()
    
    print("\n" + "=" * 50)
    print("✅ 数据库测试完成")

if __name__ == "__main__":
    main()
