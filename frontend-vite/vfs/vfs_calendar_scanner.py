# -*- coding: utf-8 -*-
"""
VFS日历扫描器 - 基于数据库客户和Redis URN数据
功能：扫描有URN的VFS客户的可用预约日期
"""
from curl_cffi import requests
import time
import json
import threading
import random
import string
import base64
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
import logging
import psycopg2
from psycopg2.extras import RealDictCursor

# 导入现有模块
from RedisClient import RedisClient
from db_utils import get_db_connection
from common_remote_accounts import get_deu_account_from_remote, get_ita_account_from_remote

from vfs_mappings import get_mission_name, get_center_name, get_visa_name
from vfs_toolkit import send_wecom_notify_for_order


# 配置日志（仅在root无handler时配置，避免重复输出）
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s | %(levelname)-8s | %(threadName)-15s | %(funcName)-20s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
logger = logging.getLogger(__name__)

# 初始化Redis客户端
redis_client = RedisClient()

# 全局变量
rsa_string = ""
delegate = []


def get_current_timestamp():
    """获取当前时间戳"""
    now = datetime.now()
    return now.strftime("%Y-%m-%dT%H:%M:%S")


def format_rsa_string(compact_key: str) -> str:
    """格式化RSA字符串"""
    if not compact_key:
        return ""
    base64_content = compact_key.replace("|", "\n")
    return f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"


def encryption(text):
    """RSA加密"""
    try:
        if not rsa_string:
            return ""
        public_key = serialization.load_pem_public_key(rsa_string.encode())
        encrypted = public_key.encrypt(text.encode(), padding.PKCS1v15())
        return base64.b64encode(encrypted).decode()
    except Exception as e:
        logger.error(f"RSA加密失败: {e}")
        return ""


def generate_random_string(length=31):
    """生成随机字符串"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))


def convert_date_to_calendar_format(date_str):
    """转换日期格式 YYYY-MM-DD -> DD/MM/YYYY 或 MM/DD/YYYY -> DD/MM/YYYY"""
    try:
        # 如果已经是 DD/MM/YYYY 格式，直接返回
        if "/" in date_str and len(date_str.split("/")) == 3:
            parts = date_str.split("/")
            if len(parts[0]) == 2 and len(parts[1]) == 2 and len(parts[2]) == 4:
                # 检查是否是 MM/DD/YYYY 格式，需要转换为 DD/MM/YYYY
                if int(parts[0]) > 12:  # 第一部分大于12，说明是DD/MM/YYYY
                    return date_str
                else:  # 可能是 MM/DD/YYYY，转换为 DD/MM/YYYY
                    return f"{parts[1]}/{parts[0]}/{parts[2]}"

        # 如果是 YYYY-MM-DD 格式，转换为 DD/MM/YYYY
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        return date_obj.strftime("%d/%m/%Y")
    except Exception as e:
        logger.error(f"日期格式转换失败: {date_str}, 错误: {e}")
        return date_str


def get_today_if_past(date_str):
    """如果日期小于今天，则返回今天的日期"""
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        today = datetime.now().date()

        if date_obj.date() < today:
            return today.strftime("%Y-%m-%d")
        return date_str
    except Exception as e:
        logger.error(f"日期比较失败: {date_str}, 错误: {e}")
        return date_str


def get_cached_urn(order_id):
    """从Redis获取缓存的URN"""
    try:
        cache_key = f"vfs_urn:{order_id}"
        cached_data = redis_client.get(cache_key)
        if cached_data:
            urn_data = json.loads(cached_data)
            # 检查是否过期（2000秒有效期）
            if int(time.time()) - urn_data.get('createTime', 0) < 10000:
                remaining_time = 10000 - (int(time.time()) - urn_data.get('createTime', 0))
                logger.debug(f"使用缓存URN | 订单: {order_id} | URN: {urn_data.get('urn', '')[:20]}... | 剩余时间: {remaining_time}秒")
                return urn_data
            else:
                # URN过期，删除缓存
                redis_client.delete(cache_key)
                logger.debug(f"缓存URN已过期 | 订单: {order_id}")
        return None
    except Exception as e:
        logger.error(f"获取缓存URN失败 | 订单: {order_id} | 异常: {str(e)}")
        return None


def get_vfs_customers_with_urns():
    """从数据库获取有URN的VFS客户"""
    try:
        logger.debug("🔗 查询数据库中的VFS客户...")
        conn = get_db_connection()
        if not conn:
            logger.error("❌ 无法获取数据库连接")
            return []

        customers = []

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查询VFS客户数据
            query = """
                SELECT DISTINCT
                    order_id,
                    mission_code,
                    center_code,
                    visa_code,
                    travel_date,
                    vfs_account,
                    urn,
                    order_status
                FROM orders
                WHERE is_vfs_order = true
                AND auto_schedule = true
                AND mission_code != 'ita'
                AND urn IS NOT NULL
                AND urn != ''
                AND order_status IN ('registe_success', 'waitlist_available')
                ORDER BY order_id
            """
            cursor.execute(query)
            db_customers = cursor.fetchall()

            logger.info(f"📊 数据库中VFS客户: {len(db_customers)} 个")

            # 检查每个客户是否在Redis中有有效的URN
            for customer in db_customers:
                order_id = customer['order_id']
                mission_code = customer['mission_code']

                urn_info = get_cached_urn(order_id)

                if urn_info:
                    remaining_time = 2000 - (int(time.time()) - urn_info.get('createTime', 0))
                    customers.append({
                        'order_id': order_id,
                        'mission_code': mission_code,
                        'center_code': customer['center_code'],
                        'visa_code': customer['visa_code'],
                        'vfs_account': customer['vfs_account'],
                        'travel_date': str(customer['travel_date']) if customer['travel_date'] else None,
                        'urn': urn_info.get('urn'),
                        'urn_create_time': urn_info.get('createTime'),
                        'urn_remaining_time': remaining_time
                    })
                    logger.debug(f"✅ 客户 {order_id} 有有效URN: {urn_info.get('urn', '')[:20]}... | 剩余时间: {remaining_time}秒")
                elif customer['order_status'] == 'waitlist_available':
                    customers.append({
                        'order_id': order_id,
                        'mission_code': mission_code,
                        'center_code': customer['center_code'],
                        'visa_code': customer['visa_code'],
                        'vfs_account': customer['vfs_account'],
                        'travel_date': str(customer['travel_date']) if customer['travel_date'] else None,
                        'urn': customer['urn'],
                        'urn_create_time': int(time.time()),
                        'urn_remaining_time': 86400
                    })
                    logger.debug(f"✅ 客户 {order_id} 有有效候补URN: {customer.get('urn', '')[:20]}... ")
                else:
                    logger.debug(f"❌ 客户 {order_id} 无有效URN数据")

        conn.close()
        logger.info(f"✅ 找到有URN的VFS客户: {len(customers)} 个")

        # 显示前3个客户作为示例
        for i, customer in enumerate(customers[:3]):
            logger.info(f"  👤 客户 {i+1}: 订单 {customer['order_id']} | "
                        f"国家: {customer['mission_code']} | 中心: {customer['center_code']} | "
                        f"签证: {customer['visa_code']}")

        if len(customers) > 3:
            logger.info(f"  👤 ... 还有 {len(customers) - 3} 个客户")

        return customers

    except Exception as e:
        logger.error(f"❌ 获取VFS客户数据失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return []


def get_date_ranges_for_customer(customer):
    """根据客户信息获取相关的日期范围"""
    try:
        logger.debug(f"🔗 查询订单 {customer['order_id']} 的日期范围...")
        conn = get_db_connection()
        if not conn:
            logger.error("❌ 无法获取数据库连接")
            return []

        date_ranges = []

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 首先尝试获取该订单专属的日期范围
            query = """
                SELECT id, order_id, start_date, end_date
                FROM date_ranges
                WHERE order_id = %s
                ORDER BY start_date
            """
            cursor.execute(query, (customer['order_id'],))
            ranges = cursor.fetchall()

            # 如果没有专属的日期范围，获取通用的日期范围
            if not ranges:
                logger.debug(f"订单 {customer['order_id']} 没有专属日期范围，查询通用范围...")
                query = """
                    SELECT id, order_id, start_date, end_date
                    FROM date_ranges
                    WHERE order_id LIKE 'GENERAL_%' OR order_id = 'DEFAULT'
                    ORDER BY start_date
                """
                cursor.execute(query)
                ranges = cursor.fetchall()

            logger.info(f"📊 为订单 {customer['order_id']} 查询到 {len(ranges)} 条日期范围记录")

            for range_data in ranges:
                # 处理日期，如果小于今天则使用今天
                start_date = get_today_if_past(str(range_data['start_date']))
                end_date = str(range_data['end_date'])

                date_ranges.append({
                    'id': range_data['id'],
                    'order_id': range_data['order_id'],
                    'start_date': start_date,
                    'end_date': end_date,
                    'start_date_calendar': start_date,
                    'end_date_calendar': end_date
                })

                logger.debug(f"📅 处理日期范围 | 订单: {range_data['order_id']} | "
                             f"原始: {range_data['start_date']} ~ {range_data['end_date']} | "
                             f"处理后: {start_date} ~ {end_date}")

        conn.close()
        logger.info(f"✅ 为订单 {customer['order_id']} 获取到 {len(date_ranges)} 个有效日期范围")

        return date_ranges

    except Exception as e:
        logger.error(f"❌ 获取订单 {customer['order_id']} 日期范围失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return []


def get_all_date_ranges_from_db():
    """从数据库获取所有日期范围（用于统计）"""
    try:
        logger.debug("🔗 查询数据库中的所有日期范围...")
        conn = get_db_connection()
        if not conn:
            logger.error("❌ 无法获取数据库连接")
            return []

        date_ranges = []

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            query = """
                SELECT id, order_id, start_date, end_date
                FROM date_ranges
                ORDER BY start_date
            """
            cursor.execute(query)
            ranges = cursor.fetchall()

            logger.debug(f"📊 从数据库查询到 {len(ranges)} 条日期范围记录")

            for range_data in ranges:
                # 处理日期，如果小于今天则使用今天
                start_date = get_today_if_past(str(range_data['start_date']))
                end_date = str(range_data['end_date'])

                date_ranges.append({
                    'id': range_data['id'],
                    'order_id': range_data['order_id'],
                    'start_date': start_date,
                    'end_date': end_date,
                    'start_date_calendar': start_date,
                    'end_date_calendar': end_date
                })

        conn.close()
        logger.debug(f"✅ 获取到 {len(date_ranges)} 个有效日期范围")

        return date_ranges

    except Exception as e:
        logger.error(f"❌ 获取数据库日期范围失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return []


def update_order_status_in_db(order_id: str, new_status: str):
    """将订单状态写回数据库。"""
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            logger.error(f"❌ 更新订单状态失败，无法获取数据库连接 | 订单: {order_id} | 状态: {new_status}")
            return False
        with conn.cursor() as cursor:
            cursor.execute(
                "UPDATE orders SET order_status = %s WHERE order_id = %s",
                (new_status, order_id)
            )
        conn.commit()
        logger.info(f"📝 订单状态已更新 | 订单: {order_id} | 新状态: {new_status}")
        return True
    except Exception as e:
        logger.error(f"❌ 更新订单状态异常 | 订单: {order_id} | 状态: {new_status} | 错误: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()


def convert_ddmmyyyy_to_yyyy_mm_dd(date_str: str):
    """将 dd/mm/yyyy 转为 yyyy-mm-dd；已是yyyy-mm-dd则原样返回；失败返回None。"""
    try:
        if not date_str or not isinstance(date_str, str):
            return None
        s = date_str.strip()
        # 已是 yyyy-mm-dd 直接返回前10位
        if len(s) >= 10 and s[4] == '-' and s[7] == '-':
            return s[:10]
        # 统一分隔符
        s = s.replace('\\', '/')
        from datetime import datetime
        dt = datetime.strptime(s, "%d/%m/%Y")
        return dt.strftime("%Y-%m-%d")
    except Exception as e:
        logger.warning(f"预约日期格式解析失败 | 原始: {date_str} | 错误: {e}")
        return None


def convert_mmddyyyy_to_yyyy_mm_dd(date_str: str):
    """将 mm/dd/yyyy 转为 yyyy-mm-dd；已是yyyy-mm-dd则原样返回；失败返回None。"""
    try:
        if not date_str or not isinstance(date_str, str):
            return None
        s = date_str.strip()
        # 已是 yyyy-mm-dd 直接返回前10位
        if len(s) >= 10 and s[4] == '-' and s[7] == '-':
            return s[:10]
        # 统一分隔符
        s = s.replace('\\', '/')
        parts = s.split('/')
        if len(parts) < 3:
            return None
        mm, dd, yyyy = int(parts[0]), int(parts[1]), int(parts[2])
        dt = datetime(int(yyyy), int(mm), int(dd))
        return dt.strftime("%Y-%m-%d")
    except Exception as e:
        logger.warning(f"预约日期(MDY)格式解析失败 | 原始: {date_str} | 错误: {e}")
        return None


def split_date_range_by_month(start_date: str, end_date: str):
    """
    将 [YYYY-MM-DD, YYYY-MM-DD] 日期范围按月份切分。
    返回的每个子范围都包含: start_date, end_date, start_date_calendar, end_date_calendar (均为YYYY-MM-DD)
    """
    try:
        from datetime import datetime, date
        import calendar
        s = datetime.strptime(start_date, "%Y-%m-%d").date()
        e = datetime.strptime(end_date, "%Y-%m-%d").date()
        parts = []
        cur = s
        while cur <= e:
            last_day = calendar.monthrange(cur.year, cur.month)[1]
            seg_end = min(date(cur.year, cur.month, last_day), e)
            parts.append({
                "start_date": cur.strftime("%Y-%m-%d"),
                "end_date": seg_end.strftime("%Y-%m-%d"),
                "start_date_calendar": cur.strftime("%Y-%m-%d"),
                "end_date_calendar": seg_end.strftime("%Y-%m-%d"),
            })
            # 前进到下个月的第一天
            if cur.month == 12:
                cur = date(cur.year + 1, 1, 1)
            else:
                cur = date(cur.year, cur.month + 1, 1)
        return parts
    except Exception as e:
        logger.warning(f"按月拆分日期范围失败 | 原始: {start_date} ~ {end_date} | 错误: {e}")
        return [{
            "start_date": start_date,
            "end_date": end_date,
            "start_date_calendar": start_date,
            "end_date_calendar": end_date,
        }]


def update_order_transaction_id(order_id: str, transaction_id: str):
    """将支付交易号写回订单表的transaction_id。"""
    if not order_id or not transaction_id:
        return False
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            logger.error(f"❌ 更新交易号失败，无法获取数据库连接 | 订单: {order_id}")
            return False
        with conn.cursor() as cursor:
            cursor.execute(
                """
                UPDATE orders
                SET transaction_id = %s,
                    updated_at = NOW()
                WHERE order_id = %s
                """,
                (transaction_id, order_id)
            )
        conn.commit()
        logger.info(f"📝 已写入交易号 | 订单: {order_id} | trxSerNo: {str(transaction_id)[:16]}...")
        return True
    except Exception as e:
        logger.error(f"❌ 更新交易号异常 | 订单: {order_id} | 错误: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()


def update_order_appointment_info_in_db(order_id: str, appointment_date: str = None, appointment_time: str = None, request_ref_no: str = None):
    """将预约日期、时间、RequestRefNo写回订单表。缺失值写入NULL。"""
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            logger.error(f"❌ 更新预约信息失败，无法获取数据库连接 | 订单: {order_id}")
            return False
        with conn.cursor() as cursor:
            cursor.execute(
                """
                UPDATE orders
                SET appointment_date = %s,
                    appointment_time = %s,
                    requestrefno = %s,
                    updated_at = NOW()
                WHERE order_id = %s
                """,
                (appointment_date, appointment_time, request_ref_no, order_id)
            )
        conn.commit()
        logger.info(f"📝 已写入预约信息 | 订单: {order_id} | 日期: {appointment_date} | 时间: {appointment_time} | RequestRefNo: {str(request_ref_no)[:12]}...")
        return True
    except Exception as e:
        logger.error(f"❌ 更新预约信息异常 | 订单: {order_id} | 错误: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()


def get_cached_token(email, country):
    """从Redis获取缓存的Token"""
    try:
        cache_key = f"vfs_token:{email}:{country}"
        cached_data = redis_client.get(cache_key)
        if cached_data:
            token_data = json.loads(cached_data)
            # 检查是否过期（6000秒有效期）
            if int(time.time()) - token_data.get('updateTokenTime', 0) < 6000:
                remaining_time = 6000 - (int(time.time()) - token_data.get('updateTokenTime', 0))
                logger.debug(f"使用缓存Token | 邮箱: {email} | 国家: {country} | 剩余时间: {remaining_time}秒")
                return token_data
            else:
                # Token过期，删除缓存
                redis_client.delete(cache_key)
                logger.debug(f"缓存Token已过期 | 邮箱: {email} | 国家: {country}")
        return None
    except Exception as e:
        logger.error(f"获取缓存Token失败 | 邮箱: {email} | 国家: {country} | 异常: {str(e)}")
        return None


def get_available_token_for_country(country):
    """获取指定国家的可用Token"""
    try:
        # 从Redis缓存中查找可用的Token
        pattern = f"vfs_token:*:{country}"
        token_keys = redis_client.keys(pattern)

        logger.debug(f"🔍 查找国家 {country} 的Token | 找到 {len(token_keys)} 个Token键")

        for key in token_keys:
            try:
                cached_data = redis_client.get(key)
                if cached_data:
                    token_data = json.loads(cached_data)
                    # 检查Token是否还有效（至少剩余300秒）
                    remaining_time = 6000 - (int(time.time()) - token_data.get('updateTokenTime', 0))
                    if remaining_time > 300:
                        logger.info(f"找到可用Token | 国家: {country} | 邮箱: {token_data.get('email')} | 剩余时间: {remaining_time}秒")
                        return token_data
                    else:
                        logger.debug(f"Token剩余时间不足 | 国家: {country} | 邮箱: {token_data.get('email')} | 剩余: {remaining_time}秒")
            except json.JSONDecodeError:
                # 删除无效的缓存
                logger.debug(f"删除无效Token缓存: {key}")
                redis_client.delete(key)
                continue

        logger.debug(f"未找到国家 {country} 的可用Token")
        return None

    except Exception as e:
        logger.error(f"获取可用Token失败 | 国家: {country} | 异常: {str(e)}")
        return None


def get_login_user_for_country(mission_code, vfs_account=None):
    """根据 vfs_account+mission_code 精确获取登录用户；DEU 从远程获取，其它国家按本地缓存。"""
    try:
        # 德国(deu)：优先从远程获取；失败等待60秒重试一次
        if mission_code and str(mission_code).lower() == 'deu':
            for attempt in range(2):
                token_data = get_deu_account_from_remote()
                if token_data:
                    logger.info(f"[DEU_REMOTE_ACCOUNT] 使用远程账号 | 邮箱: {token_data.get('email')}")
                    return token_data
                if attempt == 0:
                    logger.warning("[DEU_REMOTE_ACCOUNT] 远程账号获取失败，60秒后重试一次")
                    time.sleep(60)
            logger.warning("[DEU_REMOTE_ACCOUNT] 远程账号两次获取均失败")
            return None
        elif mission_code and str(mission_code).lower() == 'ita':
            for attempt in range(2):
                token_data = get_ita_account_from_remote()
                if token_data:
                    logger.info(f"[ITA_REMOTE_ACCOUNT] 使用远程账号 | 邮箱: {token_data.get('email')}")
                    return token_data
                if attempt == 0:
                    logger.warning("[ITA_REMOTE_ACCOUNT] 远程账号获取失败，60秒后重试一次")
                    time.sleep(60)
            logger.warning("[ITA_REMOTE_ACCOUNT] 远程账号两次获取均失败")
            return None

        # 非DEU：优先按指定账户+国家精确取 Token
        if vfs_account:
            key = f"vfs_token:{vfs_account}:{mission_code}"
            cached = redis_client.get(key)
            if cached:
                try:
                    token_data = json.loads(cached)
                except Exception:
                    token_data = None
                if token_data:
                    remaining_time = 6000 - (int(time.time()) - token_data.get('updateTokenTime', 0))
                    if remaining_time > 300:
                        logger.info(
                            f"使用指定账户Token | 账户: {vfs_account} | 国家: {mission_code} | 邮箱: {token_data.get('email')} | 剩余: {remaining_time}秒"
                        )
                        return token_data
                    else:
                        logger.debug(
                            f"指定账户Token即将过期 | 账户: {vfs_account} | 国家: {mission_code} | 剩余: {remaining_time}秒"
                        )
                else:
                    logger.debug(f"指定账户Token缓存解析失败 | 键: {key}")
            else:
                logger.debug(f"未找到指定账户Token | 键: {key}")

        # 回退：按国家查找任意可用 Token
        token_data = get_available_token_for_country(mission_code)
        if token_data:
            logger.debug(f"选择登录用户(回退): {token_data.get('email')} | 国家: {mission_code}")
            return token_data
        else:
            logger.debug(f"国家 {mission_code} 没有可用的Token")
            return None

    except Exception as e:
        logger.error(f"获取登录用户失败 | 国家: {mission_code} | 账户: {vfs_account} | 错误: {e}")
        return None


def gen_proxy():
    """生成代理 - 简化版本"""
    try:
        return f"http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-de-session-{generate_random_string(10)}:<EMAIL>:33335"
    except Exception as e:
        logger.error(f"生成代理失败: {e}")
        return None


def get_random_cf_cookie(type):
    """从cf_pool中随机获取一个cookie数据"""
    try:
        if type == 'schedule':
            keys = redis_client.client.keys(f'cf_schedule_cookie*')
        else:
            keys = redis_client.client.keys(f'cf_cookie*')
        if not keys:
            print("❌ 没有可用的CF token")
            return None
        # 随机选择一个键
        random_key = random.choice(keys)
        # 获取token数据
        token_data = redis_client.get(random_key)
        if token_data is None:
            print(f"⚠️  Token键 {random_key} 已过期或不存在")
            return None
        print(f"✅ 获取token: {random_key}")
        # 解析并返回token数据
        return json.loads(token_data)
    except Exception as e:
        print(f"获取cf_pool cookie失败: {e}")
        return None


def refresh_global_config():
    """刷新全局配置"""
    global rsa_string, delegate

    first_run = True

    while True:
        try:
            if not first_run:
                time.sleep(10)
            first_run = False

            # 刷新RSA密钥
            source_rsa_str = redis_client.get('rsa_str')
            if source_rsa_str:
                rsa_string = format_rsa_string(source_rsa_str)
                logger.info(f"✅ RSA密钥已加载 | 长度: {len(rsa_string)} 字符")
            else:
                logger.warning("⚠️ RSA密钥未找到")

            # 刷新代理列表
            delegate_str = redis_client.get("login_proxy")
            if delegate_str:
                delegate = json.loads(delegate_str)
                logger.info(f"✅ 代理列表已加载 | 代理数: {len(delegate)}")
            else:
                logger.warning("⚠️ 代理列表(login_proxy)未找到")

            logger.debug(f"🔄 配置刷新完成 | 代理数: {len(delegate)} | RSA: {bool(rsa_string)}")

        except Exception as e:
            logger.error(f"❌ 刷新全局配置失败: {e}")


def acquire_customer_scan_lock(order_id: str, ttl_seconds: int = 60):
    """尝试获取该订单的扫描互斥锁，成功返回token，失败返回None。"""
    try:
        lock_key = f"vfs_calendar_scan_lock:{order_id}"
        token = f"{threading.current_thread().name}:{int(time.time()*1000)}:{random.randint(1000, 9999)}"
        # 使用 Redis 原生命令 SET key value NX EX ttl 实现互斥锁
        ok = redis_client.client.set(lock_key, token, nx=True, ex=ttl_seconds)
        if ok:
            logger.debug(f"🔒 获取锁成功 | 订单: {order_id} | 锁: {lock_key} | token: {token}")
            return token
        else:
            logger.info(f"⛔ 已有线程在扫描 | 订单: {order_id} | 锁: {lock_key}")
            return None
    except Exception as e:
        logger.error(f"获取扫描锁异常 | 订单: {order_id} | 错误: {e}")
        return None


def release_customer_scan_lock(order_id: str, token: str):
    """释放订单扫描锁（compare-and-del 安全释放）。返回True/False。"""
    try:
        lock_key = f"vfs_calendar_scan_lock:{order_id}"
        lua = """
        if redis.call('get', KEYS[1]) == ARGV[1] then
            return redis.call('del', KEYS[1])
        else
            return 0
        end
        """
        res = redis_client.client.eval(lua, 1, lock_key, token)
        if res == 1:
            logger.debug(f"🔓 释放锁成功 | 订单: {order_id} | 锁: {lock_key}")
            return True
        else:
            logger.debug(f"🔓 无需释放（非持有者或已过期） | 订单: {order_id} | 锁: {lock_key}")
            return False
    except Exception as e:
        logger.error(f"释放扫描锁异常 | 订单: {order_id} | 错误: {e}")
        return False


def scan_calendar_for_customer(customer, date_range, login_user, cf_params):
    """为客户扫描日历可用日期"""
    try:
        formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"📅 开始日历扫描 | {formatted_time} | 订单: {customer['order_id']} | "
                    f"国家: {customer['mission_code']} | 中心: {customer['center_code']} | "
                    f"签证类型: {customer['visa_code']} | "
                    f"日期范围: {date_range['start_date_calendar']} ~ {date_range['end_date_calendar']} | "
                    f"用户: {login_user.get('email', 'Unknown')[:20]}... | "
                    f"代理: {(cf_params.get('proxy')[:30] + '...') if (cf_params and cf_params.get('proxy')) else 'None'}")

        # 生成随机授权头
        r_auth = f"EAAAAN{generate_random_string(597)}="

        # 基于 cf_params 组装 UA 与 Cookie
        ua_from_cf = (cf_params.get("user_agent") if cf_params else None)
        cf_clearance = (cf_params.get("cookie") if cf_params else None)
        ltsn = login_user.get('ltsn', '')
        cookie_header = ltsn if ltsn else ''
        if cf_clearance:
            cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"

        url = "https://lift-apicn.vfsglobal.com/appointment/calendar"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": login_user.get("token"),
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{customer['mission_code']}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": cookie_header,
            "user-agent": (ua_from_cf or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        }

        # urn 优先使用远程账号(登录用户)的 urn，否则回退订单 urn
        urn_for_req = (login_user.get('urn') or customer.get('urn'))
        data = {
            "countryCode": "chn",
            "missionCode": customer['mission_code'],
            "centerCode": customer['center_code'],
            "visaCategoryCode": customer['visa_code'],
            "loginUser": login_user.get("email"),
            "fromDate": convert_date_to_calendar_format(date_range['start_date_calendar']),  # 转为DD/MM/YYYY格式
            "payCode": "",
            "urn": urn_for_req
        }
        logger.debug(f"📨 日历请求URN来源: {'login_user.urn' if login_user.get('urn') else 'customer.urn'} | 值: {(urn_for_req or '')[:6]}...")

        cf_proxy = (cf_params.get("proxy") if cf_params else None)
        proxies = {"http": cf_proxy, "https": cf_proxy} if cf_proxy else None

        logger.info(f"📡 发送日历API请求 | 订单: {customer['order_id']} | "
                    f"URL: {url} | 请求数据: {json.dumps(data, ensure_ascii=False)}")

        start_time = time.time()
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['firefox133', 'firefox135']),
            verify=False,
            timeout=20
        )
        end_time = time.time()
        request_duration = round((end_time - start_time) * 1000, 2)  # 毫秒

        logger.info(f"📡 日历API响应 | 订单: {customer['order_id']} | "
                    f"状态码: {response.status_code} | 耗时: {request_duration}ms | "
                    f"响应大小: {len(response.content)}字节")

        if response.status_code == 200:
            result = response.json()

            logger.debug(f"📋 日历API完整响应 | 订单: {customer['order_id']} | "
                         f"响应: {json.dumps(result, ensure_ascii=False)}")

            if isinstance(result, dict) and result.get('calendars') and len(result.get('calendars')) > 0:
                # 提取可用日期
                calendars = result.get('calendars')
                available_dates = list(dict.fromkeys([item.get('date') for item in calendars]))

                # 统计每个日期的可用数量
                date_stats = {}
                for calendar_item in calendars:
                    date = calendar_item.get('date')
                    if date not in date_stats:
                        date_stats[date] = 0
                    date_stats[date] += 1

                logger.info(f"✅ 日历扫描成功 | {formatted_time} | 订单: {customer['order_id']} | "
                            f"日期范围: {date_range['start_date']} ~ {date_range['end_date']} | "
                            f"国家: {customer['mission_code']} | 中心: {customer['center_code']} | "
                            f"签证类型: {customer['visa_code']} | "
                            f"可用日期: {len(available_dates)}个 | 总日历项: {len(calendars)}个")

                # 详细显示前5个可用日期
                for i, date in enumerate(available_dates[:5]):
                    count = date_stats.get(date, 0)
                    logger.info(f"  📅 可用日期 {i+1}: {date} | 可用项数: {count}")

                if len(available_dates) > 5:
                    logger.info(f"  📅 ... 还有 {len(available_dates) - 5} 个可用日期")

                # 返回可用日期和r_auth，供时间段请求使用
                return available_dates, r_auth
            else:
                # 分析无可用日期的原因
                error_info = ""
                if result.get('error'):
                    error_info = f" | 错误: {result.get('error')}"
                elif result.get('message'):
                    error_info = f" | 消息: {result.get('message')}"

                logger.info(f"❌ 日历无可用日期 | {formatted_time} | 订单: {customer['order_id']} | "
                            f"日期范围: {date_range['start_date']} ~ {date_range['end_date']} | "
                            f"国家: {customer['mission_code']} | 中心: {customer['center_code']}{error_info}")
                return [], None
        else:
            # 记录HTTP错误详情
            try:
                error_response = response.json()
                error_detail = f" | 错误详情: {json.dumps(error_response, ensure_ascii=False)}"
            except:
                error_detail = f" | 响应内容: {response.text[:200]}..."

            logger.warning(f"⚠️ 日历扫描失败 | {formatted_time} | 订单: {customer['order_id']} | "
                           f"状态码: {response.status_code} | 国家: {customer['mission_code']}{error_detail}")
            return [], None

    except Exception as e:
        logger.error(f"❌ 日历扫描异常 | 订单: {customer.get('order_id', 'Unknown')} | "
                     f"国家: {customer.get('mission_code', 'Unknown')} | 错误: {e}")
        return [], None


def request_time_slots_for_date(customer, selected_date, login_user, cf_params, r_auth):
    """请求指定日期的时间段 - 使用与日历请求相同的r_auth和代理"""
    try:
        formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"⏰ 开始时间段请求 | {formatted_time} | 订单: {customer['order_id']} | "
                    f"选中日期: {selected_date} | 国家: {customer['mission_code']} | "
                    f"中心: {customer['center_code']} | 签证类型: {customer['visa_code']} | "
                    f"用户: {login_user.get('email', 'Unknown')[:20]}... | "
                    f"代理: {(cf_params.get('proxy')[:30] + '...') if (cf_params and cf_params.get('proxy')) else 'None'} | "
                    f"复用r_auth: {r_auth[:20]}...")
        # 基于 cf_params 组装 UA 与 Cookie
        ua_from_cf = (cf_params.get("user_agent") if cf_params else None)
        cf_clearance = (cf_params.get("cookie") if cf_params else None)
        ltsn = login_user.get('ltsn', '')
        cookie_header = ltsn if ltsn else ''
        if cf_clearance:
            cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"
        # 使用与common_publish_worker.py相同的时间段API
        url = "https://lift-apicn.vfsglobal.com/appointment/timeslot"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,  # 使用与日历请求相同的r_auth
            "authorize": login_user.get("token"),
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "route": f"chn/zh/{customer['mission_code']}",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "cookie": cookie_header,
            "user-agent": (ua_from_cf or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        }

        print(f"headers: {headers}")

        # 确保日期格式正确（selected_date可能已经是MM/DD/YYYY格式）
        # 统一转换为DD/MM/YYYY格式
        selected_date_calendar = convert_date_to_calendar_format(selected_date)

        # 使用与common_publish_worker.py相同的数据格式；urn 优先使用远程账号(登录用户)的 urn
        urn_for_req = (login_user.get('urn') or customer.get('urn'))
        data = {
            "countryCode": "chn",
            "missionCode": customer['mission_code'],
            "centerCode": customer['center_code'],
            "loginUser": login_user.get("email"),
            "visaCategoryCode": customer['visa_code'],
            "slotDate": selected_date_calendar,  # 使用slotDate而不是appointmentDate
            "urn": urn_for_req
        }
        logger.debug(f"📨 时间段请求URN来源: {'login_user.urn' if login_user.get('urn') else 'customer.urn'} | 值: {(urn_for_req or '')[:6]}...")

        cf_proxy = (cf_params.get("proxy") if cf_params else None)
        proxies = {"http": cf_proxy, "https": cf_proxy} if cf_proxy else None

        logger.debug(f"📡 发送时间段API请求 | 订单: {customer['order_id']} | "
                     f"URL: {url} | 请求数据: {json.dumps(data, ensure_ascii=False)}")

        start_time = time.time()
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate='chrome136',
            verify=False,
            timeout=15  # 使用与common_publish_worker.py相同的超时时间
        )
        end_time = time.time()
        request_duration = round((end_time - start_time) * 1000, 2)  # 毫秒

        logger.info(f"📡 时间段API响应 | 订单: {customer['order_id']} | "
                    f"状态码: {response.status_code} | 耗时: {request_duration}ms | "
                    f"响应大小: {len(response.content)}字节")

        if response.status_code == 200:
            result = response.json()

            logger.debug(f"📋 时间段API完整响应 | 订单: {customer['order_id']} | "
                         f"响应: {json.dumps(result, ensure_ascii=False)}")

            # 使用与common_publish_worker.py相同的响应处理逻辑
            if isinstance(result, dict) and result.get('slots') is not None:
                time_slots = result.get('slots')

                if len(time_slots) > 0:
                    logger.info(f"✅ 时间段获取成功 | {formatted_time} | 订单: {customer['order_id']} | "
                                f"日期: {selected_date} | 国家: {customer['mission_code']} | "
                                f"中心: {customer['center_code']} | "
                                f"可用时间段: {len(time_slots)}个")

                    # 显示时间段详情
                    for i, slot in enumerate(time_slots):
                        slot_time = slot.get('slot', 'Unknown')
                        slot_id = slot.get('allocationId', 'Unknown')

                        logger.info(f"  ⏰ 时间段 {i+1}: {slot_time} | SlotID: {slot_id}")

                    return time_slots
                else:
                    logger.info(f"❌ 时间段列表为空 | {formatted_time} | 订单: {customer['order_id']} | "
                                f"日期: {selected_date} | 国家: {customer['mission_code']}")
                    return []
            else:
                # 分析无时间段的原因
                error_info = ""
                if result.get('error'):
                    error_info = f" | 错误: {result.get('error')}"
                elif result.get('message'):
                    error_info = f" | 消息: {result.get('message')}"

                logger.info(f"❌ 时间段无可用 | {formatted_time} | 订单: {customer['order_id']} | "
                            f"日期: {selected_date} | 国家: {customer['mission_code']}{error_info}")
                return []
        else:
            # 记录HTTP错误详情
            try:
                error_response = response.json()
                error_detail = f" | 错误详情: {json.dumps(error_response, ensure_ascii=False)}"
            except:
                error_detail = f" | 响应内容: {response.text[:200]}..."

            logger.warning(f"⚠️ 时间段请求失败 | {formatted_time} | 订单: {customer['order_id']} | "
                           f"日期: {selected_date} | 状态码: {response.status_code}{error_detail}")
            return []

    except Exception as e:
        logger.error(f"❌ 时间段请求异常 | 订单: {customer.get('order_id', 'Unknown')} | "
                     f"日期: {selected_date} | 错误: {e}")
        return []


def fetch_cf_bypass_params(proxy, type="schedule", logger_prefix="CF5s",):
    """优先从本地池获取CF过盾参数，失败再从服务器获取。
    返回 dict: {"user_agent": str, "cookie": str, 可选 "proxy": str}
    """
    try:
        # 1) 先尝试从本地Redis池获取
        try:
            pool_cf = get_random_cf_cookie(type)
        except Exception as pool_e:
            pool_cf = None
            logger.debug(f"[{logger_prefix}] 从池获取CF参数异常: {pool_e}")
        if isinstance(pool_cf, dict) and pool_cf.get("cookie") and pool_cf.get("user_agent"):
            ua = pool_cf.get("user_agent")
            cf_cookie = pool_cf.get("cookie")
            cf_proxy = pool_cf.get("proxy")
            logger.info(f"[{logger_prefix}] 使用池中CF参数 | has_proxy: {bool(cf_proxy)}")
            return {"user_agent": ua, "cookie": cf_cookie, "proxy": cf_proxy}

        # 2) 回退到服务器获取
        url = "http://47.254.131.227:5005/get_cookie"
        target = "https://lift-apicn.vfsglobal.com/appointment/CheckIsSlotAvailable"
        payload = {
            "url": target,
            "proxy": proxy,
            "token": "ashd91789h89j9idh17892hd98j90j"
        }
        logger.debug(f"[{logger_prefix}] 请求过盾参数(服务器) | proxy: {proxy if proxy else 'None'}")
        resp = requests.post(url, json=payload, timeout=90)
        if resp.status_code != 200:
            logger.warning(f"[{logger_prefix}] 获取过盾参数失败 | 状态码: {resp.status_code} | 文本: {resp.text[:200]}...")
            return None
        response_data = resp.json()
        data = response_data.get('data') if isinstance(response_data, dict) else None
        if data and isinstance(data, dict) and 'ua' in data and 'cookie' in data:
            ua = data.get('ua')
            cf_cookie = data.get('cookie')  # 即 cf_clearance
            logger.info(f"[{logger_prefix}] 过盾参数获取成功(服务器)")
            return {"user_agent": ua, "cookie": cf_cookie}
        logger.warning(f"[{logger_prefix}] 返回数据不完整 | 内容: {json.dumps(response_data)[:200]}...")
        return None
    except Exception as e:
        logger.error(f"[{logger_prefix}] 获取过盾参数异常: {e}")
        return None


def schedule_appointment(customer, login_user, random_slot, cf_params, r_auth=None):
    """提交预约（schedule），从时间段中随机选择的 slot 进行下单。金额固定为 0。"""
    try:
        formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 兼容不同时间段字段：allocationId 或 slotId
        allocation_id = random_slot.get('allocationId')
        slot_label = random_slot.get('slot')

        if not allocation_id:
            logger.warning(f"⚠️ 无法下单，缺少 allocationId/slotId | 订单: {customer.get('order_id')} | slot: {random_slot}")
            return None

        url = "https://lift-apicn.vfsglobal.com/appointment/schedule"

        # 先获取Cloudflare 5s盾过盾参数（由外部传入的 cf_params）
        ua_from_cf = cf_params.get("user_agent") if cf_params else None
        cf_clearance = cf_params.get("cookie") if cf_params else None

        ltsn = login_user.get('ltsn', '')
        cookie_header = ltsn if ltsn else ''
        if cf_clearance:
            cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"

        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": login_user.get("token"),
            "route": f"chn/zh/{customer['mission_code']}",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "cookie": cookie_header,
            "user-agent": (ua_from_cf or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        }

        # 调用费用接口，确认金额与币种
        fees_url = "https://lift-apicn.vfsglobal.com/appointment/fees"
        fees_headers = headers.copy()
        fees_data = {
            "missionCode": customer['mission_code'],
            "countryCode": "chn",
            "centerCode": customer['center_code'],
            "loginUser": login_user.get("email"),
            "urn": customer.get("urn"),
            "languageCode": "zh-CN"
        }
        # 如果使用远程账号(带有login_user.urn)，覆盖urn并追加原URN与账号信息
        if login_user.get('urn'):
            fees_data["urn"] = login_user.get('urn')
            fees_data["URN"] = customer.get("urn")
            fees_data["LogINUseR"] = customer.get("vfs_account")
        amount = 0
        currency = "CNY"
        # 如果cf_params来自池，则带有 proxy；优先使用该 proxy，以保证与cf_clearance一致
        cf_proxy = (cf_params.get("proxy") if cf_params else None)
        fees_proxies = {"http": cf_proxy, "https": cf_proxy} if cf_proxy else None
        try:
            fees_start_time = time.time()
            fees_resp = requests.post(
                fees_url,
                json=fees_data,
                headers=fees_headers,
                proxies=fees_proxies,
                impersonate=random.choice(['chrome136']),
                verify=False,
                timeout=30
            )
            fees_duration = round((time.time() - fees_start_time) * 1000, 2)
            logger.info(f"💰 费用API响应 | 订单: {customer.get('order_id')} | 状态码: {fees_resp.status_code} | 耗时: {fees_duration}ms")
            if fees_resp.status_code == 200:
                try:
                    fees_json = fees_resp.json()
                except Exception:
                    fees_json = None
                if isinstance(fees_json, dict):
                    logger.debug(f"💰 费用API完整响应 | 订单: {customer.get('order_id')} | 响应: {json.dumps(fees_json, ensure_ascii=False)}")
                    if fees_json.get('error') is None:
                        ta = fees_json.get('totalamount')
                        if isinstance(ta, (int, float)):
                            amount = ta
                        # 币种尝试从 feeDetails[0].currency 读取
                        try:
                            fd = fees_json.get('feeDetails') or []
                            if isinstance(fd, list) and fd:
                                curr = fd[0].get('currency')
                                if isinstance(curr, str) and curr:
                                    currency = curr
                        except Exception:
                            pass
            else:
                logger.warning(f"💰 费用API失败 | 订单: {customer.get('order_id')} | 内容: {fees_resp.text[:200]}...")
        except Exception as e:
            logger.error(f"💰 费用API异常 | 订单: {customer.get('order_id', 'Unknown')} | 错误: {e}")

        # 组装并提交schedule请求
        cf_proxy = (cf_params.get("proxy") if cf_params else None)
        proxies = {"http": cf_proxy, "https": cf_proxy} if cf_proxy else None

        logger.info(
            f"🧾 开始下单 | {formatted_time} | 订单: {customer.get('order_id')} | "
            f"国家: {customer.get('mission_code')} | 中心: {customer.get('center_code')} | "
            f"slot: {slot_label or 'Unknown'} | allocationId: {allocation_id} | 代理: {cf_proxy if cf_proxy else 'None'}"
        )
        schedule_appointment_data = {
            "missionCode": customer['mission_code'],
            "countryCode": "chn",
            "centerCode": customer['center_code'],
            "loginUser": login_user.get("email"),
            "urn": customer.get("urn"),
            "notificationType": "none",
            "paymentdetails": {
                "paymentmode": "Vac" if amount == 0 else "CBANK",
                "RequestRefNo": "",
                "clientId": "",
                "merchantId": "",
                "amount": amount,
                "currency":  currency if amount > 0 else None,
            },
            "allocationId": str(allocation_id),
            "CanVFSReachoutToApplicant": False,
        }
        # 如果使用远程账号(带有login_user.urn)，覆盖urn并追加原URN与账号信息
        if login_user.get('urn'):
            schedule_appointment_data["urn"] = login_user.get('urn')
            schedule_appointment_data["URN"] = customer.get("urn")
            schedule_appointment_data["LogINUseR"] = customer.get("vfs_account")
        print(schedule_appointment_data)
        start_time = time.time()
        response = requests.post(
            url,
            json=schedule_appointment_data,
            headers=headers,
            proxies=proxies,
            impersonate='chrome136',
            verify=False,
            timeout=90
        )
        dur_ms = round((time.time() - start_time) * 1000, 2)

        logger.info(
            f"🧾 下单响应 | 订单: {customer.get('order_id')} | 状态码: {response.status_code} | 耗时: {dur_ms}ms | 响应大小: {len(response.content)}字节"
        )

        if response.status_code == 200:
            try:
                result = response.json()
            except Exception:
                logger.warning(f"⚠️ 下单响应非JSON | 订单: {customer.get('order_id')} | 文本: {response.text[:200]}...")
                return None

            logger.debug(f"🧾 下单完整响应 | 订单: {customer.get('order_id')} | 响应: {json.dumps(result, ensure_ascii=False)}")

            if isinstance(result, dict):
                # 写入订单状态：需要付款=>waiting_for_payment；否则=>scheduled
                is_booked = bool(result.get("IsAppointmentBooked"))
                need_pay = bool(result.get("IsPaymentRequired"))
                if is_booked:
                    new_status = "waiting_for_payment" if need_pay else "wait_download_pdf"
                    update_ok = update_order_status_in_db(customer.get('order_id'), new_status)
                    logger.info(f"📝 已更新订单状态 | 订单: {customer.get('order_id')} | 需要付款: {need_pay} | 状态: {new_status} | DB结果: {update_ok}")
                    # 写入预约日期/时间/RequestRefNo（日期转换为yyyy-mm-dd）
                    appt_date_raw = result.get('appointmentDate')
                    appt_date = convert_ddmmyyyy_to_yyyy_mm_dd(appt_date_raw)
                    appt_time = result.get('appointmentTime')
                    # 兼容其它可能字段名
                    req_ref_no = result.get('RequestRefNo') or result.get('requestRefNo') or (
                        (result.get('paymentdetails') or {}).get('RequestRefNo') if need_pay else None
                    )
                    try:
                        update_order_appointment_info_in_db(customer.get('order_id'), appt_date, appt_time, req_ref_no)
                    except Exception as ie:
                        logger.warning(f"写入预约信息失败 | 订单: {customer.get('order_id')} | 错误: {ie}")
                    # 发送企微通知（预约成功）
                    try:
                        send_wecom_notify_for_order(customer.get('order_id'), customer, result)
                    except Exception as ne:
                        logger.warning(f"企微通知失败 | 订单: {customer.get('order_id')} | 错误: {ne}")
                    # 若需要付款，启动支付流程：解析表单->cashierOrder->生成二维码
                    if need_pay:
                        try:
                            pf_ok = process_payment_flow(result, customer, login_user, (cf_params.get("proxy") if cf_params else None), ua_from_cf, cookie_header)
                            logger.info(f"💳 支付流程触发 | 订单: {customer.get('order_id')} | 结果: {pf_ok}")
                        except Exception as pe:
                            logger.error(f"💳 支付流程异常 | 订单: {customer.get('order_id')} | 错误: {pe}")

                if result.get("error") is None and (is_booked or result.get("status") == "success"):
                    logger.info(
                        f"🎉 下单成功 | 订单: {customer.get('order_id')} | allocationId: {allocation_id} | slot: {slot_label} | 需要付款: {need_pay}"
                    )
                    return result
                else:
                    err_desc = result.get("error", {}).get("description") if isinstance(result.get("error"), dict) else result.get("error")
                    logger.info(
                        f"⚠️ 下单未成功 | 订单: {customer.get('order_id')} | allocationId: {allocation_id} | 错误: {err_desc}"
                    )
                    return result
            else:
                logger.warning(f"⚠️ 下单响应格式异常 | 订单: {customer.get('order_id')} | 响应类型: {type(result)}")
                return None
        else:
            try:
                error_response = response.json()
                error_detail = f" | 错误详情: {json.dumps(error_response, ensure_ascii=False)}"
            except Exception:
                error_detail = f" | 响应内容: {response.text[:200]}..."

            logger.warning(
                f"❌ 下单请求失败 | 订单: {customer.get('order_id')} | 状态码: {response.status_code}{error_detail}"
            )
            return None

    except Exception as e:
        logger.error(f"❌ 下单异常 | 订单: {customer.get('order_id', 'Unknown')} | 错误: {e}")
        return None


def parse_payment_form(html_text: str):
    """从支付页面HTML中解析form的action和所有input name/value。失败返回(None, {})."""
    try:
        # 查找第一个<form ... action="...">
        import re
        form_match = re.search(r"<form[^>]*action=\"([^\"]+)\"[^>]*>([\s\S]*?)</form>", html_text, re.IGNORECASE)
        if not form_match:
            return None, {}
        action = form_match.group(1)
        form_inner = form_match.group(2)
        inputs = re.findall(r"<input[^>]*name=\"([^\"]+)\"[^>]*value=\"([^\"]*)\"[^>]*>", form_inner, re.IGNORECASE)
        data = {name: value for name, value in inputs}
        return action, data
    except Exception as e:
        logger.error(f"解析支付表单失败: {e}")
        return None, {}


def http_request_with_retry(method, url, *, max_retries=3, backoff_factor=0.7,
                            retry_on_status=None, jitter=True, logger_prefix="HTTP", **kwargs):
    """
    轻量级请求重试封装：
    - 对网络异常重试
    - 对部分可恢复的HTTP状态码重试（默认：429/5xx/Cloudflare常见网关类520-524；403仅首轮重试一次）
    - 指数退避 + 抖动

    返回: 成功时返回响应对象；若所有尝试均为可重试的HTTP状态码，最后一次直接返回响应（交由上层沿用原逻辑处理）。
    抛出: 对网络异常在最后一次仍失败时抛出。
    """
    try:
        retry_on_status = set(retry_on_status or {429, 500, 502, 503, 504, 520, 521, 522, 523, 524})
        last_response = None
        for attempt in range(1, (max_retries or 1) + 1):
            start_t = time.time()
            try:
                resp = requests.request(method, url, **kwargs)
                dur_ms = int((time.time() - start_t) * 1000)
                status = getattr(resp, 'status_code', None)
                # 403 仅首轮重试一次（可能是CF偶发）
                if (status in retry_on_status) or (status == 403 and attempt == 1):
                    last_response = resp
                    if attempt < max_retries:
                        logger.warning(f"[{logger_prefix}] 可重试HTTP状态 | {method} {url[:200]} | 状态: {status} | 第{attempt}/{max_retries}次 | 耗时: {dur_ms}ms")
                        sleep_s = backoff_factor * (2 ** (attempt - 1))
                        if jitter:
                            sleep_s += random.uniform(0, 0.3)
                        time.sleep(sleep_s)
                        continue
                    else:
                        # 最后一轮仍是可重试HTTP状态，直接返回给上层处理，尽量保持原有分支逻辑
                        return resp
                # 正常返回
                return resp
            except Exception as e:
                dur_ms = int((time.time() - start_t) * 1000)
                if attempt < max_retries:
                    logger.warning(f"[{logger_prefix}] 请求异常 | {method} {url[:200]} | 第{attempt}/{max_retries}次 | 耗时: {dur_ms}ms | 错误: {e}")
                    sleep_s = backoff_factor * (2 ** (attempt - 1))
                    if jitter:
                        sleep_s += random.uniform(0, 0.3)
                    time.sleep(sleep_s)
                    continue
                # 最后一轮异常，抛出
                logger.warning(f"[{logger_prefix}] 最终失败 | {method} {url[:200]} | 耗时: {dur_ms}ms | 错误: {e}")
                raise
        # 正常不会到达此处，兜底返回
        return last_response
    except Exception:
        # 向上抛给调用者（调用方已有try/except包裹）
        raise


def generate_qr_base64_from_url(data_url: str):
    """本地生成二维码（JPEG）并返回base64字符串。失败返回None。"""
    try:
        import qrcode
        from qrcode.constants import ERROR_CORRECT_M
        from io import BytesIO
        from PIL import Image

        qr = qrcode.QRCode(
            version=None,  # 自动
            error_correction=ERROR_CORRECT_M,
            box_size=8,
            border=2,
        )
        qr.add_data(data_url)
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")

        # 统一缩放到300x300并转换为RGB以便保存为JPEG
        try:
            img = img.resize((300, 300), resample=Image.NEAREST)
        except Exception:
            pass
        try:
            img = img.convert("RGB")
        except Exception:
            pass

        buf = BytesIO()
        img.save(buf, format="JPEG", quality=90)
        b64 = base64.b64encode(buf.getvalue()).decode()
        return b64
    except Exception as e:
        logger.error(f"二维码生成异常: {e}")
        return None


def save_payment_qrcode(order_id: str, qr_b64: str):
    """将二维码Base64保存到 payment_qrcodes 表，按 order_id upsert。
    要求前缀为 data:image/jpeg;base64, 方便前端直接展示。
    """
    conn = None
    try:
        # 若无 data:image/ 前缀，则按要求补上 JPEG 前缀
        if isinstance(qr_b64, str) and not qr_b64.startswith("data:image/"):
            qr_b64 = "data:image/jpeg;base64," + qr_b64

        conn = get_db_connection()
        if not conn:
            logger.error(f"❌ 保存二维码失败，无法连接数据库 | 订单: {order_id}")
            return False
        with conn.cursor() as cursor:
            try:
                cursor.execute(
                    """
                    INSERT INTO payment_qrcodes (order_id, payment_qrcode, created_at, updated_at)
                    VALUES (%s, %s, NOW(), NOW())
                    ON CONFLICT (order_id) DO UPDATE
                    SET payment_qrcode = EXCLUDED.payment_qrcode,
                        updated_at = NOW()
                    """,
                    (order_id, qr_b64)
                )
            except Exception as e:
                # 如果表没有唯一约束，退回到先尝试更新，无则插入
                logger.warning(f"二维码UPSERT失败，尝试更新后插入 | 订单: {order_id} | 错误: {e}")
                cursor.execute("UPDATE payment_qrcodes SET payment_qrcode=%s, updated_at=NOW() WHERE order_id=%s", (qr_b64, order_id))
                if cursor.rowcount == 0:
                    cursor.execute("INSERT INTO payment_qrcodes (order_id, payment_qrcode, created_at, updated_at) VALUES (%s,%s,NOW(),NOW())", (order_id, qr_b64))
        conn.commit()
        logger.info(f"💾 二维码已保存 | 订单: {order_id}")
        return True
    except Exception as e:
        logger.error(f"❌ 保存二维码异常 | 订单: {order_id} | 错误: {e}")
        if conn:
            conn.close()
        return False


def get_operator_id_for_order(order_id: str):
    """获取订单的负责人/创建人ID。优先尝试operator，不存在则回退user_id。"""
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            logger.warning(f"无法获取DB连接以查询operator | 订单: {order_id}")
            return None
        with conn.cursor() as cur:
            # 优先尝试 operator
            try:
                cur.execute("SELECT operator FROM orders WHERE order_id = %s", (order_id,))
                row = cur.fetchone()
                if row and row[0]:
                    return row[0]
            except Exception:
                pass
            # 回退 user_id
            try:
                cur.execute("SELECT user_id FROM orders WHERE order_id = %s", (order_id,))
                row = cur.fetchone()
                if row and row[0]:
                    return row[0]
            except Exception:
                pass
        return None
    except Exception as e:
        logger.error(f"查询operator异常 | 订单: {order_id} | 错误: {e}")
        return None
    finally:
        if conn:
            conn.close()


def get_wecom_hooks_for_user(user_id: int):
    """查询用户的企业微信Hook列表。"""
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            logger.warning(f"无法获取DB连接以查询hook_url | 用户: {user_id}")
            return []
        with conn.cursor() as cur:
            cur.execute(
                """
                SELECT hook_url FROM user_hooks
                WHERE user_id = %s AND hook_url IS NOT NULL AND hook_url != ''
                """,
                (user_id,)
            )
            rows = cur.fetchall()
            return [r[0] for r in rows if r and r[0]]
    except Exception as e:
        logger.error(f"查询企业微信Hook失败 | 用户: {user_id} | 错误: {e}")
        return []
    finally:
        if conn:
            conn.close()


# 已迁移至 vfs_toolkit.send_wecom_notify_for_order，删除重复实现
    """预约成功后，随机挑选一个Hook发送企微消息。"""
    try:
        operator_id = get_operator_id_for_order(order_id)
        if not operator_id:
            logger.warning(f"未找到订单的operator_id/user_id，跳过发送企微消息 | 订单: {order_id}")
            return False
        hooks = get_wecom_hooks_for_user(operator_id)
        if not hooks:
            logger.warning(f"该用户无可用企微Hook，跳过通知 | 用户: {operator_id} | 订单: {order_id}")
            return False
        hook_url = random.choice(hooks)

        mission_code = customer.get('mission_code')
        center_code = customer.get('center_code')
        visa_code = customer.get('visa_code')
        mission_name = get_mission_name(mission_code)
        center_name = get_center_name(center_code, mission_code)
        visa_name = get_visa_name(mission_code, center_code, visa_code)
        name, passport = get_primary_client(order_id)

        content_lines = [f"订单 {order_id} 预约成功"]
        if name:
            content_lines.append(f"客户：{name}")
        if passport:
            content_lines.append(f"护照：{passport}")
        appt_date_raw = schedule_result.get('appointmentDate')
        appt_date_fmt = convert_ddmmyyyy_to_yyyy_mm_dd(appt_date_raw) or appt_date_raw
        content_lines.extend([
            f"国家：{mission_name}",
            f"中心：{center_name}",
            f"签证类型：{visa_name}",
            f"日期：{appt_date_fmt}",
            f"时间：{schedule_result.get('appointmentTime')}",
        ])
        content_lines.append("需支付：是" if bool(schedule_result.get('IsPaymentRequired')) else "需支付：否")
        text = "\n".join(content_lines)

        payload = {"msgtype": "text", "text": {"content": text}}
        resp = requests.post(hook_url, json=payload, timeout=10)
        logger.info(f"📨 企微通知已发送 | 订单: {order_id} | Hook: {hook_url[:60]}... | 状态码: {resp.status_code}")
        return True
    except Exception as e:
        logger.error(f"发送企微通知失败 | 订单: {order_id} | 错误: {e}")
        return False


# 已迁移至 vfs_toolkit.get_primary_client，删除重复实现


def process_payment_flow(schedule_result: dict, customer: dict, login_user: dict, proxy: str, ua_from_cf: str, cookie_header: str):
    """如需付款：拼接支付URL->抓取页面->解析表单->请求订单申请->调用cashierOrder->生成并保存二维码。"""
    try:
        if not schedule_result:
            return False
        need_pay = bool(schedule_result.get('IsPaymentRequired'))
        if not need_pay:
            return False

        pay_url = f"{schedule_result.get('URL')}?payLoad={schedule_result.get('payLoad')}"
        proxies = {"http": proxy, "https": proxy} if proxy else None
        headers = {
            "user-agent": ua_from_cf or "Mozilla/5.0",
            "cookie": cookie_header or "",
            "referer": "https://visa.vfsglobal.com/",
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        }
        # 获取支付HTML
        resp = http_request_with_retry(
            "GET", pay_url,
            headers=headers, proxies=proxies, verify=False, timeout=30, impersonate='chrome136',
            max_retries=3, backoff_factor=0.7, logger_prefix="PAY_HTML"
        )
        logger.info(f"🧾 支付页响应 | 订单: {customer.get('order_id')} | 状态码: {resp.status_code} | 长度: {len(resp.content)}")
        html_text = resp.text if resp and resp.text else ""
        action, form_data = parse_payment_form(html_text)
        if not action or not form_data:
            logger.warning(f"⚠️ 未能解析支付表单 | 订单: {customer.get('order_id')}")
            return False

        # 打印表单内容
        try:
            preview = {k: (v if k not in ['sign'] else v[:16] + '...') for k, v in form_data.items()}
            logger.info(f"🧾 解析到支付表单 | action: {action} | fields: {json.dumps(preview, ensure_ascii=False)}")
        except Exception:
            logger.info(f"🧾 解析到支付表单 | action: {action} | 字段数: {len(form_data)}")

        # 调用 orderApply（表单method为GET），禁止自动跳转以捕获302的Location
        # 对表单字段进行HTML反转义后再进行URL编码，避免特殊字符导致参数异常
        import html
        import urllib.parse
        unescaped = {k: (html.unescape(v) if isinstance(v, str) else v) for k, v in form_data.items()}
        qs = urllib.parse.urlencode(unescaped, doseq=True)
        order_apply_url = action + ("?" + qs if ("?" not in action) else "&" + qs)
        logger.info(f"🧾 orderApply URL 编码后预览: {order_apply_url}")
        order_apply_resp = http_request_with_retry(
            "GET", order_apply_url,
            headers=headers,
            proxies=proxies,
            verify=False,
            timeout=30,
            impersonate='chrome136',
            allow_redirects=False,
            max_retries=3,
            backoff_factor=0.7,
            logger_prefix="ORDER_APPLY"
        )
        logger.info(f"🧾 orderApply响应 | 状态码: {order_apply_resp.status_code} |  {order_apply_resp.content}")
        # 优先从302的Location中提取trxSerNo
        trx_ser_no = None
        try:
            if order_apply_resp.status_code in (301, 302):
                loc = order_apply_resp.headers.get('Location') or order_apply_resp.headers.get('location')
                if loc:
                    import urllib.parse
                    loc_decoded = urllib.parse.unquote(loc)
                    parsed = urllib.parse.urlparse(loc_decoded)
                    qs = urllib.parse.parse_qs(parsed.query)
                    trx_ser_no = (qs.get('trxSerNo') or qs.get('trxserno') or [None])[0]
                    merchant_no = (qs.get('merchantNo') or qs.get('merchantno') or [None])[0]
                    logger.info(f"🧾 从Location提取trxSerNo: {trx_ser_no} | merchantNo: {merchant_no} | Location: {loc_decoded[:200]}...")
                    # 读取到 trxSerNo 后也访问一次 Location 链接进行初始化
                    try:
                        response = http_request_with_retry(
                            "GET", loc,
                            headers=headers, proxies=proxies, verify=False, timeout=30, impersonate='chrome136',
                            max_retries=2, backoff_factor=0.7, logger_prefix="LOC_INIT"
                        )
                        logger.info(f"🧾 已访问Location初始化 | URL预览: {loc}  | 状态码: {response.status_code} | 长度: {len(response.content)}")
                    except Exception:
                        logger.warning("⚠️ 访问Location初始化失败，继续流程")

        except Exception as _:
            pass

        # 如果还未拿到，尝试JSON或文本兜底
        if not trx_ser_no:
            try:
                j = order_apply_resp.json()
                trx_ser_no = j.get('data', {}).get('trxSerNo') if isinstance(j, dict) else None
            except Exception:
                # 尝试从文本中抓取
                import re
                txt = order_apply_resp.text or ""
                m = re.search(r"trxSerNo[\"'\s:=]+([A-Z0-9_\-]+)", txt)
                if m:
                    trx_ser_no = m.group(1)

        # 如果有Location但仍未解析到，按提示再GET一次该URL并解析参数
        if not trx_ser_no and order_apply_resp.status_code in (301, 302):
            loc = order_apply_resp.headers.get('Location') or order_apply_resp.headers.get('location')
            if loc:
                import urllib.parse
                loc_decoded = urllib.parse.unquote(loc)
                http_request_with_retry(
                    "GET", loc_decoded,
                    headers=headers, proxies=proxies, verify=False, timeout=30, impersonate='chrome136',
                    max_retries=2, backoff_factor=0.7, logger_prefix="LOC_DECODED"
                )
                try:
                    parsed = urllib.parse.urlparse(loc_decoded)
                    qs = urllib.parse.parse_qs(parsed.query)
                    trx_ser_no = (qs.get('trxSerNo') or qs.get('trxserno') or [None])[0]
                except Exception:
                    pass

        if trx_ser_no:
            try:
                update_order_transaction_id(customer.get('order_id'), trx_ser_no)
            except Exception as ue:
                logger.warning(f"写入transaction_id失败 | 订单: {customer.get('order_id')} | 错误: {ue}")
        else:
            logger.warning(f"⚠️ 未能获取trxSerNo，跳过cashierOrder | 订单: {customer.get('order_id')}")
            return False

        # 在请求 getBcodePayType 之前，先查询订单状态进行初始化
        # try:
        #     if merchant_no and trx_ser_no:
        #         order_query_url = f"https://openmer.allinpaysz.com/prod-api/merchant-open/api/CbOrgTranPayController_orderQuery/{merchant_no}/{trx_ser_no}"
        #         oq_headers = {"content-type": "application/json"}
        #         oq_resp = requests.get(order_query_url, headers=oq_headers, timeout=30)
        #         logger.info(f"💳 orderQuery响应 | 状态码: {oq_resp.status_code} | 内容预览: {oq_resp.text[:500]}...")
        #     else:
        #         logger.warning(f"⚠️ 缺少merchantNo或trxSerNo，跳过orderQuery | merchantNo: {merchant_no} | trxSerNo: {trx_ser_no}")
        # except Exception as e:
        #     logger.warning(f"⚠️ orderQuery请求异常: {e}")

        # 在请求 cashierOrder 之前，先请求按业务线支付类型接口
        # try:
        #     bcode_url = "https://openmer.allinpaysz.com/prod-api/merchant-open/api/CbOrgTranPayController_getBcodePayTypeByBcode"
        #     bcode = (customer.get('mission_code') or '').upper()
        #     if merchant_no and bcode:
        #         bcode_payload = {"merchantNo": merchant_no, "bcode": bcode}
        #         bcode_headers = {"content-type": "application/json"}
        #         logger.info(f"💳 发送getBcodePayType请求 | merchantNo: {merchant_no} | bcode: {bcode}")
        #         bcode_resp = requests.post(bcode_url, json=bcode_payload, headers=bcode_headers, timeout=30)
        #         logger.info(f"💳 getBcodePayType响应 | 状态码: {bcode_resp.status_code} | 内容预览: {bcode_resp.text[:200]}...")
        #     else:
        #         logger.warning(f"⚠️ 缺少merchantNo或bcode，跳过getBcodePayType | merchantNo: {merchant_no} | bcode: {bcode}")
        # except Exception as e:
        #     logger.warning(f"⚠️ getBcodePayType请求异常: {e}")

        cashier_url = "https://openmer.allinpaysz.com/prod-api/merchant-open/api/CbOrgTranPayController_cashierOrder"
        cashier_payload = {
            "merchantNo": merchant_no,
            "orderNo": form_data.get("orderNo") or form_data.get("orderno") or "",
            "trxSerNo": trx_ser_no,
            "language": form_data.get("language", "en"),
            "collectFlag": 0,
            "payType": "WX_SCAN"
        }
        cashier_headers = {"content-type": "application/json"}
        cashier_resp = http_request_with_retry(
            "POST", cashier_url,
            json=cashier_payload, headers=cashier_headers, timeout=30,
            max_retries=2, backoff_factor=0.7, logger_prefix="CASHIER",
            retry_on_status={500, 502, 503, 504, 520, 521, 522, 523, 524}
        )
        logger.info(f"💳 cashierOrder响应 : {cashier_resp.text}")
        if cashier_resp.status_code == 200:
            try:
                j = cashier_resp.json()
            except Exception:
                j = None
            if not j:
                logger.warning(f"⚠️ cashierOrder响应非JSON | 订单: {customer.get('order_id')} | 响应: {cashier_resp.text}")
                return False
            pay_info = j.get('data', {}).get('payInfo') if isinstance(j, dict) else None
            if pay_info:
                qr_b64 = generate_qr_base64_from_url(pay_info)
                if qr_b64:
                    save_payment_qrcode(customer.get('order_id'), qr_b64)
                    return True
        return False
    except Exception as e:
        logger.error(f"支付流程异常 | 订单: {customer.get('order_id', 'Unknown')} | 错误: {e}")
        return False

    except Exception as e:
        logger.error(f"❌ 下单异常 | 订单: {customer.get('order_id', 'Unknown')} | 错误: {e}")
        return None


def vfs_calendar_scanner_worker():
    """VFS日历扫描工作器"""
    logger.info("VFS日历扫描工作器启动")

    while True:
        try:
            # 1. 获取有URN的VFS客户
            customers = get_vfs_customers_with_urns()
            if not customers:
                logger.info("⏳ 没有找到有URN的VFS客户，等待30秒...")
                time.sleep(30)
                continue

            # 2. 随机选择一个客户
            customer = random.choice(customers)
            logger.info(f"🎯 选择客户 | 订单: {customer['order_id']} | "
                        f"国家: {customer['mission_code']} | 中心: {customer['center_code']} | "
                        f"签证: {customer['visa_code']} | URN: {customer['urn'][:20]}...")

            # 3. 获取该客户的日期范围
            date_ranges = get_date_ranges_for_customer(customer)
            if not date_ranges:
                logger.info(f"⏳ 订单 {customer['order_id']} 没有找到日期范围数据，跳过...")
                time.sleep(1)
                continue

            # 加锁，避免同一客户并发扫描
            lock_token = acquire_customer_scan_lock(customer['order_id'], ttl_seconds=240)
            if not lock_token:
                # 已有线程扫描该客户，跳过
                time.sleep(1)
                continue

            try:
                # 4. 随机选择一个日期范围
                date_range = random.choice(date_ranges)
                # 4.1 若跨月，按月份拆分后再随机选择一段
                month_segments = split_date_range_by_month(date_range["start_date"], date_range["end_date"])
                # 保留原数据的其它字段（如 id, order_id）
                month_segments = [{**date_range, **seg} for seg in month_segments]
                if len(month_segments) > 1:
                    logger.debug(f"范围跨月，切分为 {len(month_segments)} 段: {[(s['start_date'], s['end_date']) for s in month_segments]}")
                date_range = random.choice(month_segments)
                logger.info(f"📅 选择日期范围 | 订单: {date_range['order_id']} | 范围: {date_range['start_date']} ~ {date_range['end_date']}")

                # 6. 获取该国家的登录用户
                login_user = get_login_user_for_country(customer['mission_code'], customer.get('vfs_account'))
                if not login_user:
                    logger.warning(f"国家 {customer['mission_code']} 没有可用的登录用户")
                    time.sleep(5)
                    continue

                # 7. 获取CF参数并统一代理
                cf_params_global = fetch_cf_bypass_params(None, 'normal')
                # 全局代理从 cf_params_global 中获取（如存在）
                proxy = (cf_params_global.get("proxy") if cf_params_global else None)
                _ = proxy  # 保持变量存在以兼容后续日志与参数

                # 8. 扫描日历可用日期
                scan_start_time = time.time()
                scan_result = scan_calendar_for_customer(customer, date_range, login_user, cf_params_global)
                scan_duration = round((time.time() - scan_start_time) * 1000, 2)

                # 解包返回结果
                if scan_result and len(scan_result) == 2:
                    available_dates, calendar_r_auth = scan_result
                else:
                    available_dates, calendar_r_auth = [], None

                if available_dates and calendar_r_auth:
                    # 9. 在客户接受范围内选择日期（取最早）
                    start_ymd = date_range['start_date']
                    end_ymd = date_range['end_date']
                    filtered_pairs = []  # (original_ddmmyyyy, ymd)
                    for d in available_dates:
                        ymd = convert_mmddyyyy_to_yyyy_mm_dd(d)
                        if ymd and start_ymd <= ymd <= end_ymd:
                            filtered_pairs.append((d, ymd))
                    if not filtered_pairs:
                        logger.info(f"❌ 可用日期均不在客户接受范围内 | 订单: {customer['order_id']} | 范围: {start_ymd} ~ {end_ymd} | 可选日期数: {len(available_dates)} | 日历扫描耗时: {scan_duration}ms")
                        continue
                    # 选择范围内最早的日期（按 yyyy-mm-dd 比较）
                    selected_date = min(filtered_pairs, key=lambda p: p[1])[1]
                    logger.info(f"📅 选择范围内最早日期 | 订单: {customer['order_id']} | 选择日期: {selected_date} | 范围: {start_ymd} ~ {end_ymd} | 可选日期数(范围内): {len(filtered_pairs)} | 日历扫描耗时: {scan_duration}ms")

                    # 10. 请求该日期的时间段（使用相同的r_auth和代理）
                    timeslot_start_time = time.time()
                    time_slots = request_time_slots_for_date(customer, selected_date, login_user, cf_params_global, calendar_r_auth)
                    timeslot_duration = round((time.time() - timeslot_start_time) * 1000, 2)

                    if time_slots:
                        logger.info(f"🎉 完整扫描成功 | 订单: {customer['order_id']} | "
                                    f"日期: {selected_date} | 时间段: {len(time_slots)}个 | "
                                    f"时间段扫描耗时: {timeslot_duration}ms | "
                                    f"总耗时: {scan_duration + timeslot_duration}ms")

                        # 从返回的时间段中随机挑选一个并尝试下单
                        random_slot = random.choice(time_slots)
                        schedule_start_time = time.time()
                        schedule_result = schedule_appointment(customer, login_user, random_slot, cf_params_global, calendar_r_auth)
                        schedule_duration = round((time.time() - schedule_start_time) * 1000, 2)

                        if schedule_result:
                            logger.info(f"✅ 下单调用完成 | 订单: {customer['order_id']} | 耗时: {schedule_duration}ms")
                        else:
                            logger.info(f"⚠️ 下单未成功 | 订单: {customer['order_id']} | 耗时: {schedule_duration}ms")
                    else:
                        logger.info(f"⚠️ 日期有效但无时间段 | 订单: {customer['order_id']} | "
                                    f"日期: {selected_date} | 时间段扫描耗时: {timeslot_duration}ms")
                else:
                    logger.info(f"❌ 日期范围内无可用日期 | 订单: {customer['order_id']} | "
                                f"范围: {date_range['start_date']} ~ {date_range['end_date']} | "
                                f"日历扫描耗时: {scan_duration}ms")
            finally:
                # 释放锁
                release_customer_scan_lock(customer['order_id'], lock_token)

            # 短暂休息
            rest_time = random.uniform(2, 5)
            logger.debug(f"💤 扫描完成，休息 {rest_time:.1f} 秒")
            time.sleep(rest_time)

        except Exception as e:
            logger.error(f"❌ VFS日历扫描工作器异常: {e}")
            time.sleep(5)


def show_scanner_statistics():
    """显示扫描器统计信息"""
    while True:
        try:
            time.sleep(300)  # 每5分钟显示一次统计

            # 获取VFS客户统计
            customers = get_vfs_customers_with_urns()
            date_ranges = get_all_date_ranges_from_db()

            logger.info("=" * 80)
            logger.info("VFS日历扫描器统计报告")
            logger.info("=" * 80)
            logger.info(f"有URN的VFS客户: {len(customers)}个")
            logger.info(f"数据库日期范围: {len(date_ranges)}个")
            logger.info(f"可用代理数量: {len(delegate)}")

            # 按国家分组统计客户
            country_stats = {}
            for customer in customers:
                country = customer['mission_code']
                if country not in country_stats:
                    country_stats[country] = []
                country_stats[country].append(customer)

            logger.info(f"涉及国家数量: {len(country_stats)}")

            # 显示各国家的客户数量
            for country, country_customers in country_stats.items():
                centers = set(c['center_code'] for c in country_customers)
                visas = set(c['visa_code'] for c in country_customers)
                logger.info(f"  🌍 {country}: {len(country_customers)}个客户 | "
                            f"中心: {list(centers)} | 签证类型: {list(visas)}")

            # 按订单分组统计日期范围
            order_stats = {}
            for dr in date_ranges:
                order_id = dr['order_id']
                if order_id not in order_stats:
                    order_stats[order_id] = []
                order_stats[order_id].append(dr)

            logger.info(f"涉及订单数量: {len(order_stats)}")

            # 显示前5个订单的日期范围
            for order_id, ranges in list(order_stats.items())[:5]:
                logger.info(f"  📋 订单 {order_id}: {len(ranges)}个日期范围")
                for r in ranges[:2]:  # 只显示前2个范围
                    logger.info(f"    📅 {r['start_date']} ~ {r['end_date']}")
                if len(ranges) > 2:
                    logger.info(f"    📅 ... 还有{len(ranges)-2}个范围")

            if len(order_stats) > 5:
                logger.info(f"  📋 ... 还有{len(order_stats)-5}个订单")

            logger.info("=" * 80)

        except Exception as e:
            logger.error(f"❌ 统计信息显示异常: {e}")


def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("VFS日历扫描器启动")
    logger.info("功能: 基于数据库客户和Redis URN数据扫描日历")
    logger.info("版本: v1.0")
    logger.info("=" * 60)

    try:
        # 启动配置刷新线程
        config_thread = threading.Thread(target=refresh_global_config, name="ConfigRefresh")
        config_thread.daemon = True
        config_thread.start()
        logger.info("配置刷新线程已启动")

        # 启动统计显示线程
        stats_thread = threading.Thread(target=show_scanner_statistics, name="Statistics")
        stats_thread.daemon = True
        stats_thread.start()
        logger.info("统计显示线程已启动")

        # 等待配置加载
        # time.sleep(5)

        # 启动多个扫描工作线程
        worker_count = 3  # 可以根据需要调整
        for i in range(worker_count):
            worker_thread = threading.Thread(target=vfs_calendar_scanner_worker, name=f"VFSScanWorker-{i+1}")
            worker_thread.daemon = True
            worker_thread.start()

        logger.info(f"已启动 {worker_count} 个VFS日历扫描工作线程")
        logger.info("系统进入运行状态，按 Ctrl+C 停止服务")

        # 主线程保持运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭服务...")
        logger.info("VFS日历扫描器已停止")
    except Exception as e:
        logger.error(f"主线程异常: {e}")
        logger.error("VFS日历扫描器异常退出")


if __name__ == "__main__":
    main()
