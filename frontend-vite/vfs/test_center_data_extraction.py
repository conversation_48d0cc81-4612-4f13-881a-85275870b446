# -*- coding: utf-8 -*-
"""
测试从 Redis 哈希 vfs_center_data 中提取国家/中心映射与叶子节点数据是否正确。
运行：python vfs/test_center_data_extraction.py
输出：
- Redis 哈希条目数量
- 解析后的根节点数量
- 扁平化后的“叶子节点”数量
- 动态生成的 missionCode->中文国名 与 centerCode->中文中心名 映射条目数，并打印前若干条示例
- 将提取结果保存为 vfs_center_data_flat.json 便于人工检查
"""
import json
import os
from typing import Any, Dict, List

try:
    # 优先复用工程内的 RedisClient（vfs/RedisClient.py）
    from RedisClient import RedisClient
    _rc = RedisClient()
    _redis = _rc.client
except Exception:
    # 兜底：直接使用 redis-py（需要安装 redis 包），使用本地默认连接
    import redis  # type: ignore
    _redis = redis.Redis(host='localhost', port=6379, db=0, password='TicketsCache#2023', decode_responses=True)


def extract_leaf_nodes(node: Dict[str, Any]) -> Dict[str, Any]:
    """按用户给定的规则清洗节点，保留关键字段，并递归处理 children。"""
    if isinstance(node, dict):
        children = node.get("data") or node.get("sub")
        cleaned = {
            k: node[k]
            for k in ("name", "missionCode", "centerCode", "code", "missionCodeName", "isoCode", "centerName")
            if k in node
        }
        if isinstance(children, list) and children:
            cleaned["children"] = [extract_leaf_nodes(child) for child in children]
        return cleaned
    return {}


def flatten_leaves(node: Dict[str, Any]) -> List[Dict[str, Any]]:
    """将树形结构扁平为叶子列表；若无 children 则该节点视为叶子。"""
    if not node:
        return []
    children = node.get("children")
    if not children:
        return [node]
    leaves: List[Dict[str, Any]] = []
    for ch in children:
        leaves.extend(flatten_leaves(ch))
    return leaves


def traverse_nodes(node: Dict[str, Any]) -> List[Dict[str, Any]]:
    """前序遍历所有节点（含非叶子），用于从任意层收集 mission/center 信息。"""
    if not node:
        return []
    nodes = [node]
    for ch in node.get("children", []) or []:
        nodes.extend(traverse_nodes(ch))
    return nodes


def build_maps_from_tree(roots: List[Dict[str, Any]]):
    """从树中构建映射：
    - mission_map 使用 missionCodeName
    - center_map 使用 isoCode + centerName（按你提供的数据结构）
    """
    mission_map: Dict[str, str] = {}
    center_map: Dict[str, str] = {}

    for root in roots:
        # 国家名
        mcode = root.get("missionCode")
        mname = root.get("missionCodeName")
        if mcode and mname:
            mission_map[mcode] = mname
        # 在整棵子树中收集含 isoCode/centerName 的节点作为中心
        for node in traverse_nodes(root):
            ccode = node.get("isoCode")
            cname = node.get("centerName")
            if ccode and cname:
                center_map[ccode] = cname.strip() if isinstance(cname, str) else cname
    return mission_map, center_map


def main():
    # 1) 读取 Redis 哈希
    raw = _redis.hgetall("vfs_center_data")
    print(f"Redis vfs_center_data 条目: {len(raw)}")
    if not raw:
        print("未读取到 vfs_center_data；请确认Redis中是否已写入该哈希")
        return

    # 2) 解析为列表
    try:
        parsed = [json.loads(val) for val in raw.values()]
    except Exception as e:
        print(f"解析 JSON 失败: {e}")
        return
    print(f"解析根节点数量: {len(parsed)}")

    # 3) 清洗并扁平化叶子
    cleaned = [extract_leaf_nodes(entry) for entry in parsed]
    leaves: List[Dict[str, Any]] = []
    for root in cleaned:
        leaves.extend(flatten_leaves(root))
    print(f"叶子节点数量: {len(leaves)}")

    # 4) 构建映射（严格来源：missionCodeName / centerName）
    mission_map, center_map = build_maps_from_tree(cleaned)
    print(f"mission映射数量: {len(mission_map)} | center映射数量: {len(center_map)}")

    # 5) 打印示例
    def preview(d: Dict[str, str], n: int = 10):
        items = list(d.items())[:n]
        for k, v in items:
            print(f"  {k} -> {v}")

    print("示例 mission 映射:")
    preview(mission_map)
    print("示例 center 映射:")
    preview(center_map)

    # 6) 保存到文件以人工检查
    out_path = os.path.join(os.path.dirname(__file__), "vfs_center_data_flat.json")
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump({
            "leaves": leaves,
            "mission_map": mission_map,
            "center_map": center_map,
        }, f, ensure_ascii=False, indent=2)
    print(f"已写入: {out_path}")


if __name__ == "__main__":
    main()
