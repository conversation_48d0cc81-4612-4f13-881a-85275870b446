# -*- coding: utf-8 -*-
"""
VFS日期扫描工作器
功能：扫描VFS系统的可用预约日期，并发布到Redis供其他工作器处理
"""
from curl_cffi import requests
import time
import json
import threading
from RedisClientAWS import RedisClient
import random
import string
import base64
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(threadName)-15s | %(funcName)-20s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 初始化Redis客户端
redis_client = RedisClient()

# 全局变量
scan_data = {}
scan_countries = []
rsa_string = ""
delegate = []

def get_current_timestamp():
    """获取当前时间戳"""
    now = datetime.now()
    return now.strftime("%Y-%m-%dT%H:%M:%S")

def format_rsa_string(compact_key: str) -> str:
    """格式化RSA字符串"""
    if not compact_key:
        return ""
    base64_content = compact_key.replace("|", "\n")
    return f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"

def encryption(text):
    """RSA加密"""
    try:
        if not rsa_string:
            return ""
        public_key = serialization.load_pem_public_key(rsa_string.encode())
        encrypted = public_key.encrypt(text.encode(), padding.PKCS1v15())
        return base64.b64encode(encrypted).decode()
    except Exception as e:
        logger.error(f"RSA加密失败: {e}")
        return ""

def generate_random_string(length=31):
    """生成随机字符串"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

def gen_proxy():
    """生成代理地址"""
    return f"http://kq123-zone-resi-region-in-session-{generate_random_string(10)}:<EMAIL>:16666"

def convert_date_format(date_str):
    """转换日期格式 YYYY-MM-DD -> DD/MM/YYYY"""
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        return date_obj.strftime("%d/%m/%Y")
    except Exception as e:
        logger.error(f"日期格式转换失败: {date_str}, 错误: {e}")
        return date_str

def is_date_between(check_date, start_date, end_date):
    """检查日期是否在指定范围内"""
    try:
        if not start_date or not end_date:
            return True
        
        check = datetime.strptime(check_date, "%Y-%m-%d")
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        
        return start <= check <= end
    except Exception as e:
        logger.error(f"日期比较失败: {e}")
        return False

def pick_random_elements(lst, count):
    """随机选择列表中的元素"""
    if not lst:
        return []
    return random.sample(lst, min(count, len(lst)))

def refresh_global_config():
    """刷新全局配置"""
    global scan_data, scan_countries, rsa_string, delegate
    
    while True:
        try:
            time.sleep(10)
            
            # 刷新RSA密钥
            source_rsa_str = redis_client.get('rsa_str')
            if source_rsa_str:
                rsa_string = format_rsa_string(source_rsa_str)
            
            # 刷新扫描数据
            scan_data_str = redis_client.get("scanData")
            if scan_data_str:
                scan_data = json.loads(scan_data_str)
                # 排除德国和意大利
                scan_countries = [country for country in scan_data.keys() 
                                if country not in ['deu', 'ita']]
            
            # 刷新代理列表
            delegate_str = redis_client.get("worker_proxy")
            if delegate_str:
                delegate = json.loads(delegate_str)
                
            logger.debug(f"配置刷新完成 | 国家数: {len(scan_countries)} | 代理数: {len(delegate)}")
            
        except Exception as e:
            logger.error(f"刷新全局配置失败: {e}")

def get_available_dates(scan_area, login_user, proxy):
    """获取可用日期"""
    try:
        # 计算扫描日期范围
        current_date = datetime.now()
        min_date = current_date + timedelta(days=1)  # 明天开始
        max_date = current_date + timedelta(days=90)  # 90天内
        
        # 生成随机授权头
        r_auth = f"EAAAAN{generate_random_string(597)}="
        
        url = "https://lift-apicn.vfsglobal.com/appointment/slots"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": login_user.get("token"),
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{scan_area.get('missionCode')}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": login_user.get('ltsn', '')
        }
        
        data = {
            "countryCode": "chn",
            "missionCode": scan_area.get("missionCode"),
            "centerCode": scan_area.get("centerCode"),
            "visaCategoryCode": scan_area.get("visaCategoryCode"),
            "loginUser": login_user.get("email"),
            "calendarId": scan_area.get("calendarId"),
            "fromDate": min_date.strftime("%Y-%m-%d"),
            "toDate": max_date.strftime("%Y-%m-%d"),
            "cultureCode": "zh-CN"
        }
        
        proxies = {"http": proxy, "https": proxy} if proxy else None
        
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
            timeout=30
        )
        
        formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if response.status_code == 200:
            result = response.json()
            
            if isinstance(result, dict) and result.get('calendars') and len(result.get('calendars')) > 0:
                # 提取可用日期
                available_dates = list(dict.fromkeys([item.get('date') for item in result.get('calendars')]))
                
                logger.info(f"扫描成功 | {formatted_time} | 日期范围: {min_date.strftime('%Y-%m-%d')} ~ {max_date.strftime('%Y-%m-%d')} | "
                          f"国家: {scan_area['missionCode']} | 领区: {scan_area.get('centerName', 'Unknown')} | "
                          f"签证类型: {scan_area.get('visaCategoryName', scan_area.get('visaCategoryCode'))} | "
                          f"可用日期: {len(available_dates)}个")
                
                # 缓存日期数据到Redis
                cache_key = f"{scan_area['missionCode']}Calendar"
                cache_value = {
                    "center": f"{scan_area['centerCode']}--{max_date.strftime('%Y-%m')}",
                    "days": available_dates,
                    "scan_time": formatted_time,
                    "scan_area": scan_area
                }
                redis_client.hset(cache_key, f"{scan_area['centerCode']}--{max_date.strftime('%Y-%m')}", 
                                json.dumps(cache_value))
                
                return available_dates
            else:
                logger.info(f"无可用日期 | {formatted_time} | 日期范围: {min_date.strftime('%Y-%m-%d')} ~ {max_date.strftime('%Y-%m-%d')} | "
                          f"国家: {scan_area['missionCode']} | 领区: {scan_area.get('centerName', 'Unknown')} | "
                          f"签证类型: {scan_area.get('visaCategoryName', scan_area.get('visaCategoryCode'))}")
                return []
        else:
            logger.warning(f"请求失败 | {formatted_time} | 状态码: {response.status_code} | "
                         f"国家: {scan_area['missionCode']} | 领区: {scan_area.get('centerName', 'Unknown')}")
            return []
            
    except Exception as e:
        logger.error(f"获取可用日期失败 | 扫描区域: {scan_area.get('missionCode', 'Unknown')} | 错误: {e}")
        return []

def process_available_dates(scan_area, available_dates, login_user):
    """处理可用日期，匹配用户并发布任务"""
    try:
        if not available_dates:
            return
        
        country = scan_area.get('missionCode')
        
        # 获取匹配的用户
        user_list = redis_client.hgetall(f"{country}UserDatas")
        if not user_list:
            logger.debug(f"国家 {country} 没有用户数据")
            return
        
        # 筛选匹配的用户
        matched_users = []
        for user_key, user_data_str in user_list.items():
            try:
                user = json.loads(user_data_str)
                if (user.get("centerCode") == scan_area["centerCode"] and
                    user.get("missionCode") == scan_area["missionCode"] and
                    user.get("visaTypeCode") == scan_area["visaCategoryCode"] and
                    (user.get("urn") or (user.get("wurn") and user.get('AVAILABLE') == True)) and
                    user.get("vip") != 5):
                    matched_users.append(user)
            except Exception as e:
                logger.error(f"解析用户数据失败: {e}")
                continue
        
        if not matched_users:
            logger.debug(f"没有匹配的用户 | 国家: {country} | 中心: {scan_area['centerCode']}")
            return
        
        # 匹配合适的日期
        suitable_dates = []
        for date in available_dates:
            for user in matched_users:
                if is_date_between(date, user.get("start_date"), user.get("end_date")):
                    suitable_dates.append(date)
                    break
        
        # 如果没有匹配的日期但允许占位，则使用所有日期
        if not suitable_dates and scan_area.get('seizeSeat') == True:
            suitable_dates = available_dates.copy()
        
        logger.info(f"日期匹配完成 | 国家: {country} | 可用日期: {len(available_dates)} | "
                   f"匹配用户: {len(matched_users)} | 合适日期: {len(suitable_dates)}")
        
        # 发布任务到Redis
        for date in pick_random_elements(suitable_dates, 3):  # 最多发布3个日期
            task_data = {
                **scan_area,
                "day": convert_date_format(date),
                "account": login_user.get('email'),
                "scan_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            redis_client.publish("common_publish_worker", json.dumps(task_data))
            logger.info(f"发布任务 | 日期: {date} | 国家: {country} | 中心: {scan_area['centerCode']}")
            
    except Exception as e:
        logger.error(f"处理可用日期失败: {e}")

def scan_dates_worker():
    """日期扫描工作器"""
    logger.info("日期扫描工作器启动")
    
    while True:
        try:
            if not scan_countries or not scan_data:
                logger.debug("等待配置数据加载...")
                time.sleep(5)
                continue
            
            # 随机选择一个国家
            country = random.choice(scan_countries)
            country_data = scan_data.get(country, {})
            
            if not country_data:
                time.sleep(1)
                continue
            
            # 随机选择一个扫描区域
            scan_areas = country_data.get('scanAreas', [])
            if not scan_areas:
                time.sleep(1)
                continue
            
            scan_area = random.choice(scan_areas)
            
            # 获取登录用户
            login_users = redis_client.hgetall(f"{country}LoginUser")
            if not login_users:
                logger.debug(f"国家 {country} 没有登录用户")
                time.sleep(5)
                continue
            
            # 随机选择一个登录用户
            login_user_email = random.choice(list(login_users.keys()))
            login_user = json.loads(login_users[login_user_email])
            
            # 选择代理
            proxy = random.choice(delegate) if delegate else None
            
            # 扫描可用日期
            available_dates = get_available_dates(scan_area, login_user, proxy)
            
            # 处理可用日期
            if available_dates:
                process_available_dates(scan_area, available_dates, login_user)
            
            # 短暂休息
            time.sleep(random.uniform(1, 3))
            
        except Exception as e:
            logger.error(f"日期扫描工作器异常: {e}")
            time.sleep(5)

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("VFS日期扫描工作器启动")
    logger.info("功能: 扫描VFS系统的可用预约日期")
    logger.info("版本: v1.0")
    logger.info("=" * 60)
    
    try:
        # 启动配置刷新线程
        config_thread = threading.Thread(target=refresh_global_config, name="ConfigRefresh")
        config_thread.daemon = True
        config_thread.start()
        logger.info("配置刷新线程已启动")
        
        # 等待配置加载
        time.sleep(5)
        
        # 启动多个扫描工作线程
        worker_count = 8  # 可以根据需要调整
        for i in range(worker_count):
            worker_thread = threading.Thread(target=scan_dates_worker, name=f"DateScanWorker-{i+1}")
            worker_thread.daemon = True
            worker_thread.start()
        
        logger.info(f"已启动 {worker_count} 个日期扫描工作线程")
        logger.info("系统进入运行状态，按 Ctrl+C 停止服务")
        
        # 主线程保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭服务...")
        logger.info("VFS日期扫描工作器已停止")
    except Exception as e:
        logger.error(f"主线程异常: {e}")
        logger.error("VFS日期扫描工作器异常退出")

if __name__ == "__main__":
    main()
