# -*- coding: utf-8 -*-
from curl_cffi import requests
import time
import json
import threading
from RedisClient import RedisClient
import random
import re
import os
import datetime
import uuid
# 导入数据库更新函数
try:
    from db_utils import update_order_by_email_activated
except ImportError:
    # 如果导入失败，定义一个空函数
    def update_order_by_email_activated(email, status="activated"):
        print(f"数据库更新函数不可用，跳过更新: {email}")
        return False

# 创建 RedisClient 实例
ita_redis_client = RedisClient()

delegate = json.loads(ita_redis_client.get("verify_proxy"))


def get_available_account_for_country(country):
    """获取指定国家的可用账号Token"""
    try:
        # 导入Token管理模块
        from token_manager import get_available_token_for_country

        # 先从Token缓存中获取
        token_data = get_available_token_for_country(country)
        if token_data:
            return token_data

        # 如果没有缓存的Token，尝试从Redis的vfs_accounts中获取
        all_accounts = ita_redis_client.hgetall("vfs_accounts")
        available_accounts = []

        for email, account_data_str in all_accounts.items():
            try:
                account_data = json.loads(account_data_str)
                if (account_data.get('missionCode') == country and
                    account_data.get('token') and
                        account_data.get('updateTokenTime')):
                    # 检查token是否在有效期内（6000秒内）
                    if int(time.time()) - account_data.get('updateTokenTime', 0) < 6000:
                        available_accounts.append(account_data)
            except json.JSONDecodeError:
                continue

        if available_accounts:
            return random.choice(available_accounts)
        else:
            print(f"没有找到国家 {country} 的可用账号")
            return None

    except Exception as e:
        print(f"获取国家 {country} 的可用账号失败: {e}")
        return None


def attempt_verify(user, max_retries=1000, retry_interval=0.1):
    for _ in range(max_retries):
        res = verify(user)
        if res:
            break
        time.sleep(retry_interval)


def attempt_active(user, max_retries=10, retry_interval=0.1):
    for _ in range(max_retries):
        res = active(user)
        if res:
            break
        time.sleep(retry_interval)


def verify(suit_user):
    try:
        ita_redis_client.hset('otped', suit_user["urn"], json.dumps({**suit_user,  "verify_time": int(time.time())}))
        current_time = datetime.datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        print(
            f"当前时间:{formatted_time} | 正在验证客户otp:{suit_user.get('chnname')} | 客户详情: {suit_user}"
        )

        # 从vfs_accounts中获取可用账号
        account = get_available_account_for_country(suit_user.get('missionCode'))
        if not account:
            print(f"无法获取国家 {suit_user.get('missionCode')} 的可用账号，验证失败")
            return False

        url = (
            "https://lift-api.vfsglobal.com/appointment/applicantotp"
            if suit_user.get("countryCode") == "gbr"
            else "https://lift-apicn.vfsglobal.com/appointment/applicantotp"
        )
        if suit_user.get("countryCode") == "gbr":
            headers = {
                "Content-Type": "application/json;charset=UTF-8",
                "Authorize": account["token"],
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "macOS",
                "origin": "https://visa.vfsglobal.com",
                "sec-fetch-site": "same-site",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://visa.vfsglobal.com/",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            }
        else:
            headers = {
                "Content-Type": "application/json;charset=UTF-8",
                "Authorize": account["token"],
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "macOS",
                "origin": "https://visa.vfsglobal.com",
                "sec-fetch-site": "same-site",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://visa.vfsglobal.com/",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            }
        data = {
            "urn": suit_user["urn"],
            "loginUser": account.get("email"),
            "LoGInUSer": suit_user["loginUser"],
            "missionCode": suit_user["missionCode"],
            "countryCode": (
                "gbr" if suit_user.get("countryCode") == "gbr" else "chn"
            ),
            "centerCode": suit_user["centerCode"],
            "captcha_version": "",
            "captcha_api_key": "",
            "OTP": suit_user.get("otp_code"),
            "otpAction": "VALIDATE",
            "cultureCode": "zh-CN",
        }
        if (not suit_user.get('refresh_urn_times') or int(suit_user.get('refresh_urn_times')) < 10) and suit_user.get('ips') != None and len(suit_user.get('ips')) > 0:
            proxy = random.choice(suit_user.get('ips'))
            if not suit_user.get('refresh_urn_times'):
                suit_user['refresh_urn_times'] = 1
            else:
                suit_user['refresh_urn_times'] = int(suit_user['refresh_urn_times']) + 1
            ita_redis_client.hset(
                "itaUserDatas",
                f"{suit_user['centerCode']}-{suit_user['passportNO']}-{suit_user['visaTypeCode']}",
                json.dumps({**suit_user}),
            )
        else:
            proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            timeout=5,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
        )
        if response.status_code == 200:
            res = response.json()
            if res.get("isOTPValidated") == True:
                suit_user["update_otp_time"] = int(time.time())
                if (
                    ita_redis_client.hget(
                        str(suit_user.get("redis_name")),
                        str(suit_user.get("redis_key")),
                    )
                    != None
                ):
                    ita_redis_client.hset(
                        str(suit_user.get("redis_name")),
                        str(suit_user.get("redis_key")),
                        json.dumps({**suit_user}),
                    )
                ita_redis_client.hdel("waitotp", suit_user.get("urn"))
                current_time = datetime.datetime.now()
                formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
                print(
                    f"{formatted_time}  客户{suit_user['chnname']}验证OTP成功, 耗时{int(time.time()) - suit_user.get('get_otp_time')}s"
                )
            else:
                print(
                    f"{formatted_time}  客户{suit_user['chnname']}验证OTP失败, {res.get('error')}s"
                )
            return True
        else:
            print(response.status_code)
        return False
    except Exception as e:
        print(e)
        return False


def active(user):
    suit_user = ita_redis_client.hget("waitactive", user.get("email"))
    if suit_user == None:
        return False
    else:
        suit_user = json.loads(suit_user)
        current_time = datetime.datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"当前时间:{formatted_time} | 正在激活邮箱:{user.get('email')}")
        try:
            url = (
                "https://lift-apicn.vfsglobal.com/user/activateemail"
                if suit_user.get("countryCode") == "chn"
                else "https://lift-api.vfsglobal.com/user/activateemail"
            )
            headers = {
                "Content-Type": "application/json;charset=UTF-8",
                "origin": "https://visa.vfsglobal.com",
                "referer": "http://visa.vfsglobal.com/",
            }
            data = {
                "token": user.get("url").split("q=")[1],
                "missioncode": suit_user.get("missionCode"),
                "countrycode": suit_user.get("countryCode"),
            }
            proxy = random.choice(delegate)
            proxies = {
                "http": proxy,
                "https": proxy,
            }
            response = requests.post(
                url,
                json=data,
                headers=headers,
                proxies=proxies,
                impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
                verify=False,
            )
            if response.status_code == 200:
                data = response.json()
                if (
                    data.get("error") == None
                    or data.get("error").get("description")
                    == "Account is already active"
                ):
                    print(f"邮箱:{user.get('email')}激活成功")
                    # 激活成功后，存储到vfs_accounts中
                    ita_redis_client.hset(
                        "vfs_accounts",  # 改为统一的vfs_accounts
                        str(suit_user.get("redis_key")),
                        json.dumps({**suit_user}),
                    )
                    ita_redis_client.hdel("waitactive", user.get("email"))

                    # 更新PostgreSQL数据库中的订单状态
                    try:
                        db_update_success = update_order_by_email_activated(user.get('email'), "registe_success")
                        if db_update_success:
                            print(f"数据库订单状态更新成功 | 邮箱: {user.get('email')}")
                        else:
                            print(f"数据库订单状态更新失败 | 邮箱: {user.get('email')}")
                    except Exception as e:
                        print(f"数据库订单状态更新异常 | 邮箱: {user.get('email')} | 异常: {str(e)}")

                    return True
                else:
                    print(data)
            else:
                print(response.status_code)
            return False
        except Exception as e:
            print(e)
            return False


def ready(_, user):
    user = json.loads(user)
    print(user)
    if user.get("type") == "otp":
        all_wait_verify = ita_redis_client.hgetall('waitotp')
        for suit_user in all_wait_verify:
            if suit_user.get('loginUser') == user.get('email'):
                suit_user['otp_code'] = user.get('code')
                thread = threading.Thread(target=attempt_verify, args=(suit_user,))
                thread.start()
    else:
        thread = threading.Thread(target=attempt_active, args=(user,))
        thread.start()


def refresh_proxy():
    while True:
        time.sleep(2)
        global delegate
        delegate = json.loads(ita_redis_client.get("fast_proxy"))


threading.Thread(target=refresh_proxy).start()

filename = os.path.basename(__file__)
match = re.search(r"verify_otp_worker_(\d+).py", filename)

if match:
    number = match.group(1)
    ita_redis_client.subscribe(f"verify_otp_worker_{number}", ready)
    print(f"verify_otp_worker{number}，正在待命")
    while True:
        time.sleep(0.1)
else:
    # 如果文件名不匹配，使用默认订阅
    ita_redis_client.subscribe("verify_otp_worker_vfs", ready)
    print("verify_otp_worker_vfs，正在待命")
    while True:
        time.sleep(0.1)
