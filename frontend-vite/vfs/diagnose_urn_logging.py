#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断URN创建日志问题
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_log_output():
    """分析日志输出问题"""
    print("=== 分析URN创建日志问题 ===")
    
    print("📋 从您提供的日志分析:")
    print("1. ✅ 系统启动正常")
    print("2. ✅ 发现需要创建URN的订单: 1个")
    print("3. ✅ 找到可用Token")
    print("4. ✅ 获取客户数据成功")
    print("5. ✅ 构建申请人数据成功")
    print("6. ❓ 缺少'订单URN创建成功'日志")
    print("7. ❌ 直接跳到OTP生成失败")
    
    print("\n🔍 可能的原因:")
    print("1. URN请求可能返回了200状态码")
    print("2. 但响应中可能没有'urn'字段或有'error'字段")
    print("3. 导致跳过了成功日志，直接进入错误处理")
    
    print("\n🛠️ 诊断步骤:")
    print("1. 检查URN API响应的完整内容")
    print("2. 验证响应中是否包含'urn'字段")
    print("3. 检查是否有'error'字段")
    print("4. 分析OTP生成失败的具体原因")


def check_urn_response_structure():
    """检查URN响应结构"""
    print("\n=== URN响应结构分析 ===")
    
    print("✅ 正常成功响应应该包含:")
    print("  {")
    print("    'urn': 'URN123456789',")
    print("    'error': null,")
    print("    'success': true,")
    print("    '... 其他字段'")
    print("  }")
    
    print("\n❌ 失败响应可能包含:")
    print("  {")
    print("    'error': {")
    print("      'description': '具体错误信息'")
    print("    },")
    print("    'urn': null")
    print("  }")
    
    print("\n🔍 需要检查的条件:")
    print("  1. response_data.get('error') is None")
    print("  2. response_data.get('urn') 存在且不为空")
    print("  3. 两个条件都满足才会输出成功日志")


def check_otp_generation_issue():
    """检查OTP生成问题"""
    print("\n=== OTP生成问题分析 ===")
    
    print("📋 从日志看到的OTP问题:")
    print("  '订单 2025080810332839712 未返回OTP生成方式'")
    print("  '订单 2025080810332839712 URN创建成功但OTP生成失败'")
    
    print("\n🔍 OTP响应分析:")
    print("  正常OTP响应应该包含:")
    print("  {")
    print("    'isOTPGenerated': true,")
    print("    'OTPGeneratedMethed': 'SMS' 或 'EMAIL',")
    print("    '... 其他字段'")
    print("  }")
    
    print("\n❓ 可能的问题:")
    print("  1. isOTPGenerated = true (OTP生成成功)")
    print("  2. 但 OTPGeneratedMethed 字段缺失或为空")
    print("  3. 这可能是VFS API的正常行为")
    
    print("\n✅ 修复建议:")
    print("  即使没有OTPGeneratedMethed，只要isOTPGenerated=true")
    print("  就应该认为OTP生成成功")


def suggest_debugging_steps():
    """建议调试步骤"""
    print("\n=== 调试建议 ===")
    
    print("🔧 立即可以做的:")
    print("  1. 重新运行系统，观察新增的调试日志")
    print("  2. 查看'订单 XXX URN请求响应'的完整内容")
    print("  3. 查看'订单 XXX OTP请求响应'的完整内容")
    
    print("\n📝 日志改进:")
    print("  已添加的调试日志:")
    print("  - 🎉 订单URN创建成功 (更明显的成功标记)")
    print("  - URN请求响应的完整内容")
    print("  - 🔐 开始为URN生成OTP (更明显的OTP开始标记)")
    print("  - OTP请求响应的完整内容")
    
    print("\n🎯 关键检查点:")
    print("  1. 确认URN确实创建成功")
    print("  2. 确认OTP是否真的需要生成")
    print("  3. 确认OTP生成失败是否影响整体流程")


def check_visa_config():
    """检查签证配置"""
    print("\n=== 签证配置检查 ===")
    
    print("📋 从日志看到的订单信息:")
    print("  国家: hun (匈牙利)")
    print("  中心: PEK (北京)")
    print("  签证类型: S1")
    
    print("\n❓ 需要确认:")
    print("  1. 匈牙利S1签证是否需要OTP验证?")
    print("  2. 签证配置中isApplicantOTPEnabled的值")
    print("  3. 如果不需要OTP，为什么会尝试生成?")
    
    try:
        from order_urn_manager import is_otp_required
        otp_required = is_otp_required("hun", "PEK", "S1")
        print(f"\n✅ OTP需求检查结果: {otp_required}")
    except Exception as e:
        print(f"\n❌ 无法检查OTP需求: {e}")


def main():
    """主诊断函数"""
    print("VFS URN创建日志诊断")
    print("=" * 50)
    
    analyze_log_output()
    check_urn_response_structure()
    check_otp_generation_issue()
    suggest_debugging_steps()
    check_visa_config()
    
    print("\n" + "=" * 50)
    print("📋 总结:")
    print("1. 已添加更详细的调试日志")
    print("2. 需要重新运行查看完整响应内容")
    print("3. OTP生成失败可能不影响URN创建成功")
    print("4. 建议检查签证配置的OTP需求")
    
    print("\n🚀 下一步:")
    print("1. 重新运行系统")
    print("2. 观察新的调试日志输出")
    print("3. 分析URN和OTP的完整响应")
    print("4. 根据响应内容进一步调整代码")


if __name__ == "__main__":
    main()
