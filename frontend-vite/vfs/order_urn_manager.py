# -*- coding: utf-8 -*-
"""
订单URN管理器 - 为PostgreSQL数据库中的订单创建和管理URN
"""
import os
from vfs_mappings import get_mission_name, get_center_name, get_visa_name
import threading
import time
import json
import random
import string
import base64
import logging
import os
from datetime import datetime
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from curl_cffi import requests
import psycopg2
from psycopg2.extras import RealDictCursor

# 导入现有模块
from RedisClient import RedisClient
from db_utils import get_db_connection
from vfs_calendar_scanner import fetch_cf_bypass_params
from common_remote_accounts import get_deu_account_from_remote, get_ita_account_from_remote
from vfs_toolkit import (
    get_clients_for_order,
    get_operator_id_for_order,
    get_wecom_hooks_for_user,
    send_wecom_notify_waitlist_success,
)


# 获取日志记录器（使用与主程序相同的logger）
logger = logging.getLogger('VFS_AccountManager')

# 初始化Redis客户端
redis_client = RedisClient()

# 全局变量
delegate = []             # 代理列表
rsa_string = ""           # RSA公钥字符串


# ===== 冷却与并发锁工具 =====

def set_order_cooldown(order_id: str, reason: str, ttl_seconds: int) -> bool:
    """为订单设置冷却期，使用Redis过期时间作为冷却控制。
    值包含: reason, until(Unix), set_at, ttl。
    """
    try:
        now = int(time.time())
        payload = {
            "reason": reason,
            "until": now + int(ttl_seconds),
            "set_at": now,
            "ttl": int(ttl_seconds),
        }
        key = f"urn_cooldown:{order_id}"
        redis_client.setex(key, int(ttl_seconds), json.dumps(payload))
        logger.info(f"设置订单冷却 | 订单: {order_id} | 原因: {reason} | TTL: {ttl_seconds}s")
        return True
    except Exception as e:
        logger.warning(f"设置订单冷却失败 | 订单: {order_id} | 异常: {e}")
        return False


def get_order_cooldown(order_id: str):
    """获取订单冷却信息；若过期则清理并返回None。"""
    try:
        key = f"urn_cooldown:{order_id}"
        raw = redis_client.get(key)
        if not raw:
            return None
        try:
            obj = json.loads(raw)
        except Exception:
            # 非预期内容，直接清理
            redis_client.delete(key)
            return None
        now = int(time.time())
        until = int(obj.get("until", 0))
        if until and now < until:
            return obj
        # 已过期，清理键
        redis_client.delete(key)
        return None
    except Exception as e:
        logger.warning(f"读取订单冷却失败 | 订单: {order_id} | 异常: {e}")
        return None


def acquire_urn_lock(order_id: str, ttl_seconds: int = 180):
    """尝试获取订单级别的分布式锁，成功返回token，失败返回None。"""
    try:
        token = generate_random_string(16)
        key = f"urn_lock:{order_id}"
        ok = redis_client.client.set(key, token, nx=True, ex=int(ttl_seconds))
        if ok:
            logger.debug(f"获取URN锁成功 | 订单: {order_id} | TTL: {ttl_seconds}s")
            return token
        logger.debug(f"获取URN锁失败(已被占用) | 订单: {order_id}")
        return None
    except Exception as e:
        logger.warning(f"获取URN锁异常 | 订单: {order_id} | 异常: {e}")
        return None


def is_urn_locked(order_id: str) -> bool:
    try:
        return bool(redis_client.get(f"urn_lock:{order_id}"))
    except Exception:
        return False


def get_retry_backoff_seconds(order_id: str) -> int:
    """指数退避：2m, 4m, 8m, ... 上限30m；计数器1小时自动过期。"""
    try:
        key = f"urn_retry_count:{order_id}"
        cnt = int(redis_client.client.incr(key))
        # 计数器1小时过期
        if cnt == 1:
            redis_client.client.expire(key, 3600)
        base = 120  # 2分钟
        ttl = base * (2 ** max(0, cnt - 1))
        return min(ttl, 1800)  # 上限30分钟
    except Exception as e:
        logger.warning(f"获取退避时间失败，使用默认2分钟 | 订单: {order_id} | 异常: {e}")
        return 120


def get_current_timestamp():
    """获取当前时间戳"""
    now = datetime.now()
    return now.strftime("%Y-%m-%dT%H:%M:%S")


def format_rsa_string(compact_key: str) -> str:
    """格式化RSA公钥字符串"""
    if not compact_key:
        return ""
    base64_content = compact_key.replace("|", "\n")
    return f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"


# 环境开关与常量（CHE 填表）
ENABLE_CHE_FORMS = str(os.getenv('ENABLE_CHE_FORMS', 'true')).lower() in {'1', 'true', 'yes', 'on'}
CHE_FORM_ID = 'SCHENGEN_1756107471108_615356'
VFS_API = 'https://lift-apicn.vfsglobal.com'
EFORMS_API = 'https://eu-api-app.vfsevisa.com'
LOCAL_FORM_SERVER = 'http://120.27.241.45:5005'


def encryption(text):
    """RSA加密"""
    try:
        if not rsa_string:
            logger.error("RSA公钥未初始化")
            return ""
        public_key = serialization.load_pem_public_key(rsa_string.encode())
        encrypted = public_key.encrypt(
            text.encode(),
            padding.PKCS1v15()
        )
        return base64.b64encode(encrypted).decode()
    except Exception as e:
        logger.error(f"RSA加密失败: {e}")
        return ""


def generate_random_string(length=31):
    """生成随机字符串"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))


def refresh_global_config():
    """刷新全局配置"""
    global delegate, rsa_string

    try:
        # 刷新代理列表
        proxy_data = redis_client.get("fast_proxy")
        if proxy_data:
            delegate = json.loads(proxy_data)
            logger.info(f"刷新代理列表，数量: {len(delegate)}")

        # 刷新RSA公钥
        source_rsa_str = redis_client.get('rsa_str')
        if source_rsa_str:
            rsa_string = format_rsa_string(source_rsa_str)
            logger.info("RSA公钥已刷新")

    except Exception as e:
        logger.error(f"刷新全局配置失败: {e}")


# 德国候补检查间隔（秒）
WAITLIST_CHECK_INTERVAL = 300
# 指定需要检查的德国中心
DEU_TARGET_CENTERS = {"GRSG", "GR NN", "GRHZ"}


def get_cached_urn(order_id, mission_code=None, center_code=None, visa_code=None):
    """从Redis获取缓存的URN。
    特殊规则：若为德国(deu)且该签证类型启用waitlist，则即便过期也不删除缓存并直接返回。
    """
    try:
        cache_key = f"vfs_urn:{order_id}"
        cached_data = redis_client.get(cache_key)
        is_deu_waitlist = False
        try:
            if mission_code and center_code and visa_code:
                is_deu_waitlist = (str(mission_code).lower() == 'deu') and bool(is_waitlist_enabled(mission_code, center_code, visa_code))
        except Exception as ie:
            logger.debug(f"waitlist 判断异常，按默认处理 | 订单: {order_id} | 错误: {ie}")
        if cached_data:
            urn_data = json.loads(cached_data)
            now = int(time.time())
            create_time = urn_data.get('createTime', 0)
            age = now - create_time

            # 检查是否过期（10000秒有效期）
            if age < 10000:
                remaining_time = 10000 - age
                logger.info(f"使用缓存URN | 订单: {order_id} | 剩余时间: {remaining_time}秒")
                return urn_data
            else:
                if is_deu_waitlist:
                    logger.info(f"德国waitlist场景：不过期删除，继续使用缓存URN | 订单: {order_id}")
                    return urn_data
                # 非德国waitlist：URN过期，删除缓存，并同步清空数据库的 URN
                redis_client.delete(cache_key)
                logger.info(f"缓存URN已过期 | 订单: {order_id}")
                try:
                    conn = get_db_connection()
                    if conn:
                        with conn.cursor() as cur:
                            cur.execute("UPDATE orders SET urn = NULL, updated_at=CURRENT_TIMESTAMP WHERE order_id=%s", (order_id,))
                            conn.commit()
                        conn.close()
                        logger.info(f"数据库已同步清空URN | 订单: {order_id}")
                except Exception as ue:
                    logger.warning(f"数据库清空URN失败 | 订单: {order_id} | 错误: {ue}")
        else:
            if is_deu_waitlist:
                logger.info(f"redis缓存不存在，德国waitlist场景：不操作删除，继续使用数据库URN | 订单: {order_id}")
                return None
            redis_client.delete(cache_key)
            logger.info(f"redis缓存URN不存在, 清空数据库的 URN | 订单: {order_id}")
            try:
                conn = get_db_connection()
                if conn:
                    with conn.cursor() as cur:
                        cur.execute("UPDATE orders SET urn = NULL, updated_at=CURRENT_TIMESTAMP WHERE order_id=%s", (order_id,))
                        conn.commit()
                    conn.close()
                    logger.info(f"数据库已同步清空URN | 订单: {order_id}")
            except Exception as ue:
                logger.warning(f"数据库清空URN失败 | 订单: {order_id} | 错误: {ue}")
        return None
    except Exception as e:
        logger.error(f"获取缓存URN失败 | 订单: {order_id} | 异常: {str(e)}")
        return None


# ==== 名称映射缓存（从 Redis 的 vfs_center_data 获取 mission/center 映射） ====
_MAPPING_TTL = 600
_mapping_cache = {"mission": {}, "center": {}}
_mapping_cache_time = 0


def _traverse_nodes(items):
    if not isinstance(items, list):
        return
    for it in items:
        yield it
        if isinstance(it, dict):
            # 支持两种结构: children/sub
            sub = it.get('sub') or it.get('children')
            if sub:
                for x in _traverse_nodes(sub):
                    yield x


def cache_urn(order_id, urn_data):
    """将URN缓存到Redis。
    德国 + waitlist: 过期时间 2*86400 + 3600 秒（两天+1小时）。
    默认: 10000 秒。
    """
    try:
        cache_key = f"vfs_urn:{order_id}"
        ttl_default = 10000
        ttl_deu_waitlist = 2*86400 + 3600
        try:
            mission_code = urn_data.get('mission_code')
            center_code = urn_data.get('center_code')
            visa_code = urn_data.get('visa_code')
            is_deu_waitlist = (str(mission_code).lower() == 'deu') and bool(is_waitlist_enabled(mission_code, center_code, visa_code))
        except Exception:
            is_deu_waitlist = False
        ttl = ttl_deu_waitlist if is_deu_waitlist else ttl_default
        redis_client.setex(cache_key, ttl, json.dumps(urn_data))
        logger.info(f"URN已缓存 | 订单: {order_id} | 有效期: {ttl}秒 | 德国候补: {is_deu_waitlist}")
        return True
    except Exception as e:
        logger.error(f"缓存URN失败 | 订单: {order_id} | 异常: {str(e)}")
        return False


def get_token_for_account(email, mission_code):
    """根据账号和国家从Redis获取Token数据，校验有效期后返回。"""
    try:
        cache_key = f"vfs_token:{email}:{mission_code}"
        cached = redis_client.get(cache_key)
        if not cached:
            logger.warning(f"未找到账号Token | 邮箱: {email} | 国家: {mission_code}")
            return None
        data = json.loads(cached)
        remain = 2000 - (int(time.time()) - data.get('updateTokenTime', 0))
        if remain <= 0:
            logger.warning(f"账号Token已过期 | 邮箱: {email} | 国家: {mission_code}")
            return None
        logger.info(f"使用账号Token | 邮箱: {email} | 国家: {mission_code} | 剩余: {remain}秒")
        return data
    except Exception as e:
        logger.error(f"获取账号Token失败 | 邮箱: {email} | 国家: {mission_code} | 异常: {str(e)}")
        return None


def get_orders_need_urn():
    """从PostgreSQL orders表中获取需要创建URN的订单列表"""
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("无法获取数据库连接")
            return []

        orders_need_urn = []

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查询需要创建URN的VFS订单
            query = """
                SELECT order_id, mission_code, center_code, visa_type, visa_code, travel_date, vfs_account, order_status
                FROM orders
                WHERE is_vfs_order = true
                AND mission_code IS NOT NULL
                AND mission_code != ''
                AND center_code IS NOT NULL
                AND center_code != ''
                AND visa_code IS NOT NULL
                AND visa_code != ''
                AND order_status  IN ('wait_registe', 'registe_success')
                AND (
                    LOWER(mission_code) = 'deu'
                    OR LOWER(mission_code) = 'ita'
                    OR order_status = 'registe_success'
                )
                AND (
                    LOWER(mission_code) = 'deu'
                    OR LOWER(mission_code) = 'ita'
                    OR (vfs_account IS NOT NULL AND vfs_account != '')
                )
            """
            cursor.execute(query)
            orders = cursor.fetchall()

            for order in orders:
                # 检查是否有缓存的URN
                cached_urn = get_cached_urn(order['order_id'], order['mission_code'], order['center_code'], order['visa_code'])
                logger.debug(f"订单 {order['order_id']} 缓存URN检查结果: {cached_urn}")
                if not cached_urn:
                    # 排除不需要创建URN的国家
                    excluded_countries = {'spain'}
                    if order['mission_code'].lower() not in excluded_countries:
                        orders_need_urn.append(dict(order))
                        logger.debug(f"订单需要URN | 订单: {order['order_id']} | 国家: {order['mission_code']}")

        conn.close()

        logger.info(f"订单URN需求分析完成 | 需要创建URN的订单数: {len(orders_need_urn)}")
        return orders_need_urn

    except Exception as e:
        logger.error(f"获取需要创建URN的订单列表失败 | 异常: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return []


def get_visa_config(mission_code, center_code, visa_code):
    """获取签证类型配置，判断是否需要OTP"""
    try:
        center_data_str = redis_client.get(f'vfs_center_data:{mission_code}')
        if not center_data_str:
            # 尝试从原始格式获取
            center_data_str = redis_client.hget('vfs_center_data', mission_code)
            if not center_data_str:
                logger.warning(f"未找到国家 {mission_code} 的中心数据")
                return None

        center_data = json.loads(center_data_str)

        # 遍历中心数据查找匹配的签证类型
        for center in center_data.get('data', []):
            if center.get('isoCode') == center_code:
                # 递归查找签证类型配置
                def find_visa_config(items):
                    for item in items:
                        if item.get('code') == visa_code:
                            return item
                        if 'sub' in item:
                            result = find_visa_config(item['sub'])
                            if result:
                                return result
                    return None

                visa_config = find_visa_config(center.get('sub', []))
                if visa_config:
                    return visa_config

        logger.warning(f"未找到签证类型配置: {mission_code}/{center_code}/{visa_code}")
        return None

    except Exception as e:
        logger.error(f"获取签证配置失败: {e}")
        return None


def is_otp_required(mission_code, center_code, visa_code):
    """判断指定签证类型是否需要OTP"""
    try:
        visa_config = get_visa_config(mission_code, center_code, visa_code)
        if visa_config:
            return visa_config.get('isApplicantOTPEnabled', False)
        return False
    except Exception as e:
        logger.error(f"判断OTP需求失败: {e}")
        return False


def build_cf_params_from_token(token_data: dict) -> dict:
    """从 token_data 构建 cf 参数字典（ITA 专用）。
    字段: cookie(cf_clearance), proxy(login_proxy), user_agent(cf_user_agent)
    返回缺省空字典供上层按需 fallback。
    """
    try:
        if not isinstance(token_data, dict):
            return {}
        cookie = (token_data.get("cf_clearance") or "").strip()
        proxy = (token_data.get("login_proxy") or "").strip()
        ua = (token_data.get("cf_user_agent") or "").strip()
        cf = {}
        if cookie:
            cf["cookie"] = cookie
        if proxy:
            cf["proxy"] = proxy
        if ua:
            cf["user_agent"] = ua
        return cf
    except Exception:
        return {}


def is_ocr_enabled(mission_code, center_code, visa_code):
    """判断指定签证类型是否需要OTP"""
    try:
        visa_config = get_visa_config(mission_code, center_code, visa_code)
        if visa_config:
            return visa_config.get('isOCREnable', False)
        return False
    except Exception as e:
        logger.error(f"判断OCR需求失败: {e}")
        return False


def is_waitlist_enabled(mission_code, center_code, visa_code):
    """判断指定签证类型是否启用等待列表"""
    try:
        visa_config = get_visa_config(mission_code, center_code, visa_code)
        if visa_config:
            return visa_config.get('iswaitlist', False)
        return False
    except Exception as e:
        logger.error(f"判断等待列表需求失败: {e}")
        return False


def insert_urn_error_message(order_id, error_message):
    """向order_messages表插入或更新URN创建错误消息，同时更新orders表状态"""
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("无法获取数据库连接")
            return False

        with conn.cursor() as cursor:
            # 1. 插入或更新错误消息
            message_query = """
                INSERT INTO order_messages (order_id, message, message_type, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT (order_id)
                DO UPDATE SET
                    message = EXCLUDED.message,
                    message_type = EXCLUDED.message_type,
                    status = EXCLUDED.status,
                    updated_at = CURRENT_TIMESTAMP
            """
            # message_type 和 status 都填写 "urn_create_error"
            cursor.execute(message_query, (order_id, error_message, "urn_create_error", "urn_create_error"))

            # 2. 更新orders表的order_status为urn_create_error
            order_query = """
                UPDATE orders
                SET order_status = 'urn_create_error', updated_at = CURRENT_TIMESTAMP
                WHERE order_id = %s
            """
            cursor.execute(order_query, (order_id,))

        conn.commit()
        logger.info(f"URN错误处理完成 | 订单: {order_id} | 错误: {error_message} | 状态: urn_create_error")
        return True

    except Exception as e:
        logger.error(f"URN错误处理失败 | 订单: {order_id} | 异常: {str(e)}")
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception:
                pass


def fetch_passport_image(passport_image: str) -> bytes:
    """下载护照图片，返回原始二进制；失败返回空字节串。"""
    try:
        if not passport_image:
            return b""
        url = f"http://120.27.241.45:5005/api/passport_image/{passport_image}"
        resp = requests.get(url, timeout=30)
        if resp.status_code == 200 and resp.content:
            return resp.content
        logger.warning(f"下载护照图片失败 | 文件: {passport_image} | 状态码: {resp.status_code}")
        return b""
    except Exception as e:
        logger.warning(f"下载护照图片异常 | 文件: {passport_image} | 异常: {e}")
        return b""


def upload_passport_ocr(file_b64: str, token_data: dict, mission_code: str, center_code: str, visa_code: str, cf_params: dict, selfi_b64: str = ""):
    """调用 VFS OCR 上传接口，返回 (json_dict, status_code)。"""
    try:
        r_auth = f"EAAAAN{generate_random_string(597)}="
        url = "https://lift-apicn.vfsglobal.com/appointment/UploadApplicantDocument"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": token_data.get("token", ""),
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{mission_code}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": token_data.get('ltsn', '')
        }
        # 合并 CF cookie 与 UA
        ua_from_cf = (cf_params or {}).get("user_agent")
        cf_clearance = (cf_params or {}).get("cookie")
        cookie_header = headers.get("cookie") or ""
        if cf_clearance:
            cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"
        headers["cookie"] = cookie_header
        headers["user-agent"] = ua_from_cf or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

        data = {
            "urn": "",
            "countryCode": "chn",
            "missionCode": mission_code,
            "loginUser": token_data.get("email"),
            "languageCode": "zh-CN",
            "centerCode": center_code,
            "visaCategoryCode": visa_code,
            "fileBytes": file_b64,
            "selfiImageFileBytes": selfi_b64 or ""
        }
        proxy = (cf_params or {}).get("proxy")
        if not proxy and delegate:
            proxy = random.choice(delegate)
        proxies = {"http": proxy, "https": proxy} if proxy else None

        resp = requests.post(url, json=data, headers=headers, proxies=proxies, impersonate='chrome136', verify=False, timeout=30)
        try:
            js = resp.json()
        except Exception:
            js = {"raw": str(resp.text)[:500]}
        return js, resp.status_code
    except Exception as e:
        logger.warning(f"OCR上传异常: {e}")
        return {"error": str(e)}, -1


def persist_ocr_guid(order_id: str, passport_no: str, guid: str, ttl_seconds: int = 7*24*3600) -> bool:
    try:
        if not (order_id and passport_no and guid):
            return False
        key = f"vfs_ocr_guid:{order_id}:{passport_no}"
        redis_client.setex(key, int(ttl_seconds), guid)
        logger.info(f"OCR GUID 已保存 | 订单: {order_id} | 护照: {passport_no[-4:]} | GUID: {guid[:8]}...")
        return True
    except Exception as e:
        logger.warning(f"保存 OCR GUID 失败 | 订单: {order_id} | 异常: {e}")
        return False


def persist_order_urn_and_account(order_id: str, urn_value: str, vfs_account_value: str, order_status: str):
    """将生成的 URN 和使用的 vfs_account 回写到 orders 表。"""
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("无法获取数据库连接，跳过回写")
            return False
        with conn.cursor() as cur:
            cur.execute(
                """
                UPDATE orders
                SET urn = %s, vfs_account = %s, updated_at = CURRENT_TIMESTAMP, order_status= %s
                WHERE order_id = %s
                """,
                (urn_value, vfs_account_value, order_status, order_id)
            )
            conn.commit()
        logger.info(f"订单 {order_id} 已回写 URN 和 vfs_account 到数据库")
        return True
    except Exception as e:
        logger.error(f"订单 {order_id} 回写数据库失败: {e}")
        return False
    finally:
        if conn:
            try:
                conn.close()
            except Exception:
                pass


def update_order_urn_to_redis(order_id, urn):
    """将订单URN信息更新到Redis(不写入数据库)"""
    try:
        # 这里可以根据需要将URN信息写入特定的Redis键
        # 例如：
        # 或者更新订单的完整信息
        # redis_client.hset('order_urns', order_id, urn)
        logger.info(f"订单URN信息已处理 | 订单: {order_id} | URN: {urn[:20]}... | 仅缓存到Redis")
        return True

    except Exception as e:
        logger.error(f"处理订单URN信息失败 | 订单: {order_id} | 异常: {str(e)}")
        return False


def delete_invalid_token(token_data):
    """删除失效的Token"""
    try:
        email = token_data.get("email")
        mission_code = token_data.get("missionCode")

        if not email or not mission_code:
            logger.warning("Token数据不完整，无法删除")
            return False

        # 从Redis删除Token
        cache_key = f"vfs_token:{email}:{mission_code}"
        redis_client.delete(cache_key)

        logger.info(f"已删除失效Token | 邮箱: {email} | 国家: {mission_code}")
        return True

    except Exception as e:
        logger.error(f"删除失效Token失败 | 异常: {str(e)}")
        return False


def generate_otp_for_urn(order_id, urn, mission_code, center_code, token_data, cf_params):
    """为URN生成OTP验证码"""
    try:
        logger.info(f"🔐 开始为URN生成OTP | 订单: {order_id} | URN: {urn[:20]}... | 国家: {mission_code} | 中心: {center_code}")

        # 生成随机授权头
        r_auth = f"EAAAAN{generate_random_string(597)}="

        url = "https://lift-apicn.vfsglobal.com/appointment/applicantotp"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": token_data["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{mission_code}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": token_data.get('ltsn', '')
        }

        data = {
            "urn": urn,
            "loginUser": token_data.get("email"),
            "lOGiNUser": token_data.get("email"),
            "missionCode": mission_code,
            "countryCode": "chn",
            "centerCode": center_code,
            "captcha_version": "",
            "captcha_api_key": "",
            "OTP": "",
            "otpAction": "GENERATE",
            "cultureCode": "zh-CN",
        }

        # 使用代理
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        # Cloudflare 参数：优先使用传入 cf_params；为空再回退池
        if not cf_params:
            cf_params = fetch_cf_bypass_params(None)
        ua_from_cf = cf_params.get("user_agent") if cf_params else None
        cf_clearance = cf_params.get("cookie") if cf_params else None
        cookie_header = headers.get("cookie") or ""
        if cf_clearance:
            cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"
        headers["cookie"] = cookie_header
        headers["user-agent"] = ua_from_cf or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

        cf_proxy = cf_params.get("proxy") if cf_params else None
        proxy = cf_proxy or proxy
        proxies = {"http": proxy, "https": proxy} if proxy else None

        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate='chrome136',
            verify=False,
            timeout=30
        )

        if response.status_code == 200:
            res = response.json()
            logger.info(f"订单 {order_id} OTP请求响应: {res}")

            if res.get("isOTPGenerated") == True:
                logger.info(f"订单 {order_id} 成功触发OTP生成")

                # 检查OTP发送方式（使用正确的字段名）
                otp_delivery_methods = res.get("otpDeliveryMethods", "")
                email_id = res.get("emailId", "")
                contact_number = res.get("contactNumber", "")
                expiry_minutes = res.get("otpExpiryInMinutes", 0)

                logger.info(f"订单 {order_id} OTP发送方式: {otp_delivery_methods}")
                logger.info(f"订单 {order_id} OTP发送到: 邮箱={email_id}, 手机={contact_number}")
                logger.info(f"订单 {order_id} OTP有效期: {expiry_minutes}分钟")

                if otp_delivery_methods:
                    # OTP生成成功并有发送方式，开始等待和验证OTP
                    logger.info(f"✅ 订单 {order_id} OTP生成成功 | 发送方式: {otp_delivery_methods}")

                    # 等待获取OTP验证码
                    # 等待获取OTP验证码（DEU走远程Redis，其它走本地）
                    otp_code = wait_for_otp_code(order_id, otp_delivery_methods, email_id, contact_number, mission_code)

                    if otp_code:
                        # 验证OTP（传递同一份CF参数）
                        otp_validated = validate_otp_for_urn(order_id, urn, otp_code, mission_code, center_code, token_data, cf_params)
                        if otp_validated:
                            logger.info(f"🎉 订单 {order_id} OTP验证成功，URN完全激活")
                            return True
                        else:
                            logger.error(f"订单 {order_id} OTP验证失败")
                            return False
                    else:
                        logger.error(f"订单 {order_id} 未能获取到OTP验证码")
                        return False
                else:
                    # OTP生成成功但没有返回发送方式，仍然认为是成功的
                    logger.warning(f"订单 {order_id} OTP生成成功但未返回发送方式")
                    return True
            else:
                logger.warning(f"订单 {order_id} OTP生成失败: {res}")
                return False
        else:
            logger.error(f"OTP请求失败 | 订单: {order_id} | 状态码: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"订单OTP生成异常 | 订单: {order_id} | 异常: {str(e)}")
        return False


def get_code_from_email(email, mission_code=None):
    """从Redis获取邮箱验证码；DEU 从远程Redis获取，其它国家从本地获取。"""
    try:
        otp_code = None
        source = "local"
        # 德国使用远程Redis
        if mission_code and str(mission_code).lower() == 'deu':
            try:
                remote = RedisClient(host='************', port=6379, password='TicketsCache#2023', db=0)
                otp_code = remote.get(email)
                source = "remote"
            except Exception as re:
                logger.warning(f"从远程Redis获取邮箱验证码失败，回退本地 | 邮箱: {email} | 错误: {re}")
        elif mission_code and str(mission_code).lower() == 'ita':
            try:
                remote = RedisClient(host='***********', port=6379, password='TicketsCache#2023', db=0)
                otp_code = remote.get(email)
                source = "remote"
            except Exception as re:
                logger.warning(f"从远程Redis获取邮箱验证码失败，回退本地 | 邮箱: {email} | 错误: {re}")
        # 回退本地或非DEU场景
        if not otp_code:
            otp_code = redis_client.get(email)
            source = "local"
        if otp_code:
            logger.debug(f"从{source}邮箱 {email} 获取到验证码: {otp_code}")
            return otp_code
        return ""
    except Exception as e:
        logger.error(f"从邮箱获取验证码失败 | 邮箱: {email} | 异常: {e}")
        return ""


def get_code_from_sms(phone):
    """从短信平台获取验证码"""
    try:
        logger.debug(f"尝试从短信平台获取验证码 | 手机尾号: {str(phone)[-4:] if phone else ''}")
        # 这里可以实现从短信平台获取验证码的逻辑
        # 目前返回空字符串
        return ""
    except Exception as e:
        logger.error(f"从短信平台获取验证码失败: {e}")
        return ""


def wait_for_otp_code(order_id, otp_delivery_methods, email_id, contact_number, mission_code):
    """等待获取OTP验证码；DEU 从远程Redis获取邮箱验证码，其它国家从本地获取。"""
    try:
        max_attempts = 30  # 最多等待5分钟 (30次 * 10秒)
        logger.info(f"订单 {order_id} 开始等待OTP验证码 | 发送方式: {otp_delivery_methods}")

        for attempt in range(max_attempts):
            otp_code = ""

            if "EMAIL" in otp_delivery_methods:
                # 从Redis获取邮箱验证码（按国家选择数据源）
                otp_code = get_code_from_email(email_id, mission_code)
            elif "SMS" in otp_delivery_methods:
                # 从短信平台获取验证码
                otp_code = get_code_from_sms(contact_number)

            if otp_code:
                logger.info(f"订单 {order_id} 获取到OTP验证码: {otp_code}")
                return otp_code

            # 等待10秒后重试
            time.sleep(10)
            logger.debug(f"订单 {order_id} 等待OTP验证码，第 {attempt + 1}/{max_attempts} 次尝试")

        logger.warning(f"订单 {order_id} 等待OTP验证码超时")
        return ""

    except Exception as e:
        logger.error(f"订单 {order_id} 等待OTP验证码失败: {e}")
        return ""


def validate_otp_for_urn(order_id, urn, otp_code, mission_code, center_code, token_data, cf_params):
    """验证URN的OTP验证码"""
    try:
        logger.info(f"订单 {order_id} 开始验证OTP | 验证码: {otp_code}")

        # 生成随机授权头
        r_auth = f"EAAAAN{generate_random_string(597)}="

        url = "https://lift-apicn.vfsglobal.com/appointment/applicantotp"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": token_data["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{mission_code}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": token_data.get('ltsn', '')
        }

        data = {
            "urn": urn,
            "loginUser": token_data.get("email"),
            "lOGiNUser": token_data.get("email"),
            "missionCode": mission_code,
            "countryCode": "chn",
            "centerCode": center_code,
            "captcha_version": "",
            "captcha_api_key": "",
            "OTP": otp_code,
            "otpAction": "VALIDATE",
            "cultureCode": "zh-CN",
        }

        # 使用代理（保留原随机代理作为后备）
        proxy = random.choice(delegate) if delegate else None
        proxies = {"http": proxy, "https": proxy} if proxy else None

        # 使用上层传入的 Cloudflare 过盾参数
        ua_from_cf = cf_params.get("user_agent") if cf_params else None
        cf_clearance = cf_params.get("cookie") if cf_params else None
        cookie_header = headers.get("cookie") or ""
        if cf_clearance:
            cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"
        headers["cookie"] = cookie_header
        headers["user-agent"] = ua_from_cf or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

        cf_proxy = cf_params.get("proxy") if cf_params else None
        proxy = cf_proxy or proxy
        proxies = {"http": proxy, "https": proxy} if proxy else None

        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate='chrome136',
            verify=False,
            timeout=30
        )

        if response.status_code == 200:
            res = response.json()
            logger.info(f"订单 {order_id} OTP验证响应: {res}")

            if res.get("isOTPValidated") == True:
                logger.info(f"✅ 订单 {order_id} OTP验证成功")
                return True
            else:
                logger.warning(f"订单 {order_id} OTP验证失败: {res}")
                return False
        else:
            logger.error(f"订单 {order_id} OTP验证请求失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"订单 {order_id} OTP验证异常: {str(e)}")
        return False

# ===== 瑞士（CHE）填表流程：内联实现（参考 common_fill_form） =====


def _pick_proxies(cf_params):
    proxy = None
    try:
        cf_proxy = cf_params.get("proxy") if cf_params else None
        proxy = cf_proxy or (random.choice(delegate) if delegate else None)
    except Exception:
        proxy = random.choice(delegate) if delegate else None
    return {"http": proxy, "https": proxy} if proxy else None


def _attach_cf_headers(headers, cf_params):
    try:
        ua_from_cf = cf_params.get("user_agent") if cf_params else None
        cf_clearance = cf_params.get("cookie") if cf_params else None
        cookie_header = headers.get("cookie") or ""
        if cf_clearance:
            cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"
        headers["cookie"] = cookie_header
        headers["user-agent"] = ua_from_cf or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    except Exception:
        headers.setdefault("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
    return headers


def fetch_che_applications(token_data, mission_code, center_code, cf_params):
    try:
        r_auth = f"EAAAAN{generate_random_string(597)}="
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": token_data["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{mission_code}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": token_data.get('ltsn', ''),
        }
        headers = _attach_cf_headers(headers, cf_params)
        proxies = _pick_proxies(cf_params)
        data = {
            "countryCode": "chn",
            "missionCode": mission_code,
            "centerCode": center_code,
            "loginUser": token_data.get("email"),
            "lOGinusEr": token_data.get("email"),
            "languageCode": "zh-CN",
        }
        resp = requests.post(
            f"{VFS_API}/appointment/application",
            json=data, headers=headers, proxies=proxies,
            impersonate='chrome136', verify=False, timeout=90
        )
        if resp.status_code != 200:
            logger.warning(f"[CHE] 获取applications失败 | 状态码: {resp.status_code}")
            return []
        body = resp.json()
        if body.get("error") is None:
            return body.get('data') or []
        return []
    except Exception as e:
        logger.warning(f"[CHE] 获取applications异常: {e}")
        return []


def get_handshake_che(token_data, mission_code, center_code, applicant_arn, cf_params):
    try:
        r_auth = f"EAAAAN{generate_random_string(597)}="
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": token_data["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{mission_code}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": token_data.get('ltsn', ''),
        }
        headers = _attach_cf_headers(headers, cf_params)
        proxies = _pick_proxies(cf_params)
        data = {
            "countryCode": "chn",
            "missionCode": mission_code,
            "centerCode": center_code,
            "loginUser": token_data.get("email"),
            "lOGinusEr": token_data.get("email"),
            "aurn": applicant_arn,
            "languageCode": "zh-CN",
        }
        resp = requests.post(
            f"{VFS_API}/appointment/gethandshake", json=data, headers=headers,
            proxies=proxies, impersonate='chrome136', verify=False, timeout=30
        )
        if resp.status_code != 200:
            logger.warning(f"[CHE] gethandshake失败 | 状态码: {resp.status_code}")
            return None
        body = resp.json()
        if body.get("handshakeURL") or body.get("handshakePayload"):
            return body
        return None
    except Exception as e:
        logger.warning(f"[CHE] gethandshake异常: {e}")
        return None


def external_login_eforms(handshake_payload, proxies, max_attempts: int = 10, delay_seconds: float = 1.0, timeout: int = 120):
    print(proxies)
    """调用 eforms external-login，带失败重试。

    重试策略：
    - 最大重试次数: max_attempts（默认 10 次）
    - 固定间隔: delay_seconds（默认 1s）
    - 可重试条件: 网络异常、HTTP 429、HTTP 5xx、返回体非 JSON、成功 200 但缺少 token
    - 不重试条件: 明确的 4xx 客户端错误（除 429）
    """
    try:
        if not handshake_payload:
            logger.warning("[CHE] external-login失败 | 原因: 缺少 handshake_payload")
            return None

        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "origin": "https://eforms.vfsglobal.com",
            "referer": "https://eforms.vfsglobal.com/",
            "accept": "application/json, text/plain, */*",
        }
        data = {
            "client": "che",
            "cultureCode": "zh-CN",
            "languageCode": "zh",
            "source": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "handshakePayload": handshake_payload,
        }

        last_error = None
        for attempt in range(1, int(max_attempts) + 1):
            try:
                resp = requests.post(
                    f"{EFORMS_API}/v1/api/auth/external-login",
                    json=data,
                    headers=headers,
                    proxies=proxies,
                    impersonate='chrome136',
                    verify=False,
                    timeout=timeout,
                )
                status = resp.status_code

                # 2xx
                if status == 200:
                    try:
                        body = resp.json()
                    except Exception as je:
                        last_error = f"JSON解析异常: {je}"
                        logger.warning(f"[CHE] external-login第{attempt}次失败 | {last_error} | 准备重试")
                        if attempt < max_attempts:
                            time.sleep(delay_seconds)
                            continue
                        break

                    token = (((body or {}).get('data') or {}).get('userContext') or {}).get('token')
                    if token:
                        return token
                    # 200 但缺失 token 视为可重试
                    last_error = "回包缺少 token"
                    logger.warning(f"[CHE] external-login第{attempt}次失败 | {last_error} | 准备重试")
                    if attempt < max_attempts:
                        time.sleep(delay_seconds)
                        continue
                    break

                # 429 或 5xx -> 可重试
                if status == 429 or 500 <= status < 600:
                    last_error = f"状态码: {status}"
                    logger.warning(f"[CHE] external-login第{attempt}次失败 | {last_error} | 准备重试")
                    if attempt < max_attempts:
                        time.sleep(delay_seconds)
                        continue
                    break

                # 其它 4xx -> 不重试
                if 400 <= status < 500:
                    logger.warning(f"[CHE] external-login失败 | 客户端错误 | 状态码: {status}")
                    return None

                # 其它非 2xx/4xx/5xx -> 作为临时错误重试
                last_error = f"异常状态码: {status}"
                logger.warning(f"[CHE] external-login第{attempt}次失败 | {last_error} | 准备重试")
                if attempt < max_attempts:
                    time.sleep(delay_seconds)
                    continue
                break

            except Exception as e:
                last_error = f"请求异常: {e}"
                logger.warning(f"[CHE] external-login第{attempt}次失败 | {last_error} | 准备重试")
                if attempt < max_attempts:
                    time.sleep(delay_seconds)
                    continue
                break

        logger.warning(f"[CHE] external-login最终失败 | 尝试次数: {max_attempts} | 最后错误: {last_error}")
        return None

    except Exception as e:
        logger.warning(f"[CHE] external-login异常: {e}")
        return None


def get_schengen_form_steps():
    try:
        resp = requests.get(
            f"{LOCAL_FORM_SERVER}/api/schengen/application/{CHE_FORM_ID}/steps",
            impersonate='chrome136', verify=False, timeout=30
        )
        if resp.status_code == 200:
            return resp.json()
        logger.warning(f"[CHE] 获取form steps失败 | 状态码: {resp.status_code}")
        return None
    except Exception as e:
        logger.warning(f"[CHE] 获取form steps异常: {e}")
        return None


def submit_six_steps(form_steps, eforms_token, webrefno, proxies):
    try:
        if not form_steps:
            logger.error(f"[CHE] 六步填表失败 | 原因: 缺少表单步骤 | webrefno={webrefno}")
            return False

        def _safe_text(t: str, limit: int = 800) -> str:
            try:
                if not isinstance(t, str):
                    t = str(t)
                return (t[:limit] + '...') if len(t) > limit else t
            except Exception:
                return "<unprintable>"

        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "origin": "https://eforms.vfsglobal.com",
            "referer": "https://eforms.vfsglobal.com/",
            "accept": "application/json, text/plain, */*",
            "authorization": eforms_token,
        }
        # Step1
        step1 = dict(form_steps.get('step1') or {})
        step1.update({"isDraft": False, "webRefNo": webrefno, "applicationAlphId": None, "applicantGroupAlphId": None, "stage": "passportInformation"})
        r1 = requests.post(f"{EFORMS_API}/v1/api/application/submit-eligibility", json=step1, headers=headers, proxies=proxies, impersonate='chrome136', verify=False, timeout=30)
        if r1.status_code != 200:
            logger.error(f"[CHE] Step1 失败 | webrefno={webrefno} | status={r1.status_code} | text={_safe_text(r1.text)}")
            return False
        b1 = r1.json()
        app_id = (((b1 or {}).get('data') or {}).get('applicationAlphId'))
        grp_id = (((b1 or {}).get('data') or {}).get('applicantGroupAlphId'))
        if not app_id:
            logger.error(f"[CHE] Step1 回包缺失 applicationAlphId | webrefno={webrefno} | body={_safe_text(r1.text)}")
            return False
        logger.info(f"[CHE] Step1 成功 | webrefno={webrefno} | app_id={app_id} | grp_id={grp_id}")

        # Step2
        step2 = dict(form_steps.get('step2') or {})
        step2.update({"isDraft": False, "applicationAlphId": app_id})
        r2 = requests.post(f"{EFORMS_API}/v1/api/passport", json=step2, headers=headers, proxies=proxies, impersonate='chrome136', verify=False, timeout=30)
        if r2.status_code != 200:
            logger.error(f"[CHE] Step2 失败 | webrefno={webrefno} | status={r2.status_code} | text={_safe_text(r2.text)}")
            return False
        logger.info(f"[CHE] Step2 成功 | webrefno={webrefno}")

        # Step3
        step3 = dict(form_steps.get('step3') or {})
        step3.update({"isDraft": False, "applicationAlphId": app_id, "applicantGroupAlphId": grp_id})
        r3 = requests.post(f"{EFORMS_API}/v1/api/applicant-details/submit-applicant-details", json=step3, headers=headers, proxies=proxies, impersonate='chrome136', verify=False, timeout=30)
        if r3.status_code != 200:
            logger.error(f"[CHE] Step3 失败 | webrefno={webrefno} | status={r3.status_code} | text={_safe_text(r3.text)}")
            return False
        logger.info(f"[CHE] Step3 成功 | webrefno={webrefno}")

        # Step4
        step4 = dict(form_steps.get('step4') or {})
        step4.update({"isDraft": False, "applicationAlphId": app_id})
        r4 = requests.post(f"{EFORMS_API}/v1/api/travel", json=step4, headers=headers, proxies=proxies, impersonate='chrome136', verify=False, timeout=30)
        if r4.status_code != 200:
            logger.error(f"[CHE] Step4 失败 | webrefno={webrefno} | status={r4.status_code} | text={_safe_text(r4.text)}")
            return False
        logger.info(f"[CHE] Step4 成功 | webrefno={webrefno}")

        # Step5
        step5 = dict(form_steps.get('step5') or {})
        step5.update({"isDraft": False, "applicationAlphId": app_id, "applicantGroupAlphId": grp_id})
        r5 = requests.post(f"{EFORMS_API}/v1/api/accommodation/submit-accommodation", json=step5, headers=headers, proxies=proxies, impersonate='chrome136', verify=False, timeout=30)
        if r5.status_code != 200:
            logger.error(f"[CHE] Step5 失败 | webrefno={webrefno} | status={r5.status_code} | text={_safe_text(r5.text)}")
            return False
        logger.info(f"[CHE] Step5 成功 | webrefno={webrefno}")

        # Step6
        step6 = dict(form_steps.get('step6') or {})
        step6.update({"isDraft": False, "applicationAlphId": app_id})
        r6 = requests.post(f"{EFORMS_API}/v1/api/additional-details/submit-additional-details", json=step6, headers=headers, proxies=proxies, impersonate='chrome136', verify=False, timeout=30)
        if r6.status_code != 200:
            logger.error(f"[CHE] Step6 失败 | webrefno={webrefno} | status={r6.status_code} | text={_safe_text(r6.text)}")
            return False
        b6 = r6.json() or {}
        code = (((b6.get('responseStatus') or {}).get('code')) if isinstance(b6, dict) else None)
        if code == 200:
            logger.info(f"[CHE] Step6 成功 | webrefno={webrefno} | code=200")
            return True
        else:
            logger.error(f"[CHE] Step6 失败 | webrefno={webrefno} | code={code} | body={_safe_text(r6.text)}")
            return False
    except Exception as e:
        logger.warning(f"[CHE] 提交六步异常: {e}")
        return False


def fill_che_forms_after_urn(order, urn, token_data, clients, cf_params):
    try:
        mission_code = order.get('mission_code')
        center_code = order.get('center_code')
        apps = fetch_che_applications(token_data, mission_code, center_code, cf_params)
        if not apps:
            logger.warning(f"[CHE] 未获取到 applications | 订单: {order.get('order_id')}")
            return False
        target_app = None
        for app in apps:
            if app.get('urn') == urn:
                target_app = app
                break
        if not target_app:
            logger.warning(f"[CHE] 未找到匹配本次URN的 application | 订单: {order.get('order_id')}")
            return False
        applicants = target_app.get('applicants') or []
        if not applicants:
            logger.info(f"[CHE] application 无申请人 | 订单: {order.get('order_id')}")
            return True
        proxies = _pick_proxies(cf_params)
        for ap in applicants:
            if str(ap.get('vafStatus')).lower() == 'completed':
                continue
            arn = ap.get('arn')
            if not arn:
                logger.warning(f"[CHE] applicant 无 arn，跳过")
                return False
            hs = get_handshake_che(token_data, mission_code, center_code, arn, cf_params)
            if not hs:
                return False
            token = external_login_eforms(hs.get('handshakePayload'), proxies)
            if not token:
                return False
            steps = get_schengen_form_steps()
            ok = submit_six_steps(steps, token, arn, proxies)
            if not ok:
                return False
        return True
    except Exception as e:
        logger.warning(f"[CHE] 填表流程异常: {e}")
        return False


def convert_date_format(date_input):
    """将日期转换为 dd/mm/yyyy 格式"""
    try:
        if not date_input:
            return ""

        from datetime import datetime, date

        # 如果输入是 datetime.date 对象
        if isinstance(date_input, date):
            return date_input.strftime("%d/%m/%Y")

        # 如果输入是字符串，尝试解析
        if isinstance(date_input, str):
            # 尝试解析 yyyy-mm-dd 格式
            date_obj = datetime.strptime(date_input, "%Y-%m-%d")
            return date_obj.strftime("%d/%m/%Y")

        # 如果是其他类型，尝试转换为字符串再处理
        date_str = str(date_input)
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        return date_obj.strftime("%d/%m/%Y")

    except Exception as e:
        logger.warning(f"日期格式转换失败: {date_input} (类型: {type(date_input)}) | 异常: {str(e)}")
        return ""


# 已迁移至 vfs_toolkit.get_clients_for_order（删除实现，使用工具模块）
# 保留占位以避免外部引用破坏；如无外部直接引用，可完全删除此定义。
# from vfs_toolkit import get_clients_for_order  # 建议外部直接 from vfs_toolkit 导入


def create_urn_for_order(order):
    """为指定订单创建URN（含幂等检查）"""
    try:
        order_id = order['order_id']
        mission_code = order['mission_code']
        center_code = order['center_code']
        visa_code = order['visa_code']
        # travel_date 可以为空，仅用于申请材料

        logger.info(f"开始为订单创建URN | 订单: {order_id} | 国家: {mission_code} | 中心: {center_code} | 签证类型: {visa_code}")

        # 先检查缓存的URN
        cached_urn = get_cached_urn(order_id, mission_code, center_code, visa_code)
        if cached_urn:
            # URN已存在于缓存中，无需重复创建
            logger.info(f"订单 {order_id} URN已存在于缓存中")
            return True

        # DB 幂等预检：确认orders表中URN是否已写入
        try:
            conn = get_db_connection()
            if conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute("SELECT urn FROM orders WHERE order_id = %s LIMIT 1", (order_id,))
                    row = cursor.fetchone()
                conn.close()
                if row and row.get('urn'):
                    logger.info(f"订单 {order_id} 数据库已存在URN，跳过创建")
                    return True
        except Exception as e:
            logger.warning(f"订单 {order_id} 数据库URN预检失败: {e}")

        # 获取客户信息（工具模块）
        clients = get_clients_for_order(order_id)
        if not clients:
            logger.warning(f"订单 {order_id} 没有客户信息")
            return False

        # 获取Token：德国(deu)走远程Redis，其它国家走本地账号Token
        if mission_code and mission_code.lower() == 'deu':
            logger.info(f"[DEU_REMOTE_ACCOUNT] 订单 {order_id} 使用远程账号创建URN")
            token_data = get_deu_account_from_remote()
            if not token_data:
                logger.warning(f"订单 {order_id} 德国远程账号获取失败，跳过URN创建")
                return False
            vfs_account = token_data.get('email')
        elif mission_code and mission_code.lower() == 'ita':
            logger.info(f"[ITA_REMOTE_ACCOUNT] 订单 {order_id} 使用远程账号创建URN")
            token_data = get_ita_account_from_remote()
            if not token_data:
                logger.warning(f"订单 {order_id} 德国远程账号获取失败，跳过URN创建")
                return False
            vfs_account = token_data.get('email')
        else:
            # 按订单账号获取Token
            vfs_account = order.get('vfs_account')
            if not vfs_account:
                # 兜底：从数据库读取账号
                try:
                    conn = get_db_connection()
                    if conn:
                        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                            cursor.execute("SELECT vfs_account FROM orders WHERE order_id = %s LIMIT 1", (order_id,))
                            row = cursor.fetchone()
                        conn.close()
                        vfs_account = row.get('vfs_account') if row else None
                except Exception as e:
                    logger.warning(f"订单 {order_id} 读取账号失败: {e}")

            if not vfs_account:
                logger.warning(f"订单 {order_id} 未绑定VFS账号，跳过URN创建")
                return False

            token_data = get_token_for_account(vfs_account, mission_code)
            if not token_data:
                logger.warning(f"订单 {order_id} 账号无有效Token，跳过URN创建 | 账号: {vfs_account}")
                return False

        # 检查Token获取时间，确保至少30秒后才能创建URN
        token_create_time = token_data.get('updateTokenTime', 0)
        current_time = int(time.time())
        time_since_token = current_time - token_create_time

        if time_since_token < 30:
            wait_time = 30 - time_since_token
            logger.info(f"订单 {order_id} Token获取时间不足30秒 | 已等待: {time_since_token}秒 | 还需等待: {wait_time}秒")
            time.sleep(wait_time)
            logger.info(f"订单 {order_id} 等待完成，开始创建URN")
        else:
            logger.info(f"订单 {order_id} Token获取时间充足 | 已等待: {time_since_token}秒 | 可以创建URN")

        # 生成随机授权头
        r_auth = f"EAAAAN{generate_random_string(597)}="

        url = "https://lift-apicn.vfsglobal.com/appointment/applicants"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": token_data["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{mission_code}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": token_data.get('ltsn', '')
        }
        # Cloudflare 参数：ITA 优先使用 token 内参数，其他国家走池
        cf_params = build_cf_params_from_token(token_data) if (mission_code and str(mission_code).lower() == "ita") else fetch_cf_bypass_params(None)
        ua_from_cf = cf_params.get("user_agent") if cf_params else None
        cf_clearance = cf_params.get("cookie") if cf_params else None
        cookie_header = headers.get("cookie") or ""
        if cf_clearance:
            cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"
        headers["cookie"] = cookie_header
        headers["user-agent"] = ua_from_cf or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

        # OCR 前置（若启用）：对每位客户必须拿到 uploadDocumentGUID
        applicant_list = []
        ocr_required = is_ocr_enabled(mission_code, center_code, visa_code)
        if ocr_required:
            logger.info(f"订单 {order_id} 启用OCR前置校验 | 国家: {mission_code} | 中心: {center_code} | 签证: {visa_code}")
            # 预取 CF 参数用于 OCR 上传（与后续共用）
            cf_params_ocr = cf_params
            for client in clients:
                passport_no = client.get("passport", "") or ""
                passport_image = client.get("passport_image", "") or ""
                if not passport_image:
                    # 无图片 -> 提示上传
                    error_msg = "请上传护照图片后点击重新安排预约"
                    # 入库前检查deleted
                    try:
                        conn = get_db_connection()
                        if conn:
                            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                                cur.execute("SELECT order_status FROM orders WHERE order_id=%s LIMIT 1", (order_id,))
                                row = cur.fetchone()
                            conn.close()
                            if row and str(row.get("order_status", "")).lower() == "deleted":
                                logger.info(f"订单 {order_id} 状态为 deleted，跳过错误信息入库并结束流程")
                                return False
                    except Exception as se:
                        logger.warning(f"订单 {order_id} 状态检查失败，继续错误信息入库 | 异常: {se}")
                    insert_urn_error_message(order_id, error_msg)
                    return False
                # 查是否已有GUID
                cached_guid = redis_client.get(f"vfs_ocr_guid:{order_id}:{passport_no}")
                if cached_guid:
                    # 命中缓存的 GUID，补充到 client 结构；若缺少base64则下载补齐
                    client["uploadDocumentGUID"] = cached_guid
                    client["GUID"] = cached_guid
                    client["applicantImage"] = passport_image
                    if not client.get("applicantImageData"):
                        _img_bytes = fetch_passport_image(passport_image)
                        client["applicantImageData"] = base64.b64encode(_img_bytes).decode() if _img_bytes else ""
                    continue
                # 下载并上传（非200重试3次）
                img_bytes = fetch_passport_image(passport_image)
                file_b64 = base64.b64encode(img_bytes).decode() if img_bytes else ""
                attempts = 3
                last_status = None
                last_json = None
                for i in range(attempts):
                    js, status = upload_passport_ocr(file_b64, token_data, mission_code, center_code, visa_code, cf_params_ocr)
                    last_status, last_json = status, js
                    if status == 200:
                        break
                    wait = (i + 1) * 2
                    logger.info(f"OCR上传非200 | 尝试 {i+1}/{attempts} | 状态码: {status} | 等待 {wait}s 重试")
                    time.sleep(wait)
                if last_status != 200:
                    # 三次都非200 -> 识别失败
                    error_msg = "护照识别失败，请确保护照四角齐全，不要遮挡任何文字，特别是最下面两行字符一定要完整清晰"
                    try:
                        conn = get_db_connection()
                        if conn:
                            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                                cur.execute("SELECT order_status FROM orders WHERE order_id=%s LIMIT 1", (order_id,))
                                row = cur.fetchone()
                            conn.close()
                            if row and str(row.get("order_status", "")).lower() == "deleted":
                                logger.info(f"订单 {order_id} 状态为 deleted，跳过错误信息入库并结束流程")
                                return False
                    except Exception as se:
                        logger.warning(f"订单 {order_id} 状态检查失败，继续错误信息入库 | 异常: {se}")
                    insert_urn_error_message(order_id, error_msg)
                    return False
                # 解析200响应
                if last_json and (last_json.get("error") is None) and last_json.get("uploadDocumentGUID"):
                    guid = last_json.get("uploadDocumentGUID")
                    # 写入 client 结构以供构建 applicant 使用
                    client["uploadDocumentGUID"] = guid
                    client["GUID"] = guid
                    client["applicantImage"] = passport_image
                    client["applicantImageData"] = file_b64
                    persist_ocr_guid(order_id, passport_no, guid)
                else:
                    # 有error或无GUID
                    error_msg = "护照识别失败，请确保护照四角齐全，不要遮挡任何文字，特别是最下面两行字符一定要完整清晰"
                    try:
                        conn = get_db_connection()
                        if conn:
                            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                                cur.execute("SELECT order_status FROM orders WHERE order_id=%s LIMIT 1", (order_id,))
                                row = cur.fetchone()
                            conn.close()
                            if row and str(row.get("order_status", "")).lower() == "deleted":
                                logger.info(f"订单 {order_id} 状态为 deleted，跳过错误信息入库并结束流程")
                                return False
                    except Exception as se:
                        logger.warning(f"订单 {order_id} 状态检查失败，继续错误信息入库 | 异常: {se}")
                    insert_urn_error_message(order_id, error_msg)
                    return False

        # 构建申请人数据（使用真实客户数据）
        for client in clients:
            # 处理性别：男=1，女=2
            gender = 1 if client.get("gender") == "男" else 2
            # 处理国籍代码
            nationality_code = client.get("nationality", "CHN")
            if nationality_code.lower() == "china" or nationality_code.lower() == "中国":
                nationality_code = "CHN"

            # 转换日期格式
            dob_formatted = convert_date_format(client.get("dob"))
            passport_expire_formatted = convert_date_format(client.get("passport_expire"))
            travel_date_formatted = convert_date_format(order.get('travel_date')) if order.get('travel_date') else ""
            applicant = {
                "urn": "",
                "arn": "",
                "loginUser": token_data.get("email"),
                "firstName": client.get("firstname_pinyin", ""),
                "middleName": "",
                "lastName": client.get("surname_pinyin", ""),
                "salutation": "",
                "gender": gender,
                "nationalId": None,
                "VisaToken": None,
                "contactNumber": str(token_data.get("phone", "")),
                "dialCode": "86",
                "passportNumber": client.get("passport", ""),
                "confirmPassportNumber": client.get("passport", ""),
                "passportExpirtyDate": passport_expire_formatted,
                "dateOfBirth": dob_formatted,
                "emailId": token_data.get("email"),
                "nationalityCode": nationality_code,
                "state": None,
                "city": None,
                "isEndorsedChild": False,
                "applicantType": 0,
                "addressline1": None,
                "addressline2": None,
                "pincode": None,
                "referenceNumber": None,
                "vlnNumber": None,
                "applicantGroupId": 0,
                "parentPassportNumber": "",
                "parentPassportExpiry": "",
                "dateOfDeparture": travel_date_formatted,
                "helloVerifyNumber": "",
                "gwfNumber": "",
                "entryType": "",
                "eoiVisaType": "",
                "isAutoRefresh": True,
                "passportType": "",
                "employerContactNumber": "",
                "employerDialCode": "",
                "employerEmailId": "",
                "employerFirstName": "",
                "employerLastName": "",
                "familyReunificationCerificateNumber": "",
                "vfsReferenceNumber": "",
                "PVRequestRefNumber": "",
                "PVStatus": "",
                "PVStatusDescription": "",
                "PVCanAllowRetry": True,
                "PVisVerified": False,
                "ipAddress": None,
                "GUID": client.get("GUID", ""),
                "applicantImage": client.get("applicantImage", ""),
                "applicantImageData": client.get("applicantImageData", ""),
            }
            applicant_list.append(applicant)

            logger.info(f"添加申请人 | 姓名: {client.get('name')} | 护照: {client.get('passport')} | 生日: {dob_formatted} | 护照过期: {passport_expire_formatted}")

        logger.info(f"构建申请人列表完成 | 订单: {order_id} | 申请人数: {len(applicant_list)}")
        # 检查是否启用等待列表
        waitlist_enabled = is_waitlist_enabled(mission_code, center_code, visa_code)
        # 构建请求数据
        request_data = {
            "countryCode": "chn",
            "missionCode": mission_code,
            "centerCode": center_code,
            "loginUser": token_data.get("email"),
            "visaCategoryCode": visa_code,
            "isEdit": False,
            "feeEntryTypeCode": None,
            "feeExemptionTypeCode": None,
            "feeExemptionDetailsCode": None,
            "applicantList": applicant_list,
            "languageCode": "zh-CN",
            "isWaitlist": waitlist_enabled,
        }
        logger.debug(f"URN创建请求数据 | 订单: {order_id} | payload: {json.dumps(request_data, ensure_ascii=False)[:500]}")
        logger.info(f"🚀 订单 {order_id} 开始URN创建请求")

        # 使用代理（优先CF池返回的代理）
        cf_proxy = cf_params.get("proxy") if cf_params else None
        proxy = cf_proxy or (random.choice(delegate) if delegate else None)
        proxies = {"http": proxy, "https": proxy} if proxy else None

        logger.info(f"订单 {order_id} 使用代理: {proxy} | 来自CF池: {bool(cf_proxy)}")

        # 重试机制：最多重试3次
        max_retries = 3
        logger.info(f"订单 {order_id} 开始重试循环，最大重试次数: {max_retries}")

        for attempt in range(max_retries):
            try:
                logger.info(f"订单 {order_id} URN创建请求 | 尝试: {attempt + 1}/{max_retries}")
                print(f"headers: {headers}")
                response = requests.post(
                    url,
                    json=request_data,
                    headers=headers,
                    proxies=proxies,
                    impersonate='chrome136',
                    verify=False,
                    timeout=30
                )

                if response.status_code == 200:
                    response_data = response.json()
                    logger.info(f"订单 {order_id} URN请求响应: {response_data}")

                    if response_data.get("error") is None and response_data.get("urn"):
                        # 成功获取URN
                        urn = response_data["urn"]
                        create_time = int(time.time())

                        logger.info(f"🎉 订单URN创建成功 | 订单: {order_id} | URN: {urn}")

                        # 等待列表逻辑：仅当启用等待列表时才需要先确认候补成功
                        # 注意：ConfirmWaitlist 的 urn 必须使用 applicantList[0].urn
                        proceed_to_cache = True
                        try:
                            if waitlist_enabled:
                                logger.info(f"订单 {order_id} 类型启用等待列表，开始ConfirmWaitlist 流程")
                                # 调用ConfirmWaitlist（传递同一份CF参数）
                                confirmed = confirm_waitlist_for_urn(order_id, mission_code, center_code, visa_code, urn, token_data, cf_params)

                                if confirmed:
                                    logger.info(f"订单 {order_id} ConfirmWaitlist 成功（isConfirmed=True）")
                                    # 发送企微消息：订单号、客户姓名、护照号、国家/中心/签证类型
                                    try:
                                        first_client = clients[0] if clients else {}
                                        send_wecom_notify_waitlist_success(order_id, mission_code, center_code, visa_code, first_client, '已加入候补队列')
                                    except Exception as ne:
                                        logger.warning(f"订单 {order_id} 候补成功通知失败: {ne}")
                                else:
                                    logger.warning(f"订单 {order_id} ConfirmWaitlist 未成功（isConfirmed!=True），不缓存URN")
                                    proceed_to_cache = False
                        except Exception as e:
                            logger.error(f"订单 {order_id} ConfirmWaitlist 流程异常: {e}")
                            proceed_to_cache = False

                        # 候补未启用或未确认成功则不继续（与原有逻辑一致）
                        if not proceed_to_cache:
                            return False

                        # ======== CHE 特殊流程：先填表再OTP，再决定是否缓存/回写 ========
                        otp_required = is_otp_required(mission_code, center_code, visa_code)
                        logger.info(f"订单 {order_id} OTP需求检查 | 需要OTP: {otp_required}")
                        otp_success = False
                        if mission_code.lower() == 'che' and ENABLE_CHE_FORMS:
                            # 先同步填表
                            logger.info(f"[CHE] 订单 {order_id} 开始同步填表（先于OTP）")
                            fill_ok = fill_che_forms_after_urn(order, urn, token_data, clients, cf_params)
                            if not fill_ok:
                                logger.warning(f"[CHE] 订单 {order_id} 填表失败，本轮不缓存/不回写")
                                return False
                            # 再做 OTP（若需要），OTP 失败也视为失败，不缓存/不回写
                            if otp_required:
                                otp_success = generate_otp_for_urn(order_id, urn, mission_code, center_code, token_data, cf_params)
                                if not otp_success:
                                    logger.warning(f"[CHE] 订单 {order_id} OTP失败，本轮不缓存/不回写")
                                    return False
                            logger.info(f"[CHE] 订单 {order_id} 填表(及OTP)成功，准备缓存与回写")
                        else:
                            # 其他国家或开关关闭，保持原逻辑（先OTP，再缓存/回写；OTP失败不阻断）
                            otp_success = False
                            if otp_required:
                                otp_success = generate_otp_for_urn(order_id, urn, mission_code, center_code, token_data, cf_params)
                                if otp_success:
                                    logger.info(f"订单 {order_id} URN创建并OTP生成完成")
                                else:
                                    logger.warning(f"订单 {order_id} URN创建成功但OTP生成失败")
                            else:
                                logger.info(f"订单 {order_id} 不需要OTP验证")

                        if otp_required == False or otp_success == True:
                            # 缓存与回写（仅在上面未提前return的情况下执行；CHE 在成功填表/OTP后会走到这里）
                            urn_data = {
                                "urn": urn,
                                "order_id": order_id,
                                "mission_code": mission_code,
                                "center_code": center_code,
                                "visa_code": visa_code,
                                "createTime": create_time,
                                "otp_required": otp_required,
                                "otp_generated": otp_required and (locals().get('otp_success', False)) if otp_required else False
                            }
                            cache_urn(order_id, urn_data)

                            update_order_urn_to_redis(order_id, urn)
                            try:
                                persist_order_urn_and_account(order_id, urn, vfs_account, 'waitlisted' if waitlist_enabled else 'registe_success')
                            except Exception as pe:
                                logger.warning(f"订单 {order_id} 回写DB出现异常: {pe}")
                        else:
                            logger.warning(f"订单 {order_id} 未满足缓存条件，本轮不缓存/不回写")
                        return True
                    else:
                        # 返回200但有业务错误（仅识别三种已知错误；未识别则忽略不入库）
                        desc = (response_data.get("error", {}) or {}).get("description", "") or "未知错误"
                        d = desc.strip().rstrip(".")
                        mapping = {
                            "An applicant with this passport number has already been added": "已被他人候补",
                            "We have received your booking request and your payment is under process. Kindly check status of your booking upto 5 hours": "正在付款中",
                        }
                        matched = False
                        if "You have already booked an appointment" in d:
                            error_msg, matched = "客户已有预约", True
                        elif d in mapping:
                            error_msg, matched = mapping[d], True
                        else:
                            error_msg = desc
                        logger.warning(f"订单URN创建失败 | 订单: {order_id} | 错误: {error_msg}")

                        # 未识别的业务错误：忽略（不入库、不改状态），直接结束
                        if not matched:
                            logger.info(f"忽略未识别业务错误，不入库 | 订单: {order_id} | 原始: {desc}")
                            return False

                        # 错误入库前检查订单状态，若为 deleted 则跳过入库并结束流程
                        try:
                            conn = get_db_connection()
                            if conn:
                                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                                    cur.execute("SELECT order_status FROM orders WHERE order_id=%s LIMIT 1", (order_id,))
                                    row = cur.fetchone()
                                conn.close()
                                if row and str(row.get("order_status", "")).lower() == "deleted":
                                    logger.info(f"订单 {order_id} 状态为 deleted，跳过错误信息入库并结束流程")
                                    return False
                        except Exception as se:
                            logger.warning(f"订单 {order_id} 状态检查失败，继续错误信息入库 | 异常: {se}")

                        # 将错误信息写入数据库（仅已识别的三种错误）
                        insert_urn_error_message(order_id, error_msg)

                        return False
                else:
                    # HTTP状态码不是200，检查是否是401（Token失效）
                    if response.status_code == 401:
                        # Token失效，删除Token
                        logger.warning(f"URN请求Token失效 | 订单: {order_id} | 状态码: 401 | 删除Token")
                        delete_invalid_token(token_data)
                        return False
                    else:
                        # 其他HTTP错误，记录日志但不写数据库，准备重试
                        logger.warning(f"URN请求失败 | 订单: {order_id} | 状态码: {response.status_code} | 尝试: {attempt + 1}/{max_retries}")

                        if attempt < max_retries - 1:
                            # 还有重试机会，等待后重试
                            wait_time = (attempt + 1) * 2  # 递增等待时间：2秒、4秒、6秒
                            logger.info(f"订单 {order_id} 等待 {wait_time} 秒后重试...")
                            time.sleep(wait_time)
                            continue
                        else:
                            # 重试3次都失败
                            error_msg = f"URN请求重试{max_retries}次均失败，最后状态码: {response.status_code}"
                            logger.error(f"订单 {order_id} URN创建最终失败 | {error_msg}")
                            return False

            except requests.exceptions.RequestException as e:
                # 网络请求异常
                logger.warning(f"URN请求网络异常 | 订单: {order_id} | 异常: {str(e)} | 尝试: {attempt + 1}/{max_retries}")

                if attempt < max_retries - 1:
                    # 还有重试机会，等待后重试
                    wait_time = (attempt + 1) * 2
                    logger.info(f"订单 {order_id} 网络异常，等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    # 重试3次都失败，写入数据库
                    error_msg = f"URN请求网络异常重试{max_retries}次均失败: {str(e)}"
                    logger.error(f"订单 {order_id} URN创建最终失败 | {error_msg}")

                    return False

    except Exception as e:
        error_msg = f"订单URN创建异常: {str(e)}"
        order_id = order.get('order_id', 'unknown')
        logger.error(f"❌ 订单URN创建发生异常 | 订单: {order_id} | 异常: {str(e)}")
        logger.error(f"异常详情: {type(e).__name__}: {str(e)}")

        # 打印异常堆栈
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")

        return False


def confirm_waitlist_for_urn(order_id, mission_code, center_code, visa_code, applicant_urn, token_data, cf_params):
    """调用 ConfirmWaitlist 确认候补。返回 True 表示 isConfirmed = True。
    增加最多5次重试，线性退避(2s, 4s, 6s, 8s, 10s)。
    """
    try:
        if not applicant_urn:
            logger.warning(f"ConfirmWaitlist 跳过 | 订单: {order_id} | 原因: applicant_urn 为空")
            return False
        url = "https://lift-apicn.vfsglobal.com/appointment/ConfirmWaitlist"
        max_retries = 5
        for attempt in range(max_retries):
            try:
                r_auth = f"EAAAAN{generate_random_string(597)}="
                headers = {
                    "Content-Type": "application/json;charset=UTF-8",
                    "Authorize": r_auth,
                    "authorize": token_data.get("token"),
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "macOS",
                    "origin": "https://visa.vfsglobal.com",
                    "sec-fetch-site": "same-site",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-dest": "empty",
                    "referer": "https://visa.vfsglobal.com/",
                    "route": f"chn/zh/{mission_code}",
                    "accept-encoding": "gzip, deflate, br",
                    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                    "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
                    "cookie": token_data.get('ltsn', '')
                }
                data = {
                    "countryCode": "chn",
                    "missionCode": mission_code,
                    "centerCode": center_code,
                    "loginUser": token_data.get("email"),
                    "visaCategoryCode": visa_code,
                    "urn": applicant_urn,
                    "notificationType": "none",
                    "CanVFSReachoutToApplicant": True
                }
                proxy = random.choice(delegate) if delegate else None
                proxies = {"http": proxy, "https": proxy} if proxy else None
                logger.info(f"ConfirmWaitlist 尝试 {attempt+1}/{max_retries} | 订单: {order_id} | 代理: {proxy}")
                response = requests.post(
                    url,
                    json=data,
                    headers=headers,
                    proxies=proxies,
                    impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
                    verify=False,
                    timeout=30
                )
                if response.status_code == 200:
                    res = response.json()
                    logger.info(f"ConfirmWaitlist 响应 | 订单: {order_id} | 尝试: {attempt+1}/{max_retries} | {res}")
                    if bool(res.get("isConfirmed") is True):
                        return True
                    else:
                        logger.warning(f"ConfirmWaitlist 未确认 | 订单: {order_id} | 尝试: {attempt+1}/{max_retries}")
                else:
                    if response.status_code == 401:
                        logger.warning(f"ConfirmWaitlist 401 | 删除Token并终止 | 订单: {order_id}")
                        delete_invalid_token(token_data)
                        return False
                    logger.warning(f"ConfirmWaitlist 请求失败 | 订单: {order_id} | 状态码: {response.status_code} | 尝试: {attempt+1}/{max_retries}")
            except requests.exceptions.RequestException as e:
                logger.warning(f"ConfirmWaitlist 网络异常 | 订单: {order_id} | 尝试: {attempt+1}/{max_retries} | 异常: {e}")
            except Exception as e:
                logger.warning(f"ConfirmWaitlist 异常 | 订单: {order_id} | 尝试: {attempt+1}/{max_retries} | 异常: {e}")
            # 退避等待（除最后一次外）
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 2
                logger.info(f"ConfirmWaitlist 重试等待 {wait_time}s | 订单: {order_id}")
                time.sleep(wait_time)
        return False
    except Exception as e:
        logger.error(f"ConfirmWaitlist 外层异常 | 订单: {order_id} | 错误: {e}")
        return False


# 以下函数已迁移至 vfs_toolkit（删除重复实现）

# ===================== 德国候补检查：线程与逻辑 =====================


def get_de_waitlist_orders_to_check():
    """仅筛选德国指定中心，且 urn/vfs_account 均非空，且尚未标记 waitlist_available 的订单。"""
    try:
        conn = get_db_connection()
        if not conn:
            logger.error("无法获取数据库连接")
            return []
        orders = []
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute(
                """
                SELECT order_id, mission_code, center_code, visa_code, urn, vfs_account, auto_schedule
                FROM orders
                WHERE is_vfs_order = true
                  AND LOWER(mission_code) = 'deu'
                  AND center_code IN ('GRSG','GR NN','GR HZ')
                  AND (urn IS NOT NULL AND urn != '')
                  AND (vfs_account IS NOT NULL AND vfs_account != '')
                  AND order_status IN ('waitlist_available', 'waitlisted')
                """
            )
            for row in cur.fetchall():
                orders.append(dict(row))
        conn.close()
        return orders
    except Exception as e:
        logger.error(f"查询德国候补待检查订单失败: {e}")
        return []


def get_random_deu_remote_account():
    """从远程Redis随机获取一个德国账号，返回 dict(email, token, ltsn?)。"""
    try:
        remote = RedisClient(host='************', port=6379, password='TicketsCache#2023', db=0)
        accounts = remote.hgetall('deuLoginUser')
        if not accounts:
            logger.warning("DEU远程Redis未返回任何账号数据 | hash=deuLoginUser")
            return None
        # 兼容两种结构：dict(key->jsonStr) 或 list/jsonStr
        raw = None
        if isinstance(accounts, dict):
            # hgetall 可能返回 {field: value}
            values = list(accounts.values())
            if not values:
                return None
            raw = random.choice(values)
        else:
            raw = random.choice(accounts)
        if isinstance(raw, (bytes, bytearray)):
            try:
                raw = raw.decode('utf-8', errors='ignore')
            except Exception:
                pass
        acct = json.loads(raw) if isinstance(raw, str) else raw
        email = (acct or {}).get('email')
        token = (acct or {}).get('token')
        ltsn = (acct or {}).get('ltsn') if isinstance((acct or {}).get('ltsn'), str) else None
        if not email or not token:
            logger.warning("DEU远程账号缺少必要字段 email/token")
            return None
        return {"email": email, "token": token, "ltsn": ltsn}
    except Exception as e:
        logger.error(f"获取DEU远程账号失败: {e}")
        return None

# 取消德国候补：当返回的 vacCode 与订单 center_code 不一致时调用


def cancel_de_waitlist_for_order(order: dict) -> bool:
    try:
        order_id = order.get("order_id")
        mission_code = (order.get("mission_code") or "").lower()
        center_code = order.get("center_code")
        visa_code = order.get("visa_code")
        urn = order.get("urn")
        vfs_account = order.get("vfs_account")
        if not urn:
            logger.info(f"[DE_WAITLIST_CANCEL] 订单 {order_id} 无 URN，跳过取消")
            return False

        token_data = get_deu_account_from_remote()
        if not token_data:
            logger.warning(f"[DE_WAITLIST_CANCEL] 订单 {order_id} 获取 DEU 远程账号失败")
            return False

        cf_params = fetch_cf_bypass_params(None, logger_prefix="CancelWL")
        ua_from_cf = cf_params.get("user_agent") if cf_params else None
        cf_clearance = cf_params.get("cookie") if cf_params else None
        cf_proxy = cf_params.get("proxy") if cf_params else None

        # cookie 组合：ltsn + cf_clearance
        ltsn = token_data.get("ltsn") or ""
        cookie_header = ltsn
        if cf_clearance:
            cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"

        # 随机授权头（与项目一致长度）
        r_auth = f"EAAAAN{generate_random_string(597)}="
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": token_data.get("token", ""),
            "route": f"chn/zh/{mission_code}",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "cookie": cookie_header,
            "user-agent": ua_from_cf or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        }

        payload = {
            "countryCode": "chn",
            "missionCode": mission_code,
            "centerCode": center_code,
            "loginUser": token_data.get("email"),
            "visaCategoryCode": visa_code,
            "urn": urn,
        }
        if vfs_account:
            payload["LogINUseR"] = vfs_account

        proxies = {"http": cf_proxy, "https": cf_proxy} if cf_proxy else None

        url = "https://lift-apicn.vfsglobal.com/appointment/CancelWaitlist"
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"[DE_WAITLIST_CANCEL] 请求取消候补 | 订单:{order_id} | center:{center_code} | 尝试 {attempt+1}/{max_retries}")
                resp = requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    proxies=proxies,
                    impersonate='chrome136',
                    timeout=30,
                    verify=False,
                )
            except Exception as req_e:
                logger.warning(f"[DE_WAITLIST_CANCEL] 请求异常 | 订单:{order_id} | 错误:{req_e} | 尝试 {attempt+1}/{max_retries}")
                if attempt < max_retries - 1:
                    time.sleep((attempt + 1) * 2)
                    continue
                else:
                    return False

            if resp.status_code != 200:
                logger.warning(f"[DE_WAITLIST_CANCEL] 非200 | 订单:{order_id} | 状态码:{resp.status_code} | 文本:{(resp.text or '')[:200]}...")
                if attempt < max_retries - 1:
                    time.sleep((attempt + 1) * 2)
                    continue
                else:
                    return False
            # 200 则认为取消成功（服务端未提供稳定的业务码）
            break

        # 发送通知并清理本地字段 + 设置状态
        try:
            first_client = (get_clients_for_order(order_id) or [{}])[0]
            send_wecom_notify_waitlist_success(order_id, mission_code, center_code, visa_code, first_client, "候补取消成功")
        except Exception as ne:
            logger.warning(f"[DE_WAITLIST_CANCEL] 通知失败 | 订单:{order_id} | 错误:{ne}")

        try:
            conn = get_db_connection()
            if conn:
                with conn.cursor() as cur:
                    cur.execute(
                        "UPDATE orders SET urn = NULL, vfs_account = NULL, order_status = 'wait_registe', updated_at=CURRENT_TIMESTAMP WHERE order_id = %s",
                        (order_id,)
                    )
                    conn.commit()
                conn.close()
            logger.info(f"[DE_WAITLIST_CANCEL] 已清空本地 URN/VFS账号并置为 wait_registe | 订单:{order_id}")
        except Exception as ce:
            logger.error(f"[DE_WAITLIST_CANCEL] 更新本地订单失败 | 订单:{order_id} | 错误:{ce}")
            return False

        return True
    except Exception as e:
        logger.error(f"[DE_WAITLIST_CANCEL] 取消流程异常 | 订单:{order.get('order_id')} | 错误:{e}")
        return False


def check_de_waitlist_for_order(order: dict):
    """检查单个德国订单的候补状态：仅按订单URN匹配。
    命中 SLOTS AVAILABLE -> 更新状态并发送企微通知；
    否则未找到 -> 清空 urn 和 vfs_account。
    """
    order_id = order.get('order_id')
    mission_code = order.get('mission_code')
    center_code = order.get('center_code')
    visa_code = order.get('visa_code')
    urn = order.get('urn')
    vfs_account = order.get('vfs_account')
    auto_schedule = order.get('auto_schedule')

    if not urn or not vfs_account:
        logger.debug(f"[DE_WAITLIST_SKIP] 订单 {order_id} 缺少 urn 或 vfs_account，跳过检查")
        return False

    # 远程随机账号
    remote_acct = get_random_deu_remote_account()
    if not remote_acct:
        logger.warning(f"[DE_WAITLIST_SKIP] 订单 {order_id} 无可用远程账号，跳过")
        return False

    url = "https://lift-apicn.vfsglobal.com/appointment/application"
    r_auth = f"EAAAAN{generate_random_string(597)}="
    headers = {
        "Content-Type": "application/json;charset=UTF-8",
        "Authorize": r_auth,
        "authorize": remote_acct.get("token"),
        "sec-ch-ua-mobile": "?0",
        "route": "chn/zh/deu",
        "sec-ch-ua-platform": "macOS",
        "origin": "https://visa.vfsglobal.com",
        "sec-fetch-site": "same-site",
        "sec-fetch-mode": "cors",
        "sec-fetch-dest": "empty",
        "referer": "https://visa.vfsglobal.com/",
        "accept-encoding": "gzip, deflate, br",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
    }
    if remote_acct.get("ltsn"):
        headers["cookie"] = remote_acct.get("ltsn")

    body = {
        "countryCode": "chn",
        "missionCode": mission_code,
        "loginUser": remote_acct.get("email"),   # 随机远程账号
        "lOGinusEr": vfs_account,                 # 数据库里实际存储的 vfs_account
        "languageCode": "zh-CN",
    }

    proxy = random.choice(delegate) if delegate else None
    proxies = {"http": proxy, "https": proxy} if proxy else None
    print(headers, body, proxies)
    max_retries = 3
    for attempt in range(max_retries):
        try:
            resp = requests.post(
                url,
                json=body,
                headers=headers,
                proxies=proxies,
                impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
                verify=False,
                timeout=30,
            )
            if resp.status_code != 200:
                if resp.status_code == 401:
                    logger.warning(f"[DE_WAITLIST] 订单 {order_id} 接口401，终止本次检查")
                    return False
                logger.warning(f"[DE_WAITLIST] 订单 {order_id} 接口状态码 {resp.status_code} | 尝试 {attempt+1}/{max_retries}")
            else:
                res = resp.json()
                if res.get('error') is None and isinstance(res.get('data'), list):
                    apps = res.get('data')
                    # 仅按订单URN匹配
                    matched = next((a for a in apps if str(a.get('urn')) == str(urn)), None)
                    if matched:
                        # 校验返回的 vacCode 是否与订单 center_code 一致；不一致则直接取消候补
                        vac_code = matched.get('vacCode')
                        if vac_code and str(vac_code) != str(center_code):
                            logger.info(f"[DE_WAITLIST_CANCEL] URN中心不一致 | 订单:{order_id} | 期望:{center_code} | 返回:{vac_code}")
                            return cancel_de_waitlist_for_order(order)

                        status = matched.get('waitlistStatus')
                        if status == 'SLOTS AVAILABLE':
                            # 更新状态为 waitlist_available（避免重复通知）
                            try:
                                conn = get_db_connection()
                                if conn:
                                    with conn.cursor(cursor_factory=RealDictCursor) as cur:
                                        cur.execute("SELECT order_status FROM orders WHERE order_id=%s", (order_id,))
                                        row = cur.fetchone()
                                        current_status = row.get('order_status') if row else None
                                        if current_status != 'waitlist_available':
                                            cur.execute(
                                                "UPDATE orders SET order_status='waitlist_available', updated_at=CURRENT_TIMESTAMP WHERE order_id=%s",
                                                (order_id,)
                                            )
                                            conn.commit()
                                            logger.info(f"[DE_WAITLIST_AVAILABLE] 订单 {order_id} 候补可用，状态更新为 waitlist_available")
                                            # 发送企微通知
                                            first_client = (get_clients_for_order(order_id) or [{}])[0]
                                            try:
                                                message = "候补成功，系统将会在匹配到日期后自动预约" if auto_schedule else "候补成功，未开启自动预约，请尽快在网页端手动预约"
                                                send_wecom_notify_waitlist_success(order_id, mission_code, center_code, visa_code, first_client, message)
                                            except Exception as ne:
                                                logger.warning(f"订单 {order_id} 候补可用通知失败: {ne}")
                                        else:
                                            logger.info(f"[DE_WAITLIST_AVAILABLE] 订单 {order_id} 已是 waitlist_available，跳过重复通知")
                                    conn.close()
                                return True
                            except Exception as we:
                                logger.error(f"订单 {order_id} 更新候补状态失败: {we}")
                                return False
                        else:
                            logger.info(f"[DE_WAITLIST_STATUS] 订单 {order_id} 当前候补状态: {status}")
                            return True
                    else:
                        # 未找到客户 -> 清空 urn 和 vfs_account
                        try:
                            conn = get_db_connection()
                            if conn:
                                with conn.cursor() as cur:
                                    cur.execute(
                                        "UPDATE orders SET urn = NULL, vfs_account = NULL, order_status = 'wait_registe', updated_at=CURRENT_TIMESTAMP WHERE order_id = %s",
                                        (order_id,)
                                    )
                                    conn.commit()
                                conn.close()
                            logger.warning(f"[DE_WAITLIST_NOT_FOUND] 订单 {order_id} 未在返回中找到客户，已清空本地 urn/vfs_account")
                            return True
                        except Exception as ce:
                            logger.error(f"订单 {order_id} 清空本地 urn/vfs_account 失败: {ce}")
                            return False
                else:
                    # 未找到客户 -> 清空 urn 和 vfs_account
                    try:
                        conn = get_db_connection()
                        if conn:
                            with conn.cursor() as cur:
                                cur.execute(
                                    "UPDATE orders SET urn = NULL, vfs_account = NULL, order_status = 'wait_registe', updated_at=CURRENT_TIMESTAMP WHERE order_id = %s",
                                    (order_id,)
                                )
                                conn.commit()
                            conn.close()
                        logger.warning(f"[DE_WAITLIST_NOT_FOUND] 订单 {order_id} 未在返回中找到客户，已清空本地 urn/vfs_account")
                        return True
                    except Exception as ce:
                        logger.error(f"订单 {order_id} 清空本地 urn/vfs_account 失败: {ce}")
                        return False
            if attempt < max_retries - 1:
                time.sleep((attempt + 1) * 2)
        except Exception as e:
            logger.warning(f"[DE_WAITLIST] 订单 {order_id} 网络异常: {e} | 尝试 {attempt+1}/{max_retries}")
            if attempt < max_retries - 1:
                time.sleep((attempt + 1) * 2)
    return False


def de_waitlist_monitor_worker():
    """德国候补监控线程：每5分钟批量并发检查。"""
    logger.info(f"DEU 候补检查线程启动 | 间隔: {WAITLIST_CHECK_INTERVAL}s | 并发: 5")
    from concurrent.futures import ThreadPoolExecutor, as_completed
    while True:
        try:
            orders = get_de_waitlist_orders_to_check()
            if not orders:
                logger.debug("DEU 候补检查：无待检查订单")
                time.sleep(WAITLIST_CHECK_INTERVAL)
                continue
            logger.info(f"DEU 候补检查：待检查订单数 {len(orders)}")
            success = 0
            fail = 0
            with ThreadPoolExecutor(max_workers=5, thread_name_prefix="DEWaitlist") as ex:
                futures = [ex.submit(check_de_waitlist_for_order, o) for o in orders]
                for f in as_completed(futures):
                    try:
                        if f.result():
                            success += 1
                        else:
                            fail += 1
                    except Exception as e:
                        fail += 1
                        logger.error(f"DEU 候补检查任务异常: {e}")
            logger.info(f"DEU 候补检查完成 | 成功: {success} | 失败: {fail}")
        except Exception as e:
            logger.error(f"DEU 候补检查线程异常: {e}")
        finally:
            time.sleep(WAITLIST_CHECK_INTERVAL)


# 启动德国候补监控线程（daemon）
try:
    t = threading.Thread(target=de_waitlist_monitor_worker, name="DEWaitlistMonitor", daemon=True)
    t.start()
    logger.info("德国候补监控线程已启动")
except Exception as e:
    logger.error(f"德国候补监控线程启动失败: {e}")

# 初始化配置
refresh_global_config()
