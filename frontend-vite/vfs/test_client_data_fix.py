#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试客户数据获取修复
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    
    try:
        from db_utils import get_db_connection
        from psycopg2.extras import RealDictCursor
        
        conn = get_db_connection()
        if not conn:
            print("✗ 数据库连接失败")
            return False
        
        print("✓ 数据库连接成功")
        
        # 测试基本查询
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            print(f"✓ PostgreSQL版本: {version['version'][:50]}...")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {e}")
        return False


def test_clients_table_structure():
    """测试clients表结构"""
    print("\n=== 测试clients表结构 ===")
    
    try:
        from db_utils import get_db_connection
        from psycopg2.extras import RealDictCursor
        
        conn = get_db_connection()
        if not conn:
            print("✗ 数据库连接失败")
            return False
        
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 检查clients表是否存在
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'clients'
                );
            """)
            exists = cursor.fetchone()['exists']
            
            if not exists:
                print("✗ clients表不存在")
                conn.close()
                return False
            
            print("✓ clients表存在")
            
            # 获取表结构
            cursor.execute("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'clients'
                ORDER BY ordinal_position;
            """)
            columns = cursor.fetchall()
            
            print("✓ clients表字段:")
            for col in columns:
                print(f"  - {col['column_name']}: {col['data_type']}")
            
            # 检查数据数量
            cursor.execute("SELECT COUNT(*) as count FROM clients;")
            count = cursor.fetchone()['count']
            print(f"✓ clients表记录数: {count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ clients表结构测试失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False


def test_client_data_retrieval():
    """测试客户数据获取"""
    print("\n=== 测试客户数据获取 ===")
    
    try:
        from order_urn_manager import get_clients_for_order
        from db_utils import get_db_connection
        from psycopg2.extras import RealDictCursor
        
        # 先找一个真实的订单ID
        conn = get_db_connection()
        if not conn:
            print("✗ 数据库连接失败")
            return False
        
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查找有客户数据的订单
            cursor.execute("""
                SELECT DISTINCT order_id 
                FROM clients 
                WHERE order_id IS NOT NULL 
                LIMIT 5
            """)
            orders = cursor.fetchall()
        
        conn.close()
        
        if not orders:
            print("ℹ 数据库中没有客户数据，创建测试数据进行验证")
            # 使用一个不存在的订单ID测试
            test_order_id = "TEST_ORDER_NONEXISTENT"
            clients = get_clients_for_order(test_order_id)
            print(f"✓ 函数正常运行，返回客户数: {len(clients)}")
            return True
        
        # 使用真实订单ID测试
        test_order_id = orders[0]['order_id']
        print(f"使用真实订单ID测试: {test_order_id}")
        
        clients = get_clients_for_order(test_order_id)
        print(f"✓ 获取客户数据成功，客户数: {len(clients)}")
        
        if clients:
            print("✓ 客户数据示例:")
            client = clients[0]
            print(f"  数据类型: {type(client)}")
            print(f"  字段数量: {len(client)}")
            
            # 检查是否是真实数据而不是字段名
            name_value = client.get('name', '')
            if name_value and name_value != 'name':
                print(f"  ✓ 姓名: {name_value}")
                print(f"  ✓ 护照: {client.get('passport', 'N/A')}")
                print(f"  ✓ 生日: {client.get('dob', 'N/A')}")
                print(f"  ✓ 性别: {client.get('gender', 'N/A')}")
                print("  ✓ 数据获取正确，包含真实值")
            else:
                print(f"  ✗ 数据异常，姓名字段值: {name_value}")
                print("  ✗ 可能仍然返回字段名而不是数据值")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 客户数据获取测试失败: {e}")
        return False


def test_realdict_cursor():
    """测试RealDictCursor的使用"""
    print("\n=== 测试RealDictCursor使用 ===")
    
    try:
        from db_utils import get_db_connection
        from psycopg2.extras import RealDictCursor
        
        conn = get_db_connection()
        if not conn:
            print("✗ 数据库连接失败")
            return False
        
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 测试简单查询
            cursor.execute("SELECT 'test_name' as name, 'test_passport' as passport, '1990-01-01' as dob;")
            result = cursor.fetchone()
            
            print(f"✓ 查询结果类型: {type(result)}")
            print(f"✓ 查询结果: {dict(result)}")
            
            # 验证字段访问
            if result['name'] == 'test_name':
                print("✓ RealDictCursor工作正常")
            else:
                print("✗ RealDictCursor工作异常")
                return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ RealDictCursor测试失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False


def main():
    """主测试函数"""
    print("客户数据获取修复验证测试")
    print("=" * 50)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("clients表结构", test_clients_table_structure),
        ("RealDictCursor使用", test_realdict_cursor),
        ("客户数据获取", test_client_data_retrieval),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！客户数据获取修复成功")
        print("\n📋 修复说明:")
        print("1. 使用RealDictCursor直接获取字典格式结果")
        print("2. 避免手动构建字典时的字段名错误")
        print("3. 确保返回真实数据值而不是字段名")
        return True
    else:
        print("⚠️  部分测试失败，请检查数据库配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
