# -*- coding: utf-8 -*-
from curl_cffi import requests
import time
import json
import threading
from RedisClientAWS import RedisClient
import random
from datetime import datetime, timedelta
from queue import Queue
import urllib.parse
import base64
from io import BytesIO
import requests as rqst
from urllib.parse import urlparse, parse_qs
import string


def generate_random_string(length=31):
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string


# 创建 RedisClient 实例
redis_client = RedisClient()
delegate = json.loads(redis_client.get("fast_proxy"))


# 创建一个线程安全的队列来保存需要处理的用户
user_queue = Queue()
# 定义一个函数用于并发处理用户请求


def pick_random_qiwei_url():
    list = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cf1c820d-b5fc-4b5b-bcf4-e25fcaedcb9f",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3bd347f5-6a2b-4b36-b647-018a7709499a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b62a48af-016c-405b-8f2d-765131632b4e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fbad1704-4d04-4c91-851d-675edee25a13",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3793f848-b529-415e-bef0-e09c31b3d7b0",
    ]
    return pick_random_elements(list, 1)[0]


def pick_random_xiecheng_url():
    list = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=99f7a2a9-d25a-4c47-b412-db6ca3cbcea7",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=73ac3ac0-14f7-4761-bc17-80a0aff92916",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e53fb8b6-a111-4ef1-aafe-85deb0606119",
    ]
    return pick_random_elements(list, 1)[0]


def pick_random_elements(arr, num):
    if num >= len(arr):
        return arr[:]
    shuffled = arr[:]
    random.shuffle(shuffled)
    return shuffled[:num]


def mission_code_to_chn(code):
    if code == 'ita':
        return '意大利'
    if code == 'deu':
        return '德国'
    if code == 'che':
        return '瑞士'
    if code == 'isl':
        return '冰岛'
    if code == 'pol':
        return '波兰'
    if code == 'aut':
        return '奥地利'
    if code == 'prt':
        return '葡萄牙'
    if code == 'hun':
        return '匈牙利'
    if code == 'nor':
        return '挪威'


centerCode2Chn = {
    'PVG': '上海',
    'HGH': '杭州',
    'CKG': '重庆',
    'NKG': '南京',
    'TNA': '济南',
    'FOC': '福州',
    'CAN': '广州',
    'CGS': '长沙',
    'WUH': '武汉',
    'XIY': '西安',
    'SHE': '沈阳',
    'SZX': '深圳',
    'PEK': '北京',
    'KUN': '昆明',
    'CDU': '成都',
    'SHAN': '上海',
    'BEIJ': '北京',
    'BGHZ': '广州',
    'BNAN': '南京',
    'SHZN': '深圳',
    'BHAN': '杭州',
    'Shaa': '上海',
    'ASCS': '重庆',
    'Chea': '成都',
    'Bei': '北京',
    'Gua': '广州',
    'NANJINGa': '南京',
    'AUTCHWH': '武汉',
    'ASCG': '长沙',
    'ASJN': '济南',
    'ASSZA': '深圳',
    'ASHZa': '杭州',
    'PCSI': '上海',
    'PCBG': '北京',
    'PCJN': '济南',
    'PCFU': '福州',
    'PCGZ': '广州',
    'PCNG': '南京',
    'PSZH': '深圳',
    'PCHU': '杭州',
    'HNCHSG': '上海',
    'HNCHBN': '北京',
    'HNCHGZ': '广州',
    'HNCHSY': '沈阳',
    'HNCHNN': '南京',
    'HNCHSN': '深圳',
    'HNCHHZ': '杭州',
    'POSH': '上海',
    'POBE': '北京',
    'GUAN': '广州',
    'PONA': '南京',
    'PSZX': '深圳',
    'POHA': '杭州',
    'SHANGHAI': '上海',
    'FUZHOU': '福州',
    'BEIJING': '北京',
    'Guangzhou': '广州',
    'NANJING': '南京',
    'SHENZHEN': '深圳',
    'Hangzhou': '杭州',
    'ICCNGZ': '广州',
    'ICCNBE': '北京',
    'ICCNCH': '成都',
    'ICCNXI': '西安',
    'ICCNWU': '武汉',
    'ICCNNA': '南京',
    'ICCNCQ': '重庆',
    'ICCNHG': '杭州',
    'ICCNFZ': '福州',
    'ICCNSG': '上海',
    'ICCNSZ': '深圳',
    'FIN CAN': '广州',
    'FIN PEK': '北京',
    'FIN CTU': '成都',
    'FIN XIY': '西安',
    'FIN WUH': '武汉',
    'FIN NKG': '南京',
    'FIN CKG': '重庆',
    'FIN HGH': '杭州',
    'FIN FOC': '福州',
    'FIN PVG': '上海',
    'FIN SZX': '深圳',
    'FIN SHE': '沈阳',
    'FIN CGS': '长沙',
    'FIN KUN': '昆明',
    'FIN TNA': '济南',
}


def confimpayment(user, resp):
    try:
        print(resp)
        loginUser = [item for item in redis_client.hgetall(f"{user.get('missionCode')}LoginUser") if item.get('updateTokenTime') != None and int(time.time()) - item.get('updateTokenTime') < 6000]
        if len(loginUser) != 0:
            account = random.choice(loginUser)
            if (
                account.get("updateTokenTime") == None
                or int(time.time()) - account.get("updateTokenTime") > 6000
            ):
                print("票据过期")
                return False
            url = "https://lift-apicn.vfsglobal.com/payMents/confirmAppointment"
            headers = {
                "Content-Type": "application/json;charset=UTF-8",
                "authorize": account["token"],
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "macOS",
                "origin": "https://visa.vfsglobal.com",
                "sec-fetch-site": "same-site",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://visa.vfsglobal.com/",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            }
            data = {
                "missionCode": user.get('missionCode'),
                "countryCode": "chn",
                "loginUser": account.get("email"),
                "lOGINUSEr": user.get('wemail') if user.get('wurn') != None else user.get('loginUser'),
                "urn": user.get('wurn') if user.get('wurn') != None else user.get('urn'),
                "bankReferenceNo": resp.get('TransactionId'),
                "requestReferenceNo": str(user.get('RequestRefNo')),
                "amount": resp.get('Amount'),
                "currency": "CNY",
                "cultureCode": "zh-CN",
                "centerCode": user.get('centerCode'),
                "isCBankPayment": False
            }

            proxy = "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335"
            proxies = {
                "http": proxy,
                "https": proxy,
            }
            response = requests.post(
                url,
                json=data,
                headers=headers,
                proxies=proxies,
                impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
                verify=False,
            )
            if response.status_code == 200:
                res = response.json()
                print(res)
                if res.get("IsAppointmentBooked") == True or res.get('error') != None and res.get('error').get('description') == 'Payment is already confirmed':
                    print(f"客户{user.get('chnname')}成功Confirm付款", res)
                    return res
                return None
            else:
                print(response.status_code)
        return None
    except Exception as e:
        print(f"可以重试{e}")
        return None


def attempt_confimpayment(user, resp, max_retries=10, retry_interval=10):
    for _ in range(max_retries):
        result = confimpayment(user, resp)
        if result != None:
            return result
        time.sleep(retry_interval)
    return None


def trackpaymentstatus(user):
    try:
        loginUser = [item for item in redis_client.hgetall(f"{user.get('missionCode')}LoginUser") if item.get('updateTokenTime') != None and int(time.time()) - item.get('updateTokenTime') < 6000]
        if len(loginUser) != 0:
            account = random.choice(loginUser)
            url = f"https://lift-apicn.vfsglobal.com/payments/trackpayMentstatus?RequestRefNo={user.get('RequestRefNo')}"
            headers = {
                "Content-Type": "application/json;charset=UTF-8",
                "authorization": account["token"],
                "authorize": f"EAAAANkoweAPl1c92tUMRWNCuOfMZw2XjSr1EcsVDtuqr18JsegEk1l3C6f7VvsD07lOiYz3/wFlhsC3jSGYpxjhITkvpQw0fbUp/FKE2JzaVAiq6Gye8LKchEL3yruYwYnsUiMw+7KqtabN0gfC0NP7jbyPIyWHyYHKoSs63jGk+/JKlFJXHQPEkrEAheFqoN/6eQ8H0sWaFQrb644IKM7N8HjRhkcQXB5MYsn{generate_random_string()}+BKVnsDVvsTqf9jVy6xG960twrgfiyyV9YJjWDEcPcw8bpbrgjV9+C3Snvxi7w3RvcZCbeugfy1Ci7ATQH4soTWNM0D0b3UZWXHmfCp2phPujse36H+e2yI4+FJ09IWjCaGr0fKEVMBHzzy5Ac63rE27z/J0qVJBnzNVs6dxDH3BX0KMZoj8+sUHP9K8+gZGcJoUmC1/1PVG87JGuHvu7YZSDubbjg0PIAmbec3MeoD3J0sz+AcJ2zWujjYRNodhBEfqYYeyugZaEau1vDmqk07iDphprFenX2Jo/j2iw3l/7sdR0YLMqqTV5d/OOCUKcMNyuvUNrcxFk4LojCvfI=",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "macOS",
                "origin": "https://visa.vfsglobal.com",
                "sec-fetch-site": "same-site",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://visa.vfsglobal.com/",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            }

            proxy = "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335"
            proxies = {
                "http": proxy,
                "https": proxy,
            }
            response = requests.get(
                url,
                headers=headers,
                proxies=proxies,
                impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
                verify=False,
            )
            if response.status_code == 200:
                res = response.json()
                print(res)
                if res.get("Status") == 'SUCCESS':
                    print(f"客户{user.get('chnname')}成功付款", res)
                    return res
                return None
        return None
    except Exception as e:
        print(f"可以重试{e}")
        return None


def attempt_trackpaymentstatus(user, max_retries=10, retry_interval=10):
    for _ in range(max_retries):
        result = trackpaymentstatus(user)
        if result != None:
            return result
        time.sleep(retry_interval)
    return None


def download(user):
    loginUser = [item for item in redis_client.hgetall(f"{user.get('missionCode')}LoginUser") if item.get('updateTokenTime') != None and int(time.time()) - item.get('updateTokenTime') < 6000]
    # account = next(
    #     (acc for acc in loginUser if acc["email"] == user["loginUser"]), None
    # )
    # if not account:
    #     print(f"账号不存在:{user.get('chnname')} | 结果: {user.get('loginUser')}")
    #     return
    acc1 = pick_random_elements(loginUser, 1)[0]
    if user.get('wurn') != None:
        urn = user.get('wurn')
        email = user.get('wemail')
    else:
        urn = user["urn"]
        email = user["loginUser"]
    print(urn, email)
    try:
        if redis_client.get(urn) == None:
            print(user)
            if user.get('missionCode') in ['deu', 'isl', 'aut', 'pol', 'prt', 'hun', 'che', 'nor'] and int(time.time()) - user.get("update_order_time") < 1800 and user.get('payment_confirmed') != True:
                # 原始URL
                # 使用 urlparse 分析 URL
                parsed_url = urlparse(user.get('old_url'))

                # 使用 parse_qs 分析查询参数
                query_params = parse_qs(parsed_url.query)

                amount = query_params.get('Amount', [None])[0]

                if user.get('pay_code') != None and amount != None:
                    print('正在确认付款')
                    res = attempt_confimpayment(user, {"TransactionId": user.get('pay_code'), "Amount":  int(amount)})
                    if res != None and res.get("IsAppointmentBooked") == True or res.get('error') != None and res.get('error').get('description') == 'Payment is already confirmed':
                        redis_client.hset(
                            "successUserDatas",
                            f"{user['centerCode']}-{user['passportNO']}",
                            json.dumps({**user, "payment_confirmed": True}),
                        )
                    else:
                        return False
                else:
                    res = attempt_trackpaymentstatus(user)
                    if res != None and res.get("Status") == 'SUCCESS':
                        print('正在确认付款')
                        res = attempt_confimpayment(user, res)
                        if res.get("IsAppointmentBooked") == True or res.get('error') != None and res.get('error').get('description') == 'Payment is already confirmed':
                            redis_client.hset(
                                "successUserDatas",
                                f"{user['centerCode']}-{user['passportNO']}",
                                json.dumps({**user, "payment_confirmed": True}),
                            )
                        else:
                            return False
                    else:
                        return False
            if user.get('chnname') == "测试":
                return
            if int(time.time()) - user.get("update_order_time") > 7200:
                url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7b816326-5e5c-441d-aced-fc3d186addfe"
                postData = {
                    "msgtype": "text",
                    "text": {
                        "content": f"{mission_code_to_chn(user.get('missionCode'))}客户【{user.get('chnname')}】超过2小时未能下载预约信，尝试重新添加"
                    },
                }
                requests.post(url, json=postData)
                redis_client.set(urn, urn, 86400)
            url = "https://lift-apicn.vfsglobal.com/appointment/downLoadPdf"
            headers = {
                "Content-Type": "application/json;charset=UTF-8",
                "authorization": acc1["token"],
                "authorize": f"EAAAANkoweAPl1c92tUMRWNCuOfMZw2XjSr1EcsVDtuqr18JsegEk1l3C6f7VvsD07lOiYz3/wFlhsC3jSGYpxjhITkvpQw0fbUp/FKE2JzaVAiq6Gye8LKchEL3yruYwYnsUiMw+7KqtabN0gfC0NP7jbyPIyWHyYHKoSs63jGk+/JKlFJXHQPEkrEAheFqoN/6eQ8H0sWaFQrb644IKM7N8HjRhkcQXB5MYsn{generate_random_string()}+BKVnsDVvsTqf9jVy6xG960twrgfiyyV9YJjWDEcPcw8bpbrgjV9+C3Snvxi7w3RvcZCbeugfy1Ci7ATQH4soTWNM0D0b3UZWXHmfCp2phPujse36H+e2yI4+FJ09IWjCaGr0fKEVMBHzzy5Ac63rE27z/J0qVJBnzNVs6dxDH3BX0KMZoj8+sUHP9K8+gZGcJoUmC1/1PVG87JGuHvu7YZSDubbjg0PIAmbec3MeoD3J0sz+AcJ2zWujjYRNodhBEfqYYeyugZaEau1vDmqk07iDphprFenX2Jo/j2iw3l/7sdR0YLMqqTV5d/OOCUKcMNyuvUNrcxFk4LojCvfI=",
                "sec-ch-ua-mobile": "?0",
                "route": "chn/zh/deu",
                "sec-ch-ua-platform": "macOS",
                "origin": "https://visa.vfsglobal.com",
                "sec-fetch-site": "same-site",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://visa.vfsglobal.com/",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            }

            data = {
                "cultureCode": "",
                "missionCode": user["missionCode"],
                "countryCode": "chn",
                "loginUser": acc1["email"],
                "loginuSer": email,
                "urn": urn,
            }
            proxy = "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335"
            proxies = {
                "http": proxy,
                "https": proxy,
            }
            response = requests.post(
                url,
                json=data,
                headers=headers,
                proxies=proxies,
                impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
                verify=False,
            )
            if response.status_code == 200:
                try:
                    if response.json().get("code") != None:
                        print(
                            f"下载预约信失败:{user.get('chnname')} | {urn} | 结果: {response.json().get('description')}"
                        )
                except:
                    if len(response.text) > 1000:
                        pdf_data = base64.b64decode(response.text)
                        pdf_file = BytesIO(pdf_data)
                        if user.get('child') != None and len(user.get('child')) > 0:
                            name = ",".join(d["chnname"] for d in user.get('child'))
                        else:
                            name = user.get('chnname')
                        if user.get('date'):
                            file_name = f"{mission_code_to_chn(user.get('missionCode'))}{centerCode2Chn.get(user.get('centerCode'))}-{datetime.strptime(user.get('date'), '%d/%m/%Y').strftime('%Y年%m月%d日')}-{name}【{user.get('from')}】.pdf"
                        else:
                            file_name = f"{mission_code_to_chn(user.get('missionCode'))}{centerCode2Chn.get(user.get('centerCode'))}-{name}【{user.get('from')}】.pdf"
                        pdf_file.name = file_name
                        url = pick_random_qiwei_url()
                        res = rqst.post(
                            f"https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key={url.split('key=')[1]}&type=file",
                            files={
                                "file": (
                                    file_name,
                                    pdf_file,
                                )
                            },
                        )
                        pdf_file.seek(0)
                        if "携程" in user.get('from') or "线下" in user.get('from'):
                            url_2 = pick_random_xiecheng_url()
                            resp2 = rqst.post(
                                f"https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key={url_2.split('key=')[1]}&type=file",
                                files={
                                    "file": (
                                        file_name,
                                        pdf_file,
                                    )
                                },
                            )
                            if resp2.status_code == 200:
                                datas = resp2.json()
                                postData = {
                                    "msgtype": "file",
                                    "file": {"media_id": datas.get("media_id")},
                                }
                                resp = requests.post(url_2, json=postData)
                        if res.status_code == 200:
                            datas = res.json()
                            postData = {
                                "msgtype": "file",
                                "file": {"media_id": datas.get("media_id")},
                            }
                            resp = requests.post(url, json=postData)
                            if resp.status_code == 200:
                                jsond = resp.json()
                                if jsond.get("errcode") == 0:
                                    redis_client.hset(
                                        "successUserDatas",
                                        f"{user['centerCode']}-{user['passportNO']}",
                                        json.dumps({**user, "pdf_downloaded": True}),
                                    )
                        else:
                            print(res.text)
            else:
                print(
                    f"下载预约信失败:{user.get('chnname')} | 结果: {response.status_code}"
                )
    except Exception as e:
        print(e)
        pass


def process_users():
    while not user_queue.empty():
        user = user_queue.get()
        download(user)
        user_queue.task_done()


def getNeedProceed(user_list):
    current_time = int(time.time())

    def filter_condition(user):
        return (
            user.get("pdf_downloaded") != True
            and user.get('update_order_time') and current_time - user.get("update_order_time") < 12000
            and ((user.get("missionCode") in ["deu", "prt", "che", "aut", "hun", "pol", 'nor']) and current_time - user.get("update_order_time") > 20 or (user.get("missionCode") == "isl" and current_time - user.get("update_order_time") > 5))
        )
    need_proceed = list(filter(filter_condition, user_list))
    return need_proceed


def refresh_proxy():
    while True:
        time.sleep(60)
        global delegate
        delegate = json.loads(redis_client.get("fast_proxy"))
        print(delegate)


threading.Thread(target=refresh_proxy).start()

while True:
    orders = redis_client.hgetall("successUserDatas")
    need_proceed = getNeedProceed(orders)

    if len(need_proceed) != 0:
        print(f"正在下载{len(need_proceed)}个预约信")
        # 创建一个线程安全的队列来保存需要处理的用户
        for user in need_proceed:
            user_queue.put(user)

        num_threads = 10  # 可根据需要调整线程数量
        threads = []
        for _ in range(num_threads):
            thread = threading.Thread(target=process_users)
            thread.start()
            threads.append(thread)

        for thread in threads:
            thread.join()

        # for user in need_proceed:
        #     get_token(user)
        time.sleep(10)
    else:
        print("暂无需要下载的预约信")
        time.sleep(10)
