# -*- coding: utf-8 -*-
"""
VFS 通用工具模块（工具函数集合）
- 加密/时间
- DB 查询：订单负责人、Hook、客户信息
- 企微通知：候补成功、预约成功
- 日期转换：dd/mm/yyyy -> yyyy-mm-dd（用于通知显示）

注意：encryption 内部带 RSA 公钥的懒加载（从 Redis 读取 rsa_str）。
"""
from __future__ import annotations

import time
import json
import logging
from typing import List, Optional, Tuple
from datetime import datetime

from curl_cffi import requests
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding

from db_utils import get_db_connection
from RedisClient import RedisClient
from vfs_mappings import get_mission_name, get_center_name, get_visa_name

logger = logging.getLogger(__name__)
redis_client = RedisClient()

# RSA 懒加载缓存
_RSA_CACHE_TTL = 300  # 秒
_rsa_string: Optional[str] = None
_rsa_last_load: int = 0


def get_current_timestamp() -> str:
    now = datetime.now()
    return now.strftime("%Y-%m-%dT%H:%M:%S")


def format_rsa_string(compact_key: str) -> str:
    if not compact_key:
        return ""
    base64_content = compact_key.replace("|", "\n")
    return f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"


def _ensure_rsa_loaded() -> None:
    global _rsa_string, _rsa_last_load
    now = int(time.time())
    if _rsa_string and (now - _rsa_last_load) < _RSA_CACHE_TTL:
        return
    try:
        src = redis_client.get('rsa_str')
        if src:
            _rsa_string = format_rsa_string(src)
            _rsa_last_load = now
            logger.debug(f"RSA 公钥已懒加载，长度: {len(_rsa_string)}")
        else:
            logger.warning("RSA 公钥未在 Redis 中找到(rsa_str)")
            _rsa_string = None
            _rsa_last_load = now
    except Exception as e:
        logger.warning(f"加载 RSA 公钥失败: {e}")
        _rsa_string = None
        _rsa_last_load = now


def encryption(text: str) -> str:
    try:
        _ensure_rsa_loaded()
        if not _rsa_string:
            return ""
        public_key = serialization.load_pem_public_key(_rsa_string.encode())
        encrypted = public_key.encrypt(text.encode(), padding.PKCS1v15())
        import base64
        return base64.b64encode(encrypted).decode()
    except Exception as e:
        logger.error(f"RSA加密失败: {e}")
        return ""


# ===================== DB 查询 =====================

def get_operator_id_for_order(order_id: str) -> Optional[int]:
    """获取订单的负责人/创建人ID，优先 operator，回退 user_id。"""
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            return None
        with conn.cursor() as cur:
            try:
                cur.execute("SELECT operator FROM orders WHERE order_id = %s", (order_id,))
                row = cur.fetchone()
                if row and row[0]:
                    return row[0]
            except Exception:
                pass
            try:
                cur.execute("SELECT user_id FROM orders WHERE order_id = %s", (order_id,))
                row = cur.fetchone()
                if row and row[0]:
                    return row[0]
            except Exception:
                pass
        return None
    except Exception as e:
        logger.error(f"查询operator异常 | 订单: {order_id} | 错误: {e}")
        return None
    finally:
        if conn:
            conn.close()


def get_wecom_hooks_for_user(user_id: int) -> List[str]:
    """查询用户的企业微信Hook列表。"""
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            return []
        with conn.cursor() as cur:
            cur.execute(
                """
                SELECT hook_url FROM user_hooks
                WHERE user_id = %s AND hook_url IS NOT NULL AND hook_url != ''
                """,
                (user_id,)
            )
            rows = cur.fetchall()
            return [r[0] for r in rows if r and r[0]]
    except Exception as e:
        logger.error(f"查询企业微信Hook失败 | 用户: {user_id} | 错误: {e}")
        return []
    finally:
        if conn:
            conn.close()


def get_clients_for_order(order_id: str) -> List[dict]:
    """从数据库获取订单的客户信息(列表)。"""
    try:
        from psycopg2.extras import RealDictCursor
        conn = get_db_connection()
        if not conn:
            logger.error("无法获取数据库连接")
            return []
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(
                """
                SELECT name, surname_pinyin, firstname_pinyin, dob, passport,
                       passport_expire, passport_image, avatar_image, gender,
                       nationality, passport_date, sign_location, bornplace,
                       marital_status, country, region
                FROM clients WHERE order_id = %s
                ORDER BY id
                """,
                (order_id,)
            )
            clients = cursor.fetchall()
            clients = [dict(row) for row in clients]
        conn.close()
        return clients
    except Exception as e:
        logger.error(f"获取订单客户信息失败 | 订单: {order_id} | 异常: {e}")
        try:
            if conn:
                conn.close()
        except Exception:
            pass
        return []


def get_primary_client(order_id: str) -> Tuple[Optional[str], Optional[str]]:
    """获取订单的首位客户姓名与护照 (name, passport)。失败返回 (None, None)。"""
    conn = None
    try:
        conn = get_db_connection()
        if not conn:
            return None, None
        with conn.cursor() as cur:
            cur.execute(
                "SELECT name, passport FROM clients WHERE order_id=%s ORDER BY id LIMIT 1",
                (order_id,)
            )
            r = cur.fetchone()
            return (r[0], r[1]) if r else (None, None)
    except Exception as e:
        logger.warning(f"获取客户信息失败 | 订单: {order_id} | 错误: {e}")
        return None, None
    finally:
        if conn:
            conn.close()


# ===================== 日期转换 =====================

def convert_ddmmyyyy_to_yyyy_mm_dd(date_str: str) -> Optional[str]:
    """将 dd/mm/yyyy 转为 yyyy-mm-dd；已是yyyy-mm-dd则原样返回；失败返回None。"""
    try:
        if not date_str or not isinstance(date_str, str):
            return None
        s = date_str.strip()
        if len(s) >= 10 and s[4] == '-' and s[7] == '-':
            return s[:10]
        s = s.replace('\\', '/')
        dt = datetime.strptime(s, "%d/%m/%Y")
        return dt.strftime("%Y-%m-%d")
    except Exception as e:
        logger.warning(f"预约日期格式解析失败 | 原始: {date_str} | 错误: {e}")
        return None


# ===================== 企微通知 =====================

def send_wecom_notify_waitlist_success(order_id: str, mission_code: str, center_code: str, visa_code: str, first_client: dict, message: str) -> bool:
    """候补成功后发送企微通知。"""
    try:
        operator_id = get_operator_id_for_order(order_id)
        if not operator_id:
            logger.warning(f"未找到订单的operator_id/user_id，跳过发送企微消息 | 订单: {order_id}")
            return False
        hooks = get_wecom_hooks_for_user(operator_id)
        if not hooks:
            logger.warning(f"该用户无可用企微Hook，跳过通知 | 用户: {operator_id} | 订单: {order_id}")
            return False
        hook_url = hooks[0]
        # 客户姓名与护照
        name = first_client.get('name') or (f"{first_client.get('firstname_pinyin', '')} {first_client.get('surname_pinyin', '')}".strip())
        passport = first_client.get('passport', '')
        mission_name = get_mission_name(mission_code)
        center_name = get_center_name(center_code, mission_code)
        visa_name = get_visa_name(mission_code, center_code, visa_code)
        content_lines = [
            f"订单 {order_id} {message}",
            f"客户：{name}",
            f"护照：{passport}",
            f"国家：{mission_name}",
            f"中心：{center_name}",
            f"签证类型：{visa_name}",
        ]
        text = "\n".join(content_lines)
        payload = {"msgtype": "text", "text": {"content": text}}
        resp = requests.post(hook_url, json=payload, timeout=10)
        logger.info(f"📨 候补成功企微通知已发送 | 订单: {order_id} | Hook: {hook_url[:60]}... | 状态码: {resp.status_code}")
        return True
    except Exception as e:
        logger.error(f"发送候补成功企微通知失败 | 订单: {order_id} | 错误: {e}")
        return False


def send_wecom_notify_for_order(order_id: str, customer: dict, schedule_result: dict) -> bool:
    """预约成功后发送企微通知（日期转换为 yyyy-mm-dd）。"""
    try:
        operator_id = get_operator_id_for_order(order_id)
        if not operator_id:
            logger.warning(f"未找到订单的operator_id/user_id，跳过发送企微消息 | 订单: {order_id}")
            return False
        hooks = get_wecom_hooks_for_user(operator_id)
        if not hooks:
            logger.warning(f"该用户无可用企微Hook，跳过通知 | 用户: {operator_id} | 订单: {order_id}")
            return False
        hook_url = hooks[0]

        mission_code = customer.get('mission_code')
        center_code = customer.get('center_code')
        visa_code = customer.get('visa_code')
        mission_name = get_mission_name(mission_code)
        center_name = get_center_name(center_code, mission_code)
        visa_name = get_visa_name(mission_code, center_code, visa_code)
        name, passport = get_primary_client(order_id)

        content_lines = [f"订单 {order_id} 预约成功"]
        if name:
            content_lines.append(f"客户：{name}")
        if passport:
            content_lines.append(f"护照：{passport}")

        appt_date_raw = schedule_result.get('appointmentDate')
        appt_date_fmt = convert_ddmmyyyy_to_yyyy_mm_dd(appt_date_raw) or appt_date_raw
        content_lines.extend([
            f"国家：{mission_name}",
            f"中心：{center_name}",
            f"签证类型：{visa_name}",
            f"日期：{appt_date_fmt}",
            f"时间：{schedule_result.get('appointmentTime')}",
        ])
        content_lines.append("需支付：是" if bool(schedule_result.get('IsPaymentRequired')) else "需支付：否")
        text = "\n".join(content_lines)

        payload = {"msgtype": "text", "text": {"content": text}}
        resp = requests.post(hook_url, json=payload, timeout=10)
        logger.info(f"📨 企微通知已发送 | 订单: {order_id} | Hook: {hook_url[:60]}... | 状态码: {resp.status_code}")
        return True
    except Exception as e:
        logger.error(f"发送企微通知失败 | 订单: {order_id} | 错误: {e}")
        return False

