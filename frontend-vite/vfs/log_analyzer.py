#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VFS集成日历扫描器日志分析工具
"""
import re
import json
from datetime import datetime
from collections import defaultdict, Counter
import sys

class VFSLogAnalyzer:
    """VFS日志分析器"""
    
    def __init__(self):
        self.log_entries = []
        self.scan_sessions = defaultdict(list)
        self.statistics = {
            'total_scans': 0,
            'successful_calendar_scans': 0,
            'successful_timeslot_requests': 0,
            'failed_calendar_scans': 0,
            'failed_timeslot_requests': 0,
            'countries': Counter(),
            'orders': Counter(),
            'response_times': [],
            'error_types': Counter()
        }
    
    def parse_log_line(self, line):
        """解析单行日志"""
        # 日志格式: 2024-12-08 16:45:00 | INFO | ThreadName | FunctionName | Message
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| (\w+)\s*\| ([^|]+) \| ([^|]+) \| (.+)'
        match = re.match(pattern, line.strip())
        
        if match:
            timestamp_str, level, thread, function, message = match.groups()
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
            
            return {
                'timestamp': timestamp,
                'level': level.strip(),
                'thread': thread.strip(),
                'function': function.strip(),
                'message': message.strip(),
                'raw_line': line.strip()
            }
        return None
    
    def extract_order_id(self, message):
        """从消息中提取订单ID"""
        pattern = r'订单:\s*([^\s|]+)'
        match = re.search(pattern, message)
        return match.group(1) if match else None
    
    def extract_country(self, message):
        """从消息中提取国家代码"""
        pattern = r'国家:\s*([^\s|]+)'
        match = re.search(pattern, message)
        return match.group(1) if match else None
    
    def extract_response_time(self, message):
        """从消息中提取响应时间"""
        pattern = r'耗时:\s*([0-9.]+)ms'
        match = re.search(pattern, message)
        return float(match.group(1)) if match else None
    
    def analyze_log_file(self, log_file_path):
        """分析日志文件"""
        print(f"📊 开始分析日志文件: {log_file_path}")
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"📋 总日志行数: {len(lines)}")
            
            for line in lines:
                entry = self.parse_log_line(line)
                if entry:
                    self.log_entries.append(entry)
                    self.analyze_entry(entry)
            
            print(f"✅ 成功解析日志条目: {len(self.log_entries)}")
            
        except Exception as e:
            print(f"❌ 日志文件分析失败: {e}")
            return False
        
        return True
    
    def analyze_entry(self, entry):
        """分析单个日志条目"""
        message = entry['message']
        
        # 提取订单ID和国家
        order_id = self.extract_order_id(message)
        country = self.extract_country(message)
        response_time = self.extract_response_time(message)
        
        if order_id:
            self.statistics['orders'][order_id] += 1
        
        if country:
            self.statistics['countries'][country] += 1
        
        if response_time:
            self.statistics['response_times'].append(response_time)
        
        # 分析不同类型的操作
        if '📅 开始日历扫描' in message:
            self.statistics['total_scans'] += 1
            if order_id:
                self.scan_sessions[order_id].append({
                    'type': 'calendar_start',
                    'timestamp': entry['timestamp'],
                    'country': country
                })
        
        elif '✅ 日历扫描成功' in message:
            self.statistics['successful_calendar_scans'] += 1
            if order_id:
                self.scan_sessions[order_id].append({
                    'type': 'calendar_success',
                    'timestamp': entry['timestamp'],
                    'message': message
                })
        
        elif '❌ 日历无可用日期' in message:
            self.statistics['failed_calendar_scans'] += 1
            if order_id:
                self.scan_sessions[order_id].append({
                    'type': 'calendar_no_dates',
                    'timestamp': entry['timestamp']
                })
        
        elif '⏰ 开始时间段请求' in message:
            if order_id:
                self.scan_sessions[order_id].append({
                    'type': 'timeslot_start',
                    'timestamp': entry['timestamp']
                })
        
        elif '✅ 时间段获取成功' in message:
            self.statistics['successful_timeslot_requests'] += 1
            if order_id:
                self.scan_sessions[order_id].append({
                    'type': 'timeslot_success',
                    'timestamp': entry['timestamp'],
                    'message': message
                })
        
        elif '❌ 时间段无可用' in message:
            self.statistics['failed_timeslot_requests'] += 1
            if order_id:
                self.scan_sessions[order_id].append({
                    'type': 'timeslot_no_slots',
                    'timestamp': entry['timestamp']
                })
        
        elif '🎉 完整扫描成功' in message:
            if order_id:
                self.scan_sessions[order_id].append({
                    'type': 'complete_success',
                    'timestamp': entry['timestamp'],
                    'message': message
                })
        
        # 分析错误
        if entry['level'] in ['ERROR', 'WARNING']:
            error_type = 'Unknown'
            if '日历扫描' in message:
                error_type = 'Calendar Scan Error'
            elif '时间段' in message:
                error_type = 'Timeslot Request Error'
            elif '数据库' in message:
                error_type = 'Database Error'
            elif '配置' in message:
                error_type = 'Configuration Error'
            
            self.statistics['error_types'][error_type] += 1
    
    def generate_report(self):
        """生成分析报告"""
        print("\n" + "=" * 80)
        print("📊 VFS集成日历扫描器日志分析报告")
        print("=" * 80)
        
        # 基本统计
        print(f"📋 基本统计:")
        print(f"  总日志条目: {len(self.log_entries)}")
        print(f"  总扫描次数: {self.statistics['total_scans']}")
        print(f"  成功日历扫描: {self.statistics['successful_calendar_scans']}")
        print(f"  失败日历扫描: {self.statistics['failed_calendar_scans']}")
        print(f"  成功时间段请求: {self.statistics['successful_timeslot_requests']}")
        print(f"  失败时间段请求: {self.statistics['failed_timeslot_requests']}")
        
        # 成功率计算
        if self.statistics['total_scans'] > 0:
            calendar_success_rate = (self.statistics['successful_calendar_scans'] / 
                                   self.statistics['total_scans']) * 100
            print(f"  日历扫描成功率: {calendar_success_rate:.1f}%")
        
        total_timeslot_requests = (self.statistics['successful_timeslot_requests'] + 
                                 self.statistics['failed_timeslot_requests'])
        if total_timeslot_requests > 0:
            timeslot_success_rate = (self.statistics['successful_timeslot_requests'] / 
                                   total_timeslot_requests) * 100
            print(f"  时间段请求成功率: {timeslot_success_rate:.1f}%")
        
        # 国家统计
        print(f"\n🌍 国家扫描统计:")
        for country, count in self.statistics['countries'].most_common(10):
            print(f"  {country}: {count} 次")
        
        # 订单统计
        print(f"\n📋 订单扫描统计 (前10个):")
        for order_id, count in self.statistics['orders'].most_common(10):
            print(f"  {order_id}: {count} 次")
        
        # 响应时间统计
        if self.statistics['response_times']:
            response_times = self.statistics['response_times']
            avg_time = sum(response_times) / len(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            
            print(f"\n⏱️ 响应时间统计:")
            print(f"  平均响应时间: {avg_time:.1f}ms")
            print(f"  最快响应时间: {min_time:.1f}ms")
            print(f"  最慢响应时间: {max_time:.1f}ms")
            print(f"  响应时间样本数: {len(response_times)}")
        
        # 错误统计
        if self.statistics['error_types']:
            print(f"\n❌ 错误类型统计:")
            for error_type, count in self.statistics['error_types'].most_common():
                print(f"  {error_type}: {count} 次")
        
        # 扫描会话分析
        self.analyze_scan_sessions()
    
    def analyze_scan_sessions(self):
        """分析扫描会话"""
        print(f"\n🔍 扫描会话分析:")
        
        complete_sessions = 0
        incomplete_sessions = 0
        successful_sessions = 0
        
        for order_id, events in self.scan_sessions.items():
            has_calendar_start = any(e['type'] == 'calendar_start' for e in events)
            has_complete_success = any(e['type'] == 'complete_success' for e in events)
            
            if has_calendar_start:
                if has_complete_success:
                    complete_sessions += 1
                    successful_sessions += 1
                else:
                    incomplete_sessions += 1
        
        total_sessions = complete_sessions + incomplete_sessions
        
        print(f"  总扫描会话: {total_sessions}")
        print(f"  完整会话: {complete_sessions}")
        print(f"  不完整会话: {incomplete_sessions}")
        print(f"  成功会话: {successful_sessions}")
        
        if total_sessions > 0:
            session_success_rate = (successful_sessions / total_sessions) * 100
            print(f"  会话成功率: {session_success_rate:.1f}%")
        
        # 显示最近的成功会话
        print(f"\n🎉 最近的成功扫描 (前5个):")
        recent_successes = []
        
        for order_id, events in self.scan_sessions.items():
            success_events = [e for e in events if e['type'] == 'complete_success']
            for event in success_events:
                recent_successes.append({
                    'order_id': order_id,
                    'timestamp': event['timestamp'],
                    'message': event['message']
                })
        
        # 按时间排序，取最近的5个
        recent_successes.sort(key=lambda x: x['timestamp'], reverse=True)
        
        for i, success in enumerate(recent_successes[:5]):
            print(f"  {i+1}. {success['timestamp']} | 订单: {success['order_id']}")
            # 提取关键信息
            message = success['message']
            if '日期:' in message:
                date_match = re.search(r'日期:\s*([^\s|]+)', message)
                if date_match:
                    print(f"     📅 日期: {date_match.group(1)}")
            if '时间段:' in message:
                slots_match = re.search(r'时间段:\s*([^\s|]+)', message)
                if slots_match:
                    print(f"     ⏰ 时间段: {slots_match.group(1)}")


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python log_analyzer.py <日志文件路径>")
        print("示例: python log_analyzer.py vfs_scanner.log")
        sys.exit(1)
    
    log_file_path = sys.argv[1]
    
    analyzer = VFSLogAnalyzer()
    
    if analyzer.analyze_log_file(log_file_path):
        analyzer.generate_report()
    else:
        print("❌ 日志分析失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
