import random
import string
import time
import signal
import sys
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import json
from curl_cffi import requests
from RedisClient import RedisClient
# 移除selenium相关导入

# 初始化Redis客户端
redis_client = RedisClient()


def get_proxy():
    session = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(11))
    # ip = f"http://brd-customer-hl_c9d2b997-zone-residential_proxy8-country-de-session-{session}:<EMAIL>:33335"
    # ip = f"http://kq123456-zone-resi-region-de-session-{session}-sessTime-120:<EMAIL>:16666"
    ip = f"http://t15117693479940-period-5-sid-{session}:<EMAIL>:15818"
    return {
        "http": ip,
        "https": ip,
    }


def get_proxy_ip_info(proxy_url, user_agent, timeout=30):
    """获取代理的IP信息"""
    try:
        # 设置代理
        proxies = {
            "http": proxy_url,
            "https": proxy_url,
        }

        # 设置请求头
        headers = {
            "User-Agent": user_agent,
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
        }

        # 发送请求获取IP信息
        response = requests.get(
            "http://ipinfo.pyproxy.io",
            headers=headers,
            proxies=proxies,
            timeout=timeout,
            impersonate="chrome"
        )

        if response.status_code == 200:
            return response.json()
        else:
            print(f"获取IP信息失败，状态码: {response.status_code}")
            return None

    except Exception as e:
        print(f"获取IP信息异常: {str(e)}")
        return None


def get_cf_cookie(url, proxy, max_retries=1):
    global cf_server, stop_flag

    for attempt in range(max_retries):
        if stop_flag:  # 检查停止标志
            return None

        try:
            data = {
                "url": url,
                'proxy': proxy,
                'token': 'ashd91789h89j9idh17892hd98j90j'
            }
            resp = requests.post(
                f"http://{cf_server}:5005/get_cookie",
                json=data,
                headers={"Content-Type": "application/json"},
                timeout=90  # 减少超时时间，让停止更快响应
            )

            print(f"尝试 {attempt + 1}/{max_retries} - 状态码: {resp.status_code}")

            if resp.status_code == 200:
                try:
                    response_data = resp.json()
                    if 'data' in response_data and 'ua' in response_data['data'] and 'cookie' in response_data['data']:
                        user_agent = response_data['data']['ua']
                        cookie = response_data['data']['cookie']
                        print(f"Cookie获取成功!")
                        return {'user_agent': user_agent, 'cookie': cookie}
                    else:
                        print(f"响应格式错误: {resp.text}")
                except Exception as json_error:
                    print(f"JSON解析错误: {json_error}, 响应: {resp.text}")
            else:
                print(f"HTTP错误 {resp.status_code}: {resp.text}")

        except Exception as e:
            print(f"请求异常 (尝试 {attempt + 1}/{max_retries}): {str(e)}")

        if attempt < max_retries - 1:
            print(f"等待0.1秒后重试...")
            time.sleep(0.1)

    print(f"获取CF cookie失败，已重试 {max_retries} 次")
    return None


# 全局停止标志
stop_flag = False


def signal_handler(signum, frame):
    global stop_flag
    print(f"\n收到停止信号 ({signum})，正在优雅地关闭所有线程...")
    stop_flag = True


def main(url, thread_id):
    """单个线程的主要执行函数 - 不停地获取cookie"""
    global stop_flag
    print(f"线程 {thread_id}: 开始不停地获取CF cookie...")

    success_count = 0
    fail_count = 0

    while not stop_flag:
        try:
            # 每次循环创建新的proxy
            thread_proxy = get_proxy()

            print(f"线程 {thread_id}: 第 {success_count + fail_count + 1} 次尝试获取CF cookie...")
            cookie_start = time.time()
            result = get_cf_cookie(url, thread_proxy['http'])
            cookie_duration = time.time() - cookie_start

            if result is None:
                fail_count += 1
                print(f"❌ 线程 {thread_id}: 第 {success_count + fail_count} 次获取失败，耗时 {cookie_duration:.2f}秒 (成功:{success_count}, 失败:{fail_count})")
            else:
                success_count += 1
                print(f"✅ 线程 {thread_id}: 第 {success_count + fail_count} 次获取成功！耗时 {cookie_duration:.2f}秒 (成功:{success_count}, 失败:{fail_count})")
                print(f"   User-Agent: {result['user_agent']}")
                print(f"   CF Cookie: {result['cookie']}")
                print(f"   代理: {thread_proxy['http']}")

                # 获取代理的IP信息
                # print(f"   正在获取代理IP信息...")
                # ip_info = get_proxy_ip_info(thread_proxy['http'], result['user_agent'])

                # if ip_info:
                #     print(f"   代理IP: {ip_info.get('ip', 'Unknown')}")
                #     print(f"   位置: {ip_info.get('country', 'Unknown')}, {ip_info.get('region', 'Unknown')}, {ip_info.get('city', 'Unknown')}")
                #     print(f"   ISP: {ip_info.get('org', 'Unknown')}")
                # else:
                #     print(f"   代理IP: 获取失败")

                print("-" * 80)
                cf_id = f"cf_{thread_id}_{int(time.time())}"

                # 构建要存储的数据
                data_to_store = {
                    "cookie": result['cookie'],
                    "proxy": thread_proxy['http'],
                    "user_agent": result['user_agent'],
                    "key": cf_id
                }

                # 如果成功获取IP信息，添加到存储数据中
                # if ip_info:
                #     data_to_store.update({
                #         "ip": ip_info.get('ip', 'Unknown'),
                #         "country": ip_info.get('country', 'Unknown'),
                #         "region": ip_info.get('region', 'Unknown'),
                #         "city": ip_info.get('city', 'Unknown'),
                #         "isp": ip_info.get('org', 'Unknown'),
                #         "ip_check_time": time.strftime('%Y-%m-%d %H:%M:%S')
                #     })
                redis_client.client.setex(f'cf_cookie:{cf_id}', 300, json.dumps(data_to_store))

            # 短暂休息，但要检查停止标志
            for _ in range(10):  # 将1秒分成10个0.1秒
                if stop_flag:
                    break
                time.sleep(0.1)

        except KeyboardInterrupt:
            print(f"\n线程 {thread_id}: 收到停止信号，正在退出...")
            print(f"线程 {thread_id}: 最终统计 - 成功:{success_count}, 失败:{fail_count}")
            break
        except Exception as e:
            fail_count += 1
            print(f"❌ 线程 {thread_id}: 第 {success_count + fail_count} 次异常失败: {str(e)} (成功:{success_count}, 失败:{fail_count})")
            # 异常时也要检查停止标志
            for _ in range(20):  # 将2秒分成20个0.1秒
                if stop_flag:
                    break
                time.sleep(0.1)

    print(f"线程 {thread_id}: 已停止 - 最终统计: 成功:{success_count}, 失败:{fail_count}")


def run_concurrent_requests(url, num_threads=30):
    """运行并发cookie获取 - 持续运行模式"""
    global cf_server, stop_flag
    print(f"开始执行 {num_threads} 个并发线程持续获取CF cookie...")
    print("按 Ctrl+C 停止程序")
    print("="*80)

    overall_start_time = time.time()

    try:
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            # 提交所有线程任务
            futures = []
            for i in range(num_threads):
                print(f"启动线程 {i+1}...")
                future = executor.submit(main, url, i+1)
                futures.append(future)

            print(f"所有 {num_threads} 个线程已启动，开始持续获取CF cookie...")
            print("="*80)

            # 等待停止信号
            try:
                while not stop_flag:
                    time.sleep(0.1)  # 短暂休息，快速响应停止信号

                print(f"\n停止信号已收到，等待线程结束...")

                # 等待所有线程结束（最多等待5秒）
                start_wait = time.time()
                while time.time() - start_wait < 5:
                    all_done = True
                    for future in futures:
                        if not future.done():
                            all_done = False
                            break
                    if all_done:
                        break
                    time.sleep(0.1)

                # 强制取消仍在运行的线程
                for future in futures:
                    if not future.done():
                        future.cancel()

            except KeyboardInterrupt:
                print(f"\n收到额外的停止信号，强制退出...")
                stop_flag = True
                for future in futures:
                    future.cancel()

    except KeyboardInterrupt:
        print(f"\n程序被用户中断")
        stop_flag = True

    overall_end_time = time.time()
    overall_duration = overall_end_time - overall_start_time

    print("\n" + "="*50)
    print("程序运行统计:")
    print(f"总运行时间: {overall_duration:.2f}秒")
    print("程序已完全停止")
    print("="*50)


if __name__ == '__main__':
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)

    cf_server = '47.254.131.227'
    url = "https://lift-apicn.vfsglobal.com/appointment/schedule"

    # 运行30个并发线程持续获取cookie
    run_concurrent_requests(url, 2)
