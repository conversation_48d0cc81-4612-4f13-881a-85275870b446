# -*- coding: utf-8 -*-
import threading
import queue
from queue import Queue
from curl_cffi import requests
import time as tm
import json
import random
import re
from RedisClientAWS import RedisClient
from datetime import datetime, time, timedelta
import string

import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding

# 创建 RedisClient 实例
redis_client = RedisClient()


def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


source_rsa_str = redis_client.get('rsa_str')
rsa_string = format_rsa_string(source_rsa_str)


def encryption(t):
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


def generate_random_string(length=31):
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string


delegate = json.loads(redis_client.get("fast_proxy"))

# 正在处理或等待处理的用户集合
processing_users = set()

loginUsers = [item for item in redis_client.hgetall("cheLoginUser") if item.get('updateTokenTime') != None and int(tm.time()) - item.get('updateTokenTime') < 6000 and int(tm.time()) - item.get('updateTokenTime') > 60]


def refreshloginUsers():
    while True:
        global loginUsers
        global rsa_string
        source_rsa_str = redis_client.get('rsa_str')
        rsa_string = format_rsa_string(source_rsa_str)
        loginUsers = [item for item in redis_client.hgetall("cheLoginUser") if item.get('updateTokenTime') != None and int(tm.time()) - item.get('updateTokenTime') < 6000 and int(tm.time()) - item.get('updateTokenTime') > 60]
        tm.sleep(10)


threading.Thread(target=refreshloginUsers).start()


def is_earlier_than(time_a_str, time_b_str):
    # 将时间字符串解析为datetime对象
    time_format = "%d/%m/%Y"
    time_a = datetime.strptime(time_a_str, time_format)
    time_b = datetime.strptime(time_b_str, time_format)

    # 比较时间
    if time_a < time_b:
        return True
    else:
        return False


def pick_random_elements(arr, num):
    if num >= len(arr):
        return arr[:]
    shuffled = arr[:]
    random.shuffle(shuffled)
    return shuffled[:num]


def pick_random_url():
    list = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=020a8d0b-6285-41be-972a-4997494056e8",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=84a39ea8-6354-46ef-991d-bc7e0fea0066",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d842efa5-61cf-4c64-94ba-3b0008aeeadf",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=aa1ad9d3-dea9-49f1-a839-64f9227aa91a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7af17a71-e1ee-4e2c-a3fa-17455515b72e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b1c8fe9e-1a96-494e-aa6a-800aa2e311e2",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=70669e7d-abef-4c9a-85f6-3ffb93cc57a6",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=de03a1c0-e4e1-4bdb-abd9-82755c0acfa2",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f1d752d6-68e0-4cef-a739-a2ed7e68836c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=da064818-0822-4853-bf73-8904815bf592",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a55180cb-c0b7-4056-beaf-a09afb7f18b0",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=586fe69a-299e-4a7c-a5cc-ef6eaeebd19b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=606ad073-2903-4eaf-b763-c08bd1dd5737",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2a52ffb3-ccb7-4ac0-b6ee-9e32bb4337b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2da9b32e-82b5-45d9-844b-98bc75e06194",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b949bd7c-0117-4d82-a2e9-4403e70093f1",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2217904d-c443-436c-9779-f75d46336002",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=40e794bb-1fc2-46f2-bd11-8f4e87d05ef9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=16c5f10e-fbbc-4e77-b805-ef04a67dc829",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a192259d-64dc-46ed-bea3-51ffcf36ee4e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5789ee00-e168-49e5-a554-233df42d204b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ec3915f6-8900-4800-83df-2e9cb40d23b8",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b5265a1f-afc8-4fc6-a23f-4774f12f5a8c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=355d0c0a-e964-4a88-9b3d-93eddfa10118",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=254f174a-58f3-4e24-a005-a0eb1b4f0e14",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7b816326-5e5c-441d-aced-fc3d186addfe",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8532ecc9-6a45-40ab-bd57-93b97fd9cca1",
    ]

    return random.choice(list)


def randomize_case(input_string):
    # 将每个字符随机转换为大写或小写
    randomized_string = "".join(
        char.upper() if random.choice([True, False]) else char.lower()
        for char in input_string
    )
    return randomized_string


def attempt_change_visa_type(user, max_retries, retry_interval):
    for _ in range(max_retries):
        res = change_visa_type(user)
        if res:
            return res
        tm.sleep(retry_interval)
    return ""


def change_visa_type(user):
    if len(loginUsers) == 0:
        tm.sleep(1)
        return
    account = random.choice(loginUsers)
    try:
        url = f"https://lift-apicn.vfsglobal.com/appointment/updatewaitlistvisa"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "route": "chn/zh/che",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }

        data = {
            "missionCode": "che",
            "countryCode": "chn",
            "centerCode": "PVG",
            "loginUser": account.get("email"),
            "lOGINUser": user.get('wemail'),
            "urn": user.get('wurn'),
            "visaCategoryCode": "BUSTRA",
        }
        # data[f"{randomize_case('loginUser')}"] = user["loginUser"]
        print(f"正在切换客户类型{user['chnname']}，护照:{user['passportNO']}")
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
        )
        if response.status_code == 200:
            data = response.json()
            print(data)
            if data["IsUpdatedWaitlistVisa"] == True or data.get('error').get('description') == 'The Visa Category has already been updated.':
                return True
            else:
                return False
        elif response.status_code == 401:
            print(response.status_code)
            return False
        elif response.status_code == 400:
            print(response.status_code)
            return False
        else:
            print(response.status_code)
            return False
    except Exception as e:
        print(e)
        return False


def get_handshake(user, applicant):
    if len(loginUsers) == 0:
        tm.sleep(1)
        return
    account = random.choice(loginUsers)
    try:
        url = f"https://lift-apicn.vfsglobal.com/appointment/gethandshake"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "route": "chn/zh/che",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": account['ltsn']
        }
        data = {
            "countryCode": "chn",
            "missionCode": user["missionCode"],
            "centerCode": user["centerCode"],
            "loginUser": account.get("email"),
            "lOGinusEr": user["loginUser"],
            "aurn": applicant["arn"],
            "languageCode": "zh-CN",
        }
        # data[f"{randomize_case('loginUser')}"] = user["loginUser"]
        print(f"正在获取握手{user['chnname']}，护照:{user['passportNO']}")
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
        )
        if response.status_code == 200:
            res = response.json()
            # {
#     "handshakeURL": "https://eforms.vfsglobal.com/che/en/auth",
#     "handshakePayload": "6WCnSMej0Wxx1ye40XCFjEReSznEmhzBjnEzzSU2UuyirJDZgZmeWBzDAYXK2vUPDZiaErtowbIJgEvKePHhmKIIEUazhcioTSLOkdw404O%2fD0VcAC2wLBlVjPC5LHYAUE1h8E%2b8GLqTImwN455FcT2JWZ%2bGY%2fmN%2f1DU0LG5q1tspkHGEK6AqUpv4gP2VHZ9%2bkVPSFDcylIp5h8NKahFHUNFshuROOuMkHZcxsrSQtUO1kQDBWig1BExgIkNYzb%2bGQR0nGTBhyJif2bNY6c%2fu8kApI6A1Sp2h4wWa9eGD4IexUld5Z86a7e1yC5%2f%2fbLWuW54zmv7Y1rLoN3tEOx3yVWV%2baLAQiUfJ%2b2l6kTMtrjylTKyemlfv0g40ucENiqlAiRwEPaXy%2bdY72ndWmv8WpON8DzR%2fCy5CYPBszbMoBpsJuwz49lXLKXtyXo91enAhkZw8MIx%2f7hlNbPcn%2btKg1BZ0gmxXZZzf0eSjovc1qOnAnqevKtKj1RGEHcfHZKquPKZnW6rmwY76ZDX0trdaX9RJ8QdZ9IeK8CoHyjmxJEhDhds9scAs%2fY4I5N4DB6NzLIBNR0L91DxmePvkPSK2dsppyFgdzGEjbhan6G7PkhscHdhczb3PhSRbLsD%2fBL0tCf%2bBIOuuwZRNfhKFkGqJF9E2Ogesbrm1RBHCsI2nGbaQOWfvNPMq%2fCzZvc%2bB8svSOnOYqdzfLOwp8XDdPsjFEuQJPOQMhZ0gIU%2faVSYtnOCdpjc7mp0HIgUTU4nHK2HTH0iAN3tCHO29vPIq7OyfclwU1n4%2b5IIT5n7ZJOd6fa7RGlDYHQV8ldb0V6g6b2SPOdGh80Tlsl0FoaM6odX7dr1p0upZxHpLWLLeKHQAQ4zb2zTnYfpKTGiqC%2b9ek9YMUFQN56%2bEATIRHvmqkhRrjIA3SHxuiUIUzogO%2b%2bjc%2bxBhLTSYBxfVxTag3TSh4ygRuxy3QYMEqHLBl%2f8jMaAQYDyJYV3yESL8FZcZQ%2f6ZFSbzjXWrLNcpaE9sobX9woUURiovkqssOqdS99o2aRdVyewAx4f596yztCYLHQFWeqCsT6tuQ6yjZ60ebm8qVH8ZmgaaIq7Z8hrIJ7AOTIhdJxAU1liN%2fW5ZtJG3YJWFqMtT0yUyQIWWOlMHqhMbCmQfWYZ8zMyedypoHJUBzQmPWF6GEXgwTJwOy1efEb4sGuWWMfpaz6YqgGBRqSUv%2bRIAfdGAQd%2f2xr8DIdL%2fe4W7hxWS0Ler0eTa1jJE32G61BT7eJarkWqqfpF8vr82nnFMXUaiqkATW4Fe2P9LkXzrFk1pTjQsfsBRE21%2bC1h8z49Q2h9E1Uj04lr1rpnL03QBBcE%2b5MAQ7CXxt647p4%2bCxikqV%2faaxMAAH%2babkc5g7DaQQ85QTAw8BEIZbrgc5hBdEl60edj%2b7hORJ3U9DYYPXySG96Dj3TvL5Dex%2fA52L%2bPOy%2f2MG%2bIdT5xqLKNaIuO",
#     "error": null
# }
            print(res)
            if res.get("handshakeURL") != None:
                print(f"客户{user.get('chnname')}成功获取握手", res)
                return res
            return False
        else:
            print(response.status_code)
        return False
    except Exception as e:
        print(f"获取握手失败，可以重试{e}")
        return False


def attempt_get_handshake(user, applicant, max_retries, retry_interval):
    for _ in range(max_retries):
        res = get_handshake(user, applicant)
        if res:
            return res
        tm.sleep(retry_interval)
    return ""

# curl ^"https://eu-api-app.vfsevisa.com/v1/api/auth/external-login^" ^
#   -H ^"accept: application/json, text/plain, */*^" ^
#   -H ^"accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6^" ^
#   -H ^"content-type: application/json^" ^
#   -H ^"origin: https://eforms.vfsglobal.com^" ^
#   -H ^"priority: u=1, i^" ^
#   -H ^"referer: https://eforms.vfsglobal.com/^" ^
#   -H ^"sec-ch-ua: ^\^"Not)A;Brand^\^";v=^\^"8^\^", ^\^"Chromium^\^";v=^\^"138^\^", ^\^"Microsoft Edge^\^";v=^\^"138^\^"^" ^
#   -H ^"sec-ch-ua-mobile: ?0^" ^
#   -H ^"sec-ch-ua-platform: ^\^"Windows^\^"^" ^
#   -H ^"sec-fetch-dest: empty^" ^
#   -H ^"sec-fetch-mode: cors^" ^
#   -H ^"sec-fetch-site: cross-site^" ^
#   -H ^"user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********^" ^
#   --data-raw ^"^{^\^"handshakePayload^\^":^\^"6WCnSMej0Wxx1ye40XCFjEReSznEmhzBjnEzzSU2UuyirJDZgZmeWBzDAYXK2vUPDZiaErtowbIJgEvKePHhmKIIEUazhcioTSLOkdw404O^%^2FD0VcAC2wLBlVjPC5LHYAUE1h8E^%^2B8GLqTImwN455FcT2JWZ^%^2BGY^%^2FmN^%^2F1DU0LG5q1tspkHGEK6AqUpv4gP2VHZ9^%^2BkVPSFDcylIp5h8NKahFHUNFshuROOuMkHZcxsrSQtUO1kQDBWig1BExgIkNYzb^%^2BGQR0nGTBhyJif2bNY6c^%^2Fu8kApI6A1Sp2h4wWa9eGD4IexUld5Z86a7e1yC5^%^2F^%^2FbLWuW54zmv7Y1rLoN3tEOx3yVWV^%^2BaLAQiUfJ^%^2B2l6kTMtrjylTKyemlfv0g40ucENiqlAiRwEPaXy^%^2BdY72ndWmv8WpON8DzR^%^2FCy5CYPBszbMoBpsJuwz49lXLKXtyXo91enAhkZw8MIx^%^2F7hlNbPcn^%^2BtKg1BZ0gmxXZZzf0eSjovc1qOnAnqevKtKj1RGEHcfHZKquPKZnW6rmwY76ZDX0trdaX9RJ8QdZ9IeK8CoHyjmxJEhDhds9scAs^%^2FY4I5N4DB6NzLIBNR0L91DxmePvkPSK2dsppyFgdzGEjbhan6G7PkhscHdhczb3PhSRbLsD^%^2FBL0tCf^%^2BBIOuuwZRNfhKFkGqJF9E2Ogesbrm1RBHCsI2nGbaQOWfvNPMq^%^2FCzZvc^%^2BB8svSOnOYqdzfLOwp8XDdPsjFEuQJPOQMhZ0gIU^%^2FaVSYtnOCdpjc7mp0HIgUTU4nHK2HTH0iAN3tCHO29vPIq7OyfclwU1n4^%^2B5IIT5n7ZJOd6fa7RGlDYHQV8ldb0V6g6b2SPOdGh80Tlsl0FoaM6odX7dr1p0upZxHpLWLLeKHQAQ4zb2zTnYfpKTGiqC^%^2B9ek9YMUFQN56^%^2BEATIRHvmqkhRrjIA3SHxuiUIUzogO^%^2B^%^2Bjc^%^2BxBhLTSYBxfVxTag3TSh4ygRuxy3QYMEqHLBl^%^2F8jMaAQYDyJYV3yESL8FZcZQ^%^2F6ZFSbzjXWrLNcpaE9sobX9woUURiovkqssOqdS99o2aRdVyewAx4f596yztCYLHQFWeqCsT6tuQ6yjZ60ebm8qVH8ZmgaaIq7Z8hrIJ7AOTIhdJxAU1liN^%^2FW5ZtJG3YJWFqMtT0yUyQIWWOlMHqhMbCmQfWYZ8zMyedypoHJUBzQmPWF6GEXgwTJwOy1efEb4sGuWWMfpaz6YqgGBRqSUv^%^2BRIAfdGAQd^%^2F2xr8DIdL^%^2Fe4W7hxWS0Ler0eTa1jJE32G61BT7eJarkWqqfpF8vr82nnFMXUaiqkATW4Fe2P9LkXzrFk1pTjQsfsBRE21^%^2BC1h8z49Q2h9E1Uj04lr1rpnL03QBBcE^%^2B5MAQ7CXxt647p4^%^2BCxikqV^%^2FaaxMAAH^%^2Babkc5g7DaQQ85QTAw8BEIZbrgc5hBdEl60edj^%^2B7hORJ3U9DYYPXySG96Dj3TvL5Dex^%^2FA52L^%^2BPOy^%^2F2MG^%^2BIdT5xqLKNaIuO^\^",^\^"source^\^":^\^"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********^\^",^\^"languageCode^\^":^\^"en^\^",^\^"cultureCode^\^":^\^"en-US^\^",^\^"client^\^":^\^"che^\^"^}^"


def external_login(handshake_res):
    try:
        url = "https://eu-api-app.vfsevisa.com/v1/api/auth/external-login"
        payload = handshake_res.get("handshakePayload")
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://eforms.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://eforms.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "accept": "application/json, text/plain, */*",

            # origin

            # priority
            # u=1, i
            # referer
            # https://eforms.vfsglobal.com/


            # sec-ch-ua-mobile
            # ?0
            # sec-ch-ua-platform
            # "Windows"
            # sec-fetch-dest
            # empty
            # sec-fetch-mode
            # cors
            # sec-fetch-site
            # cross-site
        }
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        data = {
            "client": "che",
            "cultureCode": "zh-CN",
            "languageCode": "zh",
            "source": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "handshakePayload": payload,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
        )
        if response.status_code == 200:
            return response.json()
        else:
            print(response.status_code)
            return False
    except Exception as e:
        print(f"获取external-login失败，可以重试{e}")
        return False


def attempt_external_login(handshake_res, max_retries, retry_interval):

    for _ in range(max_retries):
        res = external_login(handshake_res)
        if res:
            return res
        tm.sleep(retry_interval)
    return ""


def get_form_data(child_user):
    """获取子客户的表单数据"""
    try:
        url = f"http://120.27.241.45:5005/api/schengen/application/{child_user.get('formId')}/steps"
        print(f"获取表单数据: {url} - 客户: {child_user.get('chnname', 'Unknown')}")
        response = requests.get(
            url,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
        )
        if response.status_code == 200:
            return response.json()
        else:
            print(f"获取表单数据失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"获取form-data失败，可以重试{e}")
        return False


def attempt_get_form_data(child_user, max_retries, retry_interval):
    """重试获取子客户的表单数据"""
    for _ in range(max_retries):
        res = get_form_data(child_user)
        if res:
            return res
        tm.sleep(retry_interval)
    return ""


def fill_form(url, data, token):
    try:
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://eforms.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://eforms.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "accept": "application/json, text/plain, */*",
            "authorization": token,
        }
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
        )
        if response.status_code == 200:
            return response.json()
        else:
            print(response.text)
            return False
    except Exception as e:
        print(f"填写表单失败，可以重试{e}")
        return False


def attempt_fill_form(url, data, token, max_retries, retry_interval):
    for _ in range(max_retries):
        res = fill_form(url, data, token)
        if res:
            return res
        tm.sleep(retry_interval)
    return ""


def fill_form_for_child(user, applicant, child_user):
    """为单个子客户填写表单"""
    try:
        print(f"🎯 开始为子客户填写表单 - 主客户: {user.get('chnname')}, 子客户: {child_user.get('chnname')}, 护照: {applicant.get('passportNumber')}")

        # 1. 获取握手
        handshake_res = attempt_get_handshake(user, applicant, 10, 1)
        if handshake_res == "":
            print(f"❌ 获取握手失败 - 子客户: {child_user.get('chnname')}")
            return False

        # 2. 外部登录
        external_login_res = attempt_external_login(handshake_res, 10, 1)
        if external_login_res == "":
            print(f"❌ 外部登录失败 - 子客户: {child_user.get('chnname')}")
            return False

        print(f"✅ 外部登录成功 - 子客户: {child_user.get('chnname')}")
        token = external_login_res['data']['userContext']['token']
        webrefno = applicant.get('arn')

        # 3. 获取子客户的表单数据
        form_data = attempt_get_form_data(child_user, 10, 1)
        if form_data == "":
            print(f"❌ 获取表单数据失败 - 子客户: {child_user.get('chnname')}")
            return False

        print(f"✅ 获取表单数据成功 - 子客户: {child_user.get('chnname')}")

        # 4. 填写6步表单
        success = fill_six_steps_form(form_data, token, webrefno, child_user)

        if success:
            print(f"🎉 表单填写完成 - 子客户: {child_user.get('chnname')}")
            return True
        else:
            print(f"❌ 表单填写失败 - 子客户: {child_user.get('chnname')}")
            return False

    except Exception as e:
        print(f"❌ 填写表单异常 - 子客户: {child_user.get('chnname')}, 错误: {e}")
        return False


def fill_form_for_main_user(user, applicant):
    """为主客户本身填写表单（没有子客户的情况）"""
    try:
        print(f"🎯 开始为主客户填写表单 - 客户: {user.get('chnname')}, 护照: {applicant.get('passportNumber')}")

        # 1. 获取握手
        handshake_res = attempt_get_handshake(user, applicant, 10, 1)
        if handshake_res == "":
            print(f"❌ 获取握手失败 - 主客户: {user.get('chnname')}")
            return False

        # 2. 外部登录
        external_login_res = attempt_external_login(handshake_res, 10, 1)
        if external_login_res == "":
            print(f"❌ 外部登录失败 - 主客户: {user.get('chnname')}")
            return False

        print(f"✅ 外部登录成功 - 主客户: {user.get('chnname')}")
        token = external_login_res['data']['userContext']['token']
        webrefno = applicant.get('arn')

        # 3. 获取主客户的表单数据（使用主客户的formId）
        form_data = attempt_get_form_data(user, 10, 1)
        if form_data == "":
            print(f"❌ 获取表单数据失败 - 主客户: {user.get('chnname')}")
            return False

        print(f"✅ 获取表单数据成功 - 主客户: {user.get('chnname')}")

        # 4. 填写6步表单
        success = fill_six_steps_form(form_data, token, webrefno, user)

        if success:
            print(f"🎉 表单填写完成 - 主客户: {user.get('chnname')}")
            return True
        else:
            print(f"❌ 表单填写失败 - 主客户: {user.get('chnname')}")
            return False

    except Exception as e:
        print(f"❌ 填写表单异常 - 主客户: {user.get('chnname')}, 错误: {e}")
        return False


def fill_six_steps_form(form_data, token, webrefno, client_user):
    """填写6步表单（支持主客户和子客户）"""
    try:
        client_name = client_user.get('chnname', 'Unknown')
        client_type = "主客户" if not client_user.get('formId') else "子客户"

        # Step 1: 资格标准
        step1 = form_data['step1']
        step1['isDraft'] = False
        step1['webRefNo'] = webrefno
        step1['applicationAlphId'] = None
        step1['applicantGroupAlphId'] = None
        step1['stage'] = "passportInformation"

        print(f"📝 Step1 - {client_type}: {client_name}")
        step1_res = attempt_fill_form("https://eu-api-app.vfsevisa.com/v1/api/application/submit-eligibility", step1, token, 10, 1)
        if not step1_res:
            print(f"❌ Step1失败 - {client_type}: {client_name}")
            return False

        applicationAlphId = step1_res['data']['applicationAlphId']
        applicantGroupAlphId = step1_res['data']['applicantGroupAlphId']
        print(f"✅ Step1成功 - {client_type}: {client_name}")

        # Step 2: 护照信息
        step2 = form_data['step2']
        step2['isDraft'] = False
        step2['applicationAlphId'] = applicationAlphId

        print(f"📝 Step2 - {client_type}: {client_name}")
        step2_res = attempt_fill_form("https://eu-api-app.vfsevisa.com/v1/api/passport", step2, token, 10, 1)
        if not step2_res:
            print(f"❌ Step2失败 - {client_type}: {client_name}")
            return False
        print(f"✅ Step2成功 - {client_type}: {client_name}")

        # Step 3: 申请人信息
        step3 = form_data['step3']
        step3['isDraft'] = False
        step3['applicationAlphId'] = applicationAlphId
        step3['applicantGroupAlphId'] = applicantGroupAlphId

        print(f"📝 Step3 - {client_type}: {client_name}")
        step3_res = attempt_fill_form("https://eu-api-app.vfsevisa.com/v1/api/applicant-details/submit-applicant-details", step3, token, 10, 1)
        if not step3_res:
            print(f"❌ Step3失败 - {client_type}: {client_name}")
            return False
        print(f"✅ Step3成功 - {client_type}: {client_name}")

        # Step 4: 旅行信息
        step4 = form_data['step4']
        step4['isDraft'] = False
        step4['applicationAlphId'] = applicationAlphId

        print(f"📝 Step4 - {client_type}: {client_name}")
        step4_res = attempt_fill_form("https://eu-api-app.vfsevisa.com/v1/api/travel", step4, token, 10, 1)
        if not step4_res:
            print(f"❌ Step4失败 - {client_type}: {client_name}")
            return False
        print(f"✅ Step4成功 - {client_type}: {client_name}")

        # Step 5: 住宿信息
        step5 = form_data['step5']
        step5['isDraft'] = False
        step5['applicationAlphId'] = applicationAlphId
        step5['applicantGroupAlphId'] = applicantGroupAlphId

        print(f"📝 Step5 - {client_type}: {client_name}")
        step5_res = attempt_fill_form("https://eu-api-app.vfsevisa.com/v1/api/accommodation/submit-accommodation", step5, token, 10, 1)
        if not step5_res:
            print(f"❌ Step5失败 - {client_type}: {client_name}")
            return False
        print(f"✅ Step5成功 - {client_type}: {client_name}")

        # Step 6: VAF信息
        step6 = form_data['step6']
        step6['isDraft'] = False
        step6['applicationAlphId'] = applicationAlphId

        print(f"📝 Step6 - {client_type}: {client_name}")
        step6_res = attempt_fill_form("https://eu-api-app.vfsevisa.com/v1/api/additional-details/submit-additional-details", step6, token, 10, 1)
        if not step6_res:
            print(f"❌ Step6失败 - {client_type}: {client_name}")
            return False

        if step6_res.get('responseStatus', {}).get('code') == 200:
            print(f"🎉 所有步骤完成 - {client_type}: {client_name}")
            return True
        else:
            print(f"❌ Step6响应异常 - {client_type}: {client_name}")
            return False

    except Exception as e:
        client_name = client_user.get('chnname', 'Unknown')
        client_type = "主客户" if not client_user.get('formId') else "子客户"
        print(f"❌ 填写6步表单异常 - {client_type}: {client_name}, 错误: {e}")
        return False


def get_urn(user):
    """🎯 改进版：支持多个子客户的表单填写"""
    if len(loginUsers) == 0:
        tm.sleep(1)
        return
    account = random.choice(loginUsers)

    user_id = f"{user.get('missionCode')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}"

    try:
        url = f"https://lift-apicn.vfsglobal.com/appointment/application"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "route": "chn/zh/che",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": account['ltsn']
        }

        data = {
            "countryCode": "chn",
            "missionCode": user["missionCode"],
            "loginUser": account.get("email"),
            "lOGinusEr": user["loginUser"],
            "languageCode": "zh-CN",
        }

        print(f"🔄 正在刷新主客户 {user['chnname']}，护照: {user['passportNO']}")
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
        )

        if response.status_code == 200:
            response_data = response.json()
            print(f"📋 API响应: {response_data}")

            if response_data["error"] is None:
                applications = response_data.get('data', [])
                if len(applications) > 0:
                    # 处理每个申请
                    for application in applications:
                        if user.get('urn') == application.get('urn'):
                            applicants = application.get('applicants', [])
                            print(f"📝 找到 {len(applicants)} 个申请人需要处理")

                            # 🎯 关键改进：支持多个子客户和单客户处理
                            child_users = user.get('child', [])

                            if not child_users:
                                # 没有子客户，处理客户本身
                                print(f"👤 主客户 {user['chnname']} 没有子客户，处理客户本身")

                                for applicant in applicants:
                                    if applicant.get('vafStatus') != 'Completed':
                                        passport_number = applicant.get('passportNumber')
                                        print(f"🔍 正在为主客户护照号 {passport_number} 填写表单")

                                        # 为主客户本身填写表单
                                        success = fill_form_for_main_user(user, applicant)
                                        if success:
                                            print(f"🎉 主客户 {user.get('chnname')} 表单填写成功")
                                        else:
                                            print(f"❌ 主客户 {user.get('chnname')} 表单填写失败")
                            else:
                                # 有子客户，处理子客户
                                print(f"👥 主客户 {user['chnname']} 有 {len(child_users)} 个子客户")

                                # 为每个申请人匹配对应的子客户并填写表单
                                for applicant in applicants:
                                    if applicant.get('vafStatus') != 'Completed':
                                        passport_number = applicant.get('passportNumber')
                                        print(f"🔍 正在为护照号 {passport_number} 寻找匹配的子客户")

                                        # 根据护照号匹配子客户
                                        matching_child = find_matching_child(child_users, passport_number)
                                        if matching_child:
                                            print(f"✅ 找到匹配的子客户: {matching_child.get('chnname')}")

                                            # 为匹配的子客户填写表单
                                            success = fill_form_for_child(user, applicant, matching_child)
                                            if success:
                                                # 更新子客户的填表时间
                                                matching_child['fill_form_time'] = int(tm.time())
                                                print(f"🎉 子客户 {matching_child.get('chnname')} 表单填写成功")
                                            else:
                                                print(f"❌ 子客户 {matching_child.get('chnname')} 表单填写失败")
                                        else:
                                            print(f"⚠️ 未找到护照号 {passport_number} 对应的子客户")

                            # 更新主客户信息
                            user['child'] = child_users  # 更新子客户信息
                            user['fill_form_time'] = int(tm.time())
                            redis_client.hset(
                                "cheUserDatas",
                                f"{user['centerCode']}-{user['passportNO']}",
                                json.dumps({**user}),
                            )
                            print(f"💾 主客户 {user['chnname']} 信息已更新到Redis")

                            # 🎉 发送完成通知
                            stats = get_child_processing_stats(child_users)
                            if stats['completion_rate'] == 100:
                                print(f"🎉 主客户 {user['chnname']} 的所有子客户表单填写完成！")
                                send_completion_notification(user, stats['completed'], stats['total'])
                            else:
                                print(f"📊 主客户 {user['chnname']} 进度: {stats['completed']}/{stats['total']} ({stats['completion_rate']:.1f}%)")

                else:
                    # 没有申请数据，标记为已处理
                    user['fill_form_time'] = int(tm.time())
                    redis_client.hset(
                        "cheUserDatas",
                        f"{user['centerCode']}-{user['passportNO']}",
                        json.dumps({**user}),
                    )
                    print(f"📝 主客户 {user['chnname']} 没有申请数据，已标记为处理完成")

            elif "No Applicant exists" in response_data['error']['description']:
                # 没有申请人存在
                user['loginUser'] = None
                user['urn'] = None
                user['not_our_email'] = False
                user['fill_form_time'] = int(tm.time())
                redis_client.hset(
                    "cheUserDatas",
                    f"{user['centerCode']}-{user['passportNO']}",
                    json.dumps({**user}),
                )
                print(f"⚠️ 主客户 {user['chnname']} 不存在申请人")

        elif response.status_code == 401:
            print(f"❌ 认证失败，状态码: {response.status_code}")
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")

    except Exception as e:
        print(f"❌ 处理主客户 {user.get('chnname')} 时发生异常: {e}")
    finally:
        # 确保从处理集合中移除
        if user_id in processing_users:
            processing_users.remove(user_id)


def find_matching_child(child_users, passport_number):
    """根据护照号查找匹配的子客户"""
    for child in child_users:
        if child.get('passportNO') == passport_number:
            return child
    return None


def get_child_processing_stats(child_users):
    """获取子客户处理统计信息"""
    total = len(child_users)
    completed = sum(1 for child in child_users if child.get('fill_form_time'))
    pending = total - completed

    return {
        'total': total,
        'completed': completed,
        'pending': pending,
        'completion_rate': (completed / total * 100) if total > 0 else 0
    }


def log_child_processing_status(user):
    """记录客户处理状态（主客户或子客户）"""
    child_users = user.get('child', [])
    if not child_users:
        # 没有子客户，显示主客户状态
        main_status = "已完成" if user.get('fill_form_time') else "待处理"
        print(f"📊 主客户 {user.get('chnname')} 状态: {main_status}")
        return

    # 有子客户，显示子客户统计
    stats = get_child_processing_stats(child_users)
    print(f"📊 用户 {user.get('chnname')} 子客户状态: "
          f"总数={stats['total']}, 已完成={stats['completed']}, "
          f"待处理={stats['pending']}, 完成率={stats['completion_rate']:.1f}%")

    # 详细列出待处理的子客户
    pending_children = [
        child for child in child_users
        if not child.get('fill_form_time') and child.get('formId')
    ]

    if pending_children:
        print(f"📝 待处理子客户:")
        for child in pending_children:
            print(f"   - {child.get('chnname', 'Unknown')} (护照: {child.get('passportNO', 'Unknown')})")


def update_child_status(user, child_passport, status='completed'):
    """更新子客户状态"""
    child_users = user.get('child', [])
    for child in child_users:
        if child.get('passportNO') == child_passport:
            if status == 'completed':
                child['fill_form_time'] = int(tm.time())
            elif status == 'failed':
                child['fill_form_error'] = int(tm.time())
            break

    # 更新到Redis
    redis_client.hset(
        "cheUserDatas",
        f"{user['centerCode']}-{user['passportNO']}",
        json.dumps({**user}),
    )


def send_completion_notification(user, completed_children, total_children):
    """发送完成通知到企业微信"""
    try:
        webhook_url = pick_random_url()

        completion_rate = (completed_children / total_children * 100) if total_children > 0 else 0

        message = {
            "msgtype": "text",
            "text": {
                "content": f"🎉 VFS表单填写完成通知\n\n"
                f"主客户: {user.get('chnname', 'Unknown')}\n"
                f"护照号: {user.get('passportNO', 'Unknown')}\n"
                f"中心代码: {user.get('centerCode', 'Unknown')}\n"
                f"子客户统计: {completed_children}/{total_children} 已完成\n"
                f"完成率: {completion_rate:.1f}%\n"
                f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            }
        }

        response = requests.post(webhook_url, json=message, timeout=10)
        if response.status_code == 200:
            print(f"✅ 企业微信通知发送成功")
        else:
            print(f"❌ 企业微信通知发送失败: {response.status_code}")

    except Exception as e:
        print(f"❌ 发送企业微信通知异常: {e}")


def is_date_between(date, start_date, end_date):
    # Convert "dd/mm/yyyy" format date strings to "mm/dd/yyyy"
    start_date = convert_date_format(start_date)
    end_date = convert_date_format(end_date)

    # Convert date strings to datetime objects
    date_obj = datetime.strptime(date, "%d/%m/%Y")
    start_date_obj = datetime.strptime(start_date, "%m/%d/%Y")
    end_date_obj = datetime.strptime(end_date, "%m/%d/%Y")

    # Check if date is within the range
    return start_date_obj <= date_obj <= end_date_obj


def convert_date_format(date):
    parts = date.split("/")
    return f"{parts[1]}/{parts[0]}/{parts[2]}"


# 创建一个线程安全的队列来保存需要处理的用户
user_queue = Queue()
# 定义一个函数用于并发处理用户请求


def process_users():
    while True:
        try:
            user = user_queue.get(timeout=1)  # 设置超时时间为1秒
            get_urn(user)
            user_queue.task_done()
        except queue.Empty:
            tm.sleep(0.1)  # 如果队列为空，则休眠0.1秒


def getNeedProceed(user_list):
    """🎯 改进版：筛选需要处理的用户（支持多子客户和单客户）"""

    def filter_condition(user):
        if user.get('not_our_email') == True:
            return False

        # 基本条件：有URN且未填表
        basic_condition = (
            not user.get("fill_form_time") and
            user.get('urn') is not None
        )

        if not basic_condition:
            return False

        # 检查是否有子客户
        child_users = user.get('child', [])

        if not child_users:
            # 没有子客户，处理客户本身
            print(f"👤 用户 {user.get('chnname')} 没有子客户，将处理客户本身")
            return True

        # 有子客户，检查是否有子客户还未填表
        has_unfilled_child = any(
            not child.get('fill_form_time') and child.get('formId')
            for child in child_users
        )

        if has_unfilled_child:
            unfilled_count = sum(
                1 for child in child_users
                if not child.get('fill_form_time') and child.get('formId')
            )
            print(f"📋 用户 {user.get('chnname')} 有 {unfilled_count} 个子客户需要填表")
            return True
        else:
            print(f"✅ 用户 {user.get('chnname')} 的所有子客户都已填表完成")
            return False

    need_proceed = list(filter(filter_condition, user_list))
    return need_proceed


def refresh_proxy():
    while True:
        tm.sleep(60)
        global delegate
        delegate = json.loads(redis_client.get("fast_proxy"))


threading.Thread(target=refresh_proxy).start()


# 首先，创建并启动子线程
threads = []
for _ in range(5):
    thread = threading.Thread(target=process_users)
    thread.daemon = True  # 将线程设置为守护线程，确保主程序退出时子线程也会停止
    thread.start()
    threads.append(thread)

while True:
    try:
        print(f"\n{'='*60}")
        print(f"🔄 开始新一轮处理 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")

        cheUserDatas = redis_client.hgetall("cheUserDatas")
        print(f"📊 Redis中总用户数: {len(cheUserDatas)}")

        need_proceed = getNeedProceed(cheUserDatas)

        if len(need_proceed) != 0:
            print(f"🎯 发现 {len(need_proceed)} 个用户需要处理表单填写")

            # 统计客户信息（主客户和子客户）
            main_users_count = 0
            total_children = 0
            pending_children = 0

            for user in need_proceed:
                child_users = user.get('child', [])

                if not child_users:
                    # 没有子客户，统计主客户
                    main_users_count += 1
                    print(f"👤 主客户: {user.get('chnname')} (无子客户)")
                else:
                    # 有子客户，统计子客户
                    total_children += len(child_users)
                    pending_children += sum(
                        1 for child in child_users
                        if not child.get('fill_form_time') and child.get('formId')
                    )

                    # 记录每个用户的子客户状态
                    log_child_processing_status(user)

            if main_users_count > 0:
                print(f"📈 主客户统计: {main_users_count} 个主客户需要处理")
            if total_children > 0:
                print(f"📈 子客户统计: {total_children} 个子客户, {pending_children} 个待处理")

            # 将需要处理的用户加入队列
            added_count = 0
            for user in need_proceed:
                user_id = f"{user.get('missionCode')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}"
                if user_id not in processing_users:
                    user_queue.put(user)
                    processing_users.add(user_id)
                    added_count += 1
                    print(f"➕ 添加到处理队列: {user.get('chnname')} (护照: {user.get('passportNO')})")

            print(f"✅ 成功添加 {added_count} 个用户到处理队列")
            print(f"🔄 当前处理队列大小: {user_queue.qsize()}")
            print(f"🔒 当前处理中用户数: {len(processing_users)}")

        else:
            print('⏰ 当前没有需要处理的用户')
            # 清理处理用户集合，避免内存泄漏
            if len(processing_users) > 0:
                print(f"🧹 清理处理用户集合，当前大小: {len(processing_users)}")
                processing_users.clear()

        print(f"💤 等待30秒后进行下一轮检查...")
        tm.sleep(30)

    except Exception as e:
        print(f"❌ 主循环发生异常: {e}")
        tm.sleep(10)  # 异常时短暂等待
