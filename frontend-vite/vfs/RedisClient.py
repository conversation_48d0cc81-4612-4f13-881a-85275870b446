# -*- coding: utf-8 -*-
import redis
import json
import threading


class RedisClient:
    def __init__(self, host='*************', port=6379, password='TicketsCache#2023', db=0):
        self.client = redis.Redis(
            host=host,
            port=port,
            password=password,
            db=db,
            decode_responses=True,
            retry_on_timeout=True,
        )

    def set(self, key, value, expire_in_seconds=None):
        if expire_in_seconds:
            return self.client.setex(key, expire_in_seconds, value)
        return self.client.set(key, value)

    def get(self, key):
        return self.client.get(key)

    def delete(self, key):
        return self.client.delete(key)

    def keys(self, pattern):
        return self.client.keys(pattern)

    def setex(self, key, seconds, value):
        return self.client.setex(key, seconds, value)

    def hset(self, hash_name, field, value):
        return self.client.hset(hash_name, field, value)

    def hget(self, hash_name, field):
        return self.client.hget(hash_name, field)

    def hgetall(self, hash_name):
        data = self.client.hgetall(hash_name)
        json_array = []
        for field, value in data.items():
            try:
                json_object = json.loads(value)
                json_array.append(json_object)
            except json.JSONDecodeError:
                json_array.append(value)
        return json_array

    def hgetallkey(self, hash_name):
        data = self.client.hgetall(hash_name)
        json_array = []
        for field, value in data.items():
            json_array.append(field)
        return json_array

    def hdel(self, hash_name, *fields):
        return self.client.hdel(hash_name, *fields)

    def expire(self, key, seconds):
        return self.client.expire(key, seconds)

    def quit(self):
        self.client.close()

    def __init_subscriber(self):
        self.subscriber = redis.Redis(
            host='*************',
            port=6379,
            db=0,
            password='TicketsCache#2023',
            decode_responses=True,
            retry_on_timeout=True,
        )
        self._pubsub = self.subscriber.pubsub()

    def subscribe(self, channel, callback):
        if not hasattr(self, 'subscriber'):
            self.__init_subscriber()

        def listen():
            self._pubsub.subscribe(channel)
            for message in self._pubsub.listen():
                if message['type'] == 'message':
                    callback(message['channel'], message['data'])

        thread = threading.Thread(target=listen)
        thread.daemon = True
        thread.start()

    def unsubscribe(self, channel):
        if hasattr(self, 'subscriber'):
            self._pubsub.unsubscribe(channel)

    def __init_publisher(self):
        self.publisher = redis.Redis(
            host='*************',
            port=6379,
            db=0,
            password='TicketsCache#2023',
            decode_responses=True,
            retry_on_timeout=True,
        )

    def publish(self, channel, message):
        if not hasattr(self, 'publisher'):
            self.__init_publisher()
        self.publisher.publish(channel, message)
