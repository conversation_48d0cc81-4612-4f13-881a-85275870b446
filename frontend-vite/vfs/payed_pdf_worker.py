# -*- coding: utf-8 -*-
"""
VFS 已支付订单处理器
- 仅处理前端将订单状态置为 'payed' 的订单
- 依次执行：trackpayMentstatus -> confirmAppointment -> downLoadPdf
- 本地保存PDF，写入 appointment_pdfs 表，并通知负责人（企业微信Hook）

运行方式：独立脚本/后台任务。请确保数据库表 appointment_pdfs 已存在，结构：
  id (PK), order_id (uniq), pdf_url, pdf_filename, file_size, download_count, created_at, updated_at
pdf_url 按需置空，前端使用 pdf_filename 展示。
"""
from curl_cffi import requests
import os
import json
import time
import random
import base64
import logging
from datetime import datetime
from typing import Optional, Tuple, Dict

from db_utils import get_db_connection
from RedisClient import RedisClient
from common_remote_accounts import get_deu_account_from_remote, get_ita_account_from_remote
from vfs_mappings import get_mission_name, get_center_name, get_visa_name


# 日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("payed_pdf_worker")

# 常量
PDF_SAVE_DIR = os.path.join(os.path.dirname(__file__), "appointment_pdfs")
TRACK_URL_TMPL = "https://lift-apicn.vfsglobal.com/payments/trackpayMentstatus?RequestRefNo={req}"
CONFIRM_URL = "https://lift-apicn.vfsglobal.com/payMents/confirmAppointment"
DOWNLOAD_URL = "https://lift-apicn.vfsglobal.com/appointment/downLoadPdf"

OPENMER_FRONT_REDIRECT_TMPL = "https://openmer.allinpaysz.com/prod-api/merchant-open/api/CbOrgTranPayController_frontRedirect/M757220240725174739485/{txn}?"

# 初始化 Redis
redis_client = RedisClient()

# 动态国家/中心映射缓存（10分钟）
_mapping_cache: Dict[str, Dict[str, str]] = {"mission": {}, "center": {}}
_mapping_cache_time = 0
_MAPPING_TTL = 600


def _extract_leaf_nodes(node: dict) -> dict:
    children = node.get("data") or node.get("sub")
    cleaned = {k: node[k] for k in ("name", "missionCode", "centerCode", "code", "missionCodeName", "isoCode", "centerName") if k in node}
    if isinstance(children, list) and children:
        cleaned["children"] = [_extract_leaf_nodes(child) for child in children]
    return cleaned


def _traverse(node: dict) -> list:
    nodes = [node]
    for ch in node.get("children", []) or []:
        nodes.extend(_traverse(ch))
    return nodes


os.makedirs(PDF_SAVE_DIR, exist_ok=True)

# 工具函数


def get_token_for_account(mission_code: str, vfs_account: str) -> Optional[dict]:
    """从Redis读取vfs_token:{vfs_account}:{mission_code}，返回token数据字典。"""
    try:
        key = f"vfs_token:{vfs_account}:{mission_code}"
        cached = redis_client.get(key)
        if not cached:
            return None
        data = json.loads(cached)
        # 检查剩余时间（>300秒）
        remaining = 6000 - (int(time.time()) - data.get('updateTokenTime', 0))
        if remaining > 300:
            return data
        return None
    except Exception as e:
        logger.warning(f"读取Token失败 | 键: {key} | 错误: {e}")
        return None


def get_cached_urn(order_id: str) -> Optional[str]:
    """从数据库读取订单URN（优先DB，不再依赖Redis）。"""
    conn = get_db_connection()
    if not conn:
        return None
    try:
        with conn.cursor() as cur:
            cur.execute("SELECT urn FROM orders WHERE order_id=%s", (order_id,))
            row = cur.fetchone()
            if row and row[0]:
                return row[0]
            return None
    except Exception as e:
        logger.warning(f"从数据库读取URN失败 | 订单: {order_id} | 错误: {e}")
        return None
    finally:
        conn.close()


def fetch_payed_orders(limit: int = 20) -> list:
    """从DB获取状态为 'payed' 的VFS订单。"""
    conn = get_db_connection()
    if not conn:
        return []
    try:
        with conn.cursor() as cur:
            cur.execute(
                """
                SELECT order_id, mission_code, center_code, visa_code, vfs_account,
                       appointment_date, appointment_time, requestrefno, transaction_id
                FROM orders
                WHERE is_vfs_order = true
                  AND order_status in ('payed','payment_failed', 'wait_download_pdf')
                  AND updated_at >= NOW() - INTERVAL '2 hours'
                ORDER BY updated_at DESC
                LIMIT %s
                """,
                (limit,)
            )
            rows = cur.fetchall()
            return rows
    except Exception as e:
        logger.error(f"查询payed订单失败: {e}")
        return []
    finally:
        conn.close()


def get_primary_client(order_id: str) -> Tuple[Optional[str], Optional[str]]:
    """获取订单的主申请人姓名与护照号(name, passport)。"""
    conn = get_db_connection()
    if not conn:
        return None, None
    try:
        with conn.cursor() as cur:
            cur.execute(
                """
                SELECT name, passport
                FROM clients
                WHERE order_id = %s
                ORDER BY id
                LIMIT 1
                """,
                (order_id,)
            )
            r = cur.fetchone()
            return (r[0], r[1]) if r else (None, None)
    except Exception as e:
        logger.warning(f"获取客户信息失败 | 订单: {order_id} | 错误: {e}")
        return None, None
    finally:
        conn.close()


def get_operator_hooks(order_id: str) -> list:
    """查订单负责人(operator)的企微Hook列表。"""
    conn = get_db_connection()
    if not conn:
        return []
    try:
        with conn.cursor() as cur:
            cur.execute("SELECT operator FROM orders WHERE order_id = %s", (order_id,))
            row = cur.fetchone()
            if not row or not row[0]:
                return []
            operator_id = row[0]
            cur.execute(
                """
                SELECT hook_url FROM user_hooks
                WHERE user_id = %s AND hook_url IS NOT NULL AND hook_url != ''
                """,
                (operator_id,)
            )
            hooks = [r[0] for r in cur.fetchall() if r and r[0]]
            return hooks
    except Exception as e:
        logger.warning(f"查询Hook失败 | 订单: {order_id} | 错误: {e}")
        return []
    finally:
        conn.close()


def send_wecom_text(hook_url: str, content: str) -> None:
    try:
        payload = {"msgtype": "text", "text": {"content": content}}
        resp = requests.post(hook_url, json=payload, timeout=10)
        logger.info(f"企微消息发送: {resp.status_code}")
    except Exception as e:
        logger.warning(f"企微消息发送失败: {e}")


# DB写入

def upsert_pdf_record(order_id: str, filename: str, file_size: int) -> bool:
    conn = get_db_connection()
    if not conn:
        return False
    try:
        with conn.cursor() as cur:
            cur.execute(
                """
                INSERT INTO appointment_pdfs (order_id, pdf_url, pdf_filename, file_size, download_count, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                ON CONFLICT (order_id) DO UPDATE SET
                    pdf_url = EXCLUDED.pdf_url,
                    pdf_filename = EXCLUDED.pdf_filename,
                    file_size = EXCLUDED.file_size,
                    updated_at = NOW()
                """,
                (order_id, None, filename, file_size, 0),
            )
        conn.commit()
        return True
    except Exception as e:
        logger.error(f"写入appointment_pdfs失败 | 订单: {order_id} | 错误: {e}")
        try:
            conn.rollback()
        except Exception:
            pass
        return False
    finally:
        conn.close()


def update_order_status(order_id: str, new_status: str) -> bool:
    conn = get_db_connection()
    if not conn:
        return False
    try:
        with conn.cursor() as cur:
            cur.execute("UPDATE orders SET order_status=%s, updated_at=NOW() WHERE order_id=%s", (new_status, order_id))
        conn.commit()
        return True
    except Exception as e:
        logger.warning(f"更新订单状态失败 | 订单: {order_id} | 错误: {e}")
        return False
    finally:
        conn.close()


# 外部HTTP

def build_headers(token: str, route: Optional[str] = None) -> dict:
    h = {
        "Content-Type": "application/json;charset=UTF-8",
        "authorize": token,
        "origin": "https://visa.vfsglobal.com",
        "referer": "https://visa.vfsglobal.com/",
        "accept-language": "zh-CN,zh;q=0.9",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "macOS",
        "origin": "https://visa.vfsglobal.com",
        "sec-fetch-site": "same-site",
        "sec-fetch-mode": "cors",
        "sec-fetch-dest": "empty",
        "referer": "https://visa.vfsglobal.com/",
        "accept-encoding": "gzip, deflate, br",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    }
    if route:
        h["route"] = route
    return h


def track_payment(request_ref_no: str, token: str) -> Optional[dict]:
    url = TRACK_URL_TMPL.format(req=request_ref_no)
    try:
        resp = requests.get(url, headers=build_headers(token), impersonate='chrome136', timeout=30, verify=False)
        logger.info(f"trackpayMentstatus: {resp.text}")
        if resp.status_code == 200:
            return resp.json()
    except Exception as e:
        logger.warning(f"trackpayMentstatus异常: {e}")


def openmer_front_redirect(transaction_id: str) -> bool:
    """Call OpenMer frontRedirect before track_payment. Return True only when HTTP 200."""
    url = OPENMER_FRONT_REDIRECT_TMPL.format(txn=transaction_id)
    try:
        resp = requests.get(url, impersonate='chrome136', timeout=30, verify=False)
        logger.info(f"openmer frontRedirect: {resp.status_code}")
        return resp.status_code == 200
    except Exception as e:
        logger.warning(f"openmer frontRedirect异常: {e}")
        return False


def confirm_appointment(mission_code: str, center_code: str, urn: Optional[str], request_ref_no: str, transaction_id: str, token_data: dict, vfs_account: Optional[str] = None) -> Optional[dict]:
    try:
        payload = {
            "missionCode": mission_code,
            "countryCode": "chn",
            "loginUser": token_data.get("email"),
            "urn": urn,
            "bankReferenceNo": transaction_id,
            "requestReferenceNo": str(request_ref_no),
            "currency": "CNY",
            "centerCode": center_code,
            "isCBankPayment": False,
            "amount": 161,
            "cultureCode": "zh-CN",
        }

        # 德国远程账号：追加 LogINUseR 为订单的 vfs_account
        if mission_code and mission_code in ['deu', 'ita'] and vfs_account:
            payload["LogINUseR"] = vfs_account
        logger.info(f"confirmAppointment: {json.dumps(payload, ensure_ascii=False)[:500]}")
        headers = build_headers(token_data.get("token", ""))
        # ITA 场景下从 token_data 读取代理
        proxies = None
        try:
            if mission_code and str(mission_code).lower() == 'ita':
                proxy = (token_data.get('login_proxy') or '').strip()
                if proxy:
                    proxies = {"http": proxy, "https": proxy}
        except Exception:
            proxies = None
        resp = requests.post(CONFIRM_URL, json=payload, headers=headers, impersonate='chrome136', timeout=30, verify=False, proxies=proxies)
        logger.info(f"confirmAppointment: {resp.status_code} | {resp.text}")
        if resp.status_code == 200:
            try:
                return resp.json()
            except Exception:
                return None
    except Exception as e:
        logger.warning(f"confirmAppointment异常: {e}")
    return None


def download_pdf(mission_code: str,  urn: str, token_data: dict, vfs_account: Optional[str] = None) -> Optional[bytes]:
    try:
        payload = {
            "cultureCode": "",
            "missionCode": mission_code,
            "countryCode": "chn",
            "loginUser": token_data.get("email"),
            "urn": urn,
        }
        # 德国远程账号：追加 LogINUseR 为订单的 vfs_account
        if token_data.get("missionCode") and str(token_data.get("missionCode")).lower() in ['deu', 'ita'] and vfs_account:
            payload["LogINUseR"] = vfs_account
        route = f"chn/zh/{mission_code}"
        headers = build_headers(token_data.get("token", ""), route=route)
        # ITA 场景下从 token_data 读取代理
        proxies = None
        try:
            if mission_code and str(mission_code).lower() == 'ita':
                proxy = (token_data.get('login_proxy') or '').strip()
                if proxy:
                    proxies = {"http": proxy, "https": proxy}
        except Exception:
            proxies = None
        resp = requests.post(DOWNLOAD_URL, json=payload, headers=headers, impersonate='chrome136', timeout=60, verify=False, proxies=proxies)
        logger.info(f"downLoadPdf: {resp.status_code} | len={len(resp.text or '')}")
        if resp.status_code == 200 and resp.text and len(resp.text) > 1000:
            try:
                return base64.b64decode(resp.text)
            except Exception:
                return None
    except Exception as e:
        logger.warning(f"downLoadPdf异常: {e}")
    return None


def safe_filename(name: str) -> str:
    return "".join(c for c in name if c not in "\\/:*?\"<>|\n\r\t").strip()


def build_pdf_filename(mission_code: str, center_code: str, client_name: str, passport: str, appt_date: str, appt_time: str, order_id: str) -> str:
    country = get_mission_name(mission_code)
    center = get_center_name(center_code, mission_code)
    parts = [country, center, client_name or "", passport or "", appt_date or "", appt_time or "", order_id]
    return safe_filename("-".join(parts) + ".pdf")


def process_one(order_row: tuple) -> bool:
    order_id, mission_code, center_code, visa_code, vfs_account, appt_date, appt_time, request_ref_no, transaction_id = order_row
    print(order_row)
    logger.info(f"处理订单: {order_id} | {mission_code}-{center_code} | visa={visa_code}")

    # 取 Token 与 URN
    if mission_code and str(mission_code).lower() == 'deu':
        token_data = get_deu_account_from_remote()
        if not token_data:
            logger.warning(f"无可用远程Token | 订单: {order_id} | 国家: {mission_code}")
            return False
    elif mission_code and str(mission_code).lower() == 'ita':
        token_data = get_ita_account_from_remote()
        if not token_data:
            logger.warning(f"无可用远程Token | 订单: {order_id} | 国家: {mission_code}")
            return False
    else:
        token_data = get_token_for_account(mission_code, vfs_account or "")
        if not token_data:
            logger.warning(f"无可用Token | 订单: {order_id} | 账户: {vfs_account}")
            return False
    # URN 从数据库读取（不再用Redis）
    urn = get_cached_urn(order_id)

    # 调用 openmer frontRedirect -> track -> confirm（仅在已支付状态下再确认一次）
    if request_ref_no:
        # 必须先用 transaction_id 调用 openmer frontRedirect，未返回200则跳过本次处理
        if not transaction_id:
            logger.info(f"openmer frontRedirect 缺少 transaction_id，跳过本次处理 | 订单: {order_id}")
            return False
        ok = False
        for attempt in range(1, 6):
            ok = openmer_front_redirect(transaction_id)
            logger.info(f"openmer frontRedirect 尝试 {attempt}/5 | 订单: {order_id} | 成功: {ok}")
            if ok:
                break
            if attempt < 5:
                time.sleep(5)
        if not ok:
            logger.info(f"openmer frontRedirect 未返回200，跳过本次处理 | 订单: {order_id}")
            return False
        track_payment(request_ref_no, token_data.get("token", ""))
    confirm_attempted = False
    if request_ref_no and transaction_id:
        confirm_attempted = True
        confirmed = False
        for attempt in range(1, 6):
            res = confirm_appointment(mission_code, center_code, urn, request_ref_no, transaction_id, token_data, vfs_account)
            ok = bool(res and (res.get("IsAppointmentBooked") is True or (res.get('error') and res['error'].get('description') == 'Payment is already confirmed')))
            logger.info(f"confirmAppointment 尝试 {attempt}/5 | 订单: {order_id} | 成功: {ok}")
            if ok:
                confirmed = True
                break
            if attempt < 5:
                time.sleep(2 ** attempt)
        if not confirmed:
            logger.info(f"confirmAppointment 未成功，尝试直接下载PDF | 订单: {order_id}")

    # 下载PDF（重试最多5次，指数退避）
    pdf_bytes = None
    if urn:
        for attempt in range(1, 6):
            pdf_bytes = download_pdf(mission_code, urn, token_data, vfs_account)
            ok = bool(pdf_bytes)
            logger.info(f"downLoadPdf 尝试 {attempt}/5 | 订单: {order_id} | 成功: {ok}")
            if ok:
                break
            if attempt < 5:
                time.sleep(2 ** attempt)
    if not pdf_bytes:
        # # 若支付确认未成功，且PDF也下载失败，则标记失败并通知负责人
        # if 'confirm_attempted' in locals() and confirm_attempted and (not 'confirmed' in locals() or not confirmed):
        #     update_order_status(order_id, 'payment_failed')
        #     hooks = get_operator_hooks(order_id)
        #     if hooks:
        #         msg = (
        #             f"订单 {order_id} 支付确认失败且预约信下载失败\n"
        #             f"国家:{mission_code} 中心:{center_code}\nRefNo:{request_ref_no}\nTxn:{transaction_id}"
        #         )
        #         try:
        #             send_wecom_text(random.choice(hooks), msg)
        #         except Exception:
        #             pass
        # logger.warning(f"下载PDF失败 | 订单: {order_id}")
        return False

    # 文件名 & 保存
    client_name, passport = get_primary_client(order_id)
    filename = build_pdf_filename(mission_code, center_code, client_name or "", passport or "",
                                  appt_date.strftime('%Y-%m-%d') if hasattr(appt_date, 'strftime') else (appt_date or ''),
                                  appt_time or '', order_id)
    abs_path = os.path.join(PDF_SAVE_DIR, filename)
    try:
        with open(abs_path, 'wb') as f:
            f.write(pdf_bytes)
        file_size = os.path.getsize(abs_path)
    except Exception as e:
        logger.error(f"保存PDF失败 | 订单: {order_id} | 错误: {e}")
        return False

    # 写库 & 状态 & 通知
    if not upsert_pdf_record(order_id, filename, file_size):
        return False
    update_order_status(order_id, 'appointment_downloaded')

    hooks = get_operator_hooks(order_id)
    if hooks:
        # 构造更清晰的通知内容
        mission_name = get_mission_name(mission_code)
        center_name = get_center_name(center_code, mission_code)
        visa_name = get_visa_name(mission_code, center_code, visa_code)
        client_name, passport = get_primary_client(order_id)
        content_lines = [
            f"订单 {order_id} 的预约信已下载，请前往已完成订单进行下载",
            f"国家：{mission_name}",
            f"中心：{center_name}",
            f"签证类型：{visa_name}",
        ]
        if client_name:
            content_lines.insert(1, f"客户：{client_name}")
        if passport:
            # 紧随客户后
            insert_idx = 2 if client_name else 1
            content_lines.insert(insert_idx, f"护照：{passport}")
        content_lines.extend([
            f"文件名：{filename}",
            f"大小：{round(file_size/1024, 1)} KB",
        ])
        text = "\n".join(content_lines)
        try:
            send_wecom_text(random.choice(hooks), text)
        except Exception:
            pass
    return True


def main_loop():
    logger.info("已支付订单处理器启动")
    while True:
        rows = fetch_payed_orders(limit=20)
        if not rows:
            time.sleep(10)
            continue
        for row in rows:
            try:
                ok = process_one(row)
                logger.info(f"处理完成 | 订单: {row[0]} | 结果: {ok}")
            except Exception as e:
                logger.error(f"处理订单异常 | 订单: {row[0]} | 错误: {e}")
        time.sleep(3)


if __name__ == "__main__":
    main_loop()
