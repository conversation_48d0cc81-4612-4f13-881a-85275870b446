# -*- coding: utf-8 -*-
"""
数据库工具模块 - 提供PostgreSQL数据库操作函数
"""
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# 数据库配置
DATABASE = {
    "host": "localhost",
    "port": 5432,
    "dbname": "user_db",
    "user": "qwyvisa",
    "password": "HAfhqh1fn0fbua8vb7v!aa"
}

# 配置日志
logger = logging.getLogger('DB_Utils')


def get_db_connection():
    """获取数据库连接"""
    try:
        conn = psycopg2.connect(**DATABASE)
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败 | 异常: {str(e)}")
        print(f"数据库连接失败: {str(e)}")
        return None


def update_order_by_email_activated(email, status="activated"):
    """根据邮箱更新订单账号为已激活状态"""
    try:
        conn = get_db_connection()
        if not conn:
            print("无法获取数据库连接")
            return False

        with conn.cursor() as cursor:
            query = """
                UPDATE orders 
                SET order_status = %s, updated_at = CURRENT_TIMESTAMP
                WHERE vfs_account = %s AND is_vfs_order = true
            """
            cursor.execute(query, (status, email))
            affected_rows = cursor.rowcount
            conn.commit()

        conn.close()

        if affected_rows > 0:
            print(f"订单账号激活状态更新成功 | 邮箱: {email} | 状态: {status} | 影响行数: {affected_rows}")
            return True
        else:
            print(f"未找到对应的订单 | 邮箱: {email}")
            return False

    except Exception as e:
        print(f"根据邮箱更新订单账号激活状态失败 | 邮箱: {email} | 异常: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False


def update_order_account(order_id, email, password, status="registered"):
    """更新订单的账号信息"""
    try:
        conn = get_db_connection()
        if not conn:
            print("无法获取数据库连接")
            return False

        with conn.cursor() as cursor:
            query = """
                UPDATE orders
                SET vfs_account = %s, vfs_password = %s, order_status = %s, updated_at = CURRENT_TIMESTAMP
                WHERE order_id = %s
            """
            cursor.execute(query, (email, password, status, order_id))
            conn.commit()

        conn.close()
        print(f"订单账号信息更新成功 | 订单: {order_id} | 账号: {email} | 状态: {status}")
        return True

    except Exception as e:
        print(f"更新订单账号信息失败 | 订单: {order_id} | 异常: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False


def get_activated_account_for_country(country):
    """获取指定国家的已激活账号"""
    try:
        conn = get_db_connection()
        if not conn:
            print("无法获取数据库连接")
            return None

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            query = """
                SELECT vfs_account, vfs_password
                FROM orders
                WHERE is_vfs_order = true
                AND mission_code = %s
                AND order_status = 'registe_success'
                AND vfs_account IS NOT NULL
                AND vfs_account != ''
                AND vfs_password IS NOT NULL
                AND vfs_password != ''
                ORDER BY updated_at DESC
                LIMIT 1
            """
            cursor.execute(query, (country,))
            result = cursor.fetchone()

        conn.close()

        if result:
            return {
                "email": result['vfs_account'],
                "password": result['vfs_password'],
                "missionCode": country
            }
        else:
            print(f"未找到国家 {country} 的已激活账号")
            return None

    except Exception as e:
        print(f"获取已激活账号失败 | 国家: {country} | 异常: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return None
