#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置VFS日历扫描器的测试数据
"""
import sys
import os
import json
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def create_test_orders_and_urns():
    """创建测试订单和URN数据"""
    print("🔧 创建测试订单和URN数据...")

    try:
        from db_utils import get_db_connection
        from RedisClientAWS import RedisClient

        # 数据库连接
        conn = get_db_connection()
        if not conn:
            print("❌ 无法连接数据库")
            return False

        # Redis连接
        redis_client = RedisClient()

        # 创建测试订单数据
        test_orders = [
            {
                'order_id': 'VFS_TEST_001',
                'mission_code': 'hun',
                'center_code': 'PEK',
                'visa_code': 'S1',
                'travel_date': (datetime.now() + timedelta(days=30)).date(),
                'is_vfs_order': True,
                'order_status': 'registe_success'
            },
            {
                'order_id': 'VFS_TEST_002',
                'mission_code': 'fra',
                'center_code': 'SHA',
                'visa_code': 'T1',
                'travel_date': (datetime.now() + timedelta(days=45)).date(),
                'is_vfs_order': True,
                'order_status': 'registe_success'
            },
            {
                'order_id': 'VFS_TEST_003',
                'mission_code': 'esp',
                'center_code': 'PEK',
                'visa_code': 'T2',
                'travel_date': (datetime.now() + timedelta(days=60)).date(),
                'is_vfs_order': True,
                'order_status': 'registe_success'
            }
        ]

        with conn.cursor() as cursor:
            # 检查orders表是否存在
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'orders'
                )
            """)

            table_exists = cursor.fetchone()[0]

            if not table_exists:
                # 创建简化的orders表
                cursor.execute("""
                    CREATE TABLE orders (
                        order_id VARCHAR(255) PRIMARY KEY,
                        mission_code VARCHAR(10),
                        center_code VARCHAR(10),
                        visa_code VARCHAR(10),
                        travel_date DATE,
                        is_vfs_order BOOLEAN DEFAULT FALSE,
                        order_status VARCHAR(50),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                print("✅ 创建 orders 表")

            # 清理旧的测试数据
            cursor.execute("DELETE FROM orders WHERE order_id LIKE 'VFS_TEST_%'")

            # 插入测试订单
            for order in test_orders:
                cursor.execute("""
                    INSERT INTO orders (order_id, mission_code, center_code, visa_code, travel_date, is_vfs_order, order_status)
                    VALUES (%(order_id)s, %(mission_code)s, %(center_code)s, %(visa_code)s, %(travel_date)s, %(is_vfs_order)s, %(order_status)s)
                """, order)

            conn.commit()

        conn.close()
        print(f"✅ 插入了 {len(test_orders)} 个测试订单")

        # 为每个订单创建URN数据（使用正确的Redis键格式和2000秒有效期）
        for order in test_orders:
            urn_data = {
                'urn': f"URN_{order['order_id']}_{datetime.now().strftime('%Y%m%d')}",
                'createTime': int(datetime.now().timestamp()),  # 使用createTime字段名
                'order_id': order['order_id'],
                'mission_code': order['mission_code']
            }

            urn_key = f"vfs_urn:{order['order_id']}"  # 使用正确的键格式
            # 设置2000秒的过期时间
            redis_client.setex(urn_key, 2000, json.dumps(urn_data, ensure_ascii=False))

            print(f"✅ 创建URN: {order['order_id']} -> {urn_data['urn']} (有效期: 2000秒)")

        return True

    except Exception as e:
        print(f"❌ 创建测试订单和URN失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False


def create_date_ranges():
    """创建日期范围数据"""
    print("\n🔧 创建日期范围数据...")

    try:
        from db_utils import get_db_connection

        conn = get_db_connection()
        if not conn:
            print("❌ 无法连接数据库")
            return False

        with conn.cursor() as cursor:
            # 创建date_ranges表（如果不存在）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS date_ranges (
                    id SERIAL PRIMARY KEY,
                    order_id VARCHAR(255) NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            print("✅ 表 'date_ranges' 已创建")

            # 清理旧的测试数据
            cursor.execute("DELETE FROM date_ranges WHERE order_id LIKE 'VFS_TEST_%'")

            # 创建日期范围数据
            today = datetime.now().date()
            date_ranges = [
                {
                    'order_id': 'VFS_TEST_001',
                    'start_date': today,
                    'end_date': today + timedelta(days=30)
                },
                {
                    'order_id': 'VFS_TEST_002',
                    'start_date': today + timedelta(days=7),
                    'end_date': today + timedelta(days=60)
                },
                {
                    'order_id': 'VFS_TEST_003',
                    'start_date': today + timedelta(days=14),
                    'end_date': today + timedelta(days=90)
                },
                # 通用日期范围
                {
                    'order_id': 'GENERAL_RANGE_001',
                    'start_date': today,
                    'end_date': today + timedelta(days=45)
                }
            ]

            for dr in date_ranges:
                cursor.execute("""
                    INSERT INTO date_ranges (order_id, start_date, end_date)
                    VALUES (%(order_id)s, %(start_date)s, %(end_date)s)
                """, dr)

            conn.commit()

        conn.close()
        print(f"✅ 插入了 {len(date_ranges)} 个日期范围")

        return True

    except Exception as e:
        print(f"❌ 创建日期范围失败: {e}")
        if 'conn' in locals() and conn:
            conn.close()
        return False


def create_login_users_and_tokens():
    """创建登录用户数据和Token缓存"""
    print("\n🔧 创建登录用户数据和Token缓存...")

    try:
        from RedisClient import RedisClient

        redis_client = RedisClient()

        # 为每个国家创建测试登录用户
        countries = ["hun", "fra", "esp"]

        for country in countries:
            # 为每个国家创建2个测试用户
            for i in range(1, 3):
                email = f"test{i}@{country}.vfs.com"

                # 1. 创建基本用户数据（存储在 {country}LoginUser）
                user_data = {
                    "email": email,
                    "createTime": int(datetime.now().timestamp()) - 3600  # 1小时前创建
                }

                # 设置到Redis
                redis_key = f"{country}LoginUser"
                redis_client.hset(redis_key, email, json.dumps(user_data, ensure_ascii=False))

                # 2. 创建Token缓存数据（使用正确的Token缓存格式）
                token_data = {
                    "email": email,
                    "token": f"test_token_{country}_{i}_{int(datetime.now().timestamp())}",
                    "ltsn": f"test_session_{country}_{i}",
                    "urn": f"URN_TOKEN_{country}_{i}_{datetime.now().strftime('%Y%m%d')}",
                    "updateTokenTime": int(datetime.now().timestamp()) - 1800  # 30分钟前更新
                }

                # 使用正确的Token缓存键格式
                token_cache_key = f"vfs_token:{email}:{country}"
                redis_client.set(token_cache_key, json.dumps(token_data, ensure_ascii=False))

                print(f"✅ 创建用户和Token: {email} | 国家: {country}")

            print(f"✅ {country}: 2 个用户和Token")

        return True

    except Exception as e:
        print(f"❌ 创建登录用户和Token失败: {e}")
        return False


def create_proxy_data():
    """创建代理数据"""
    print("\n🔧 创建代理数据...")

    try:
        from RedisClientAWS import RedisClient

        redis_client = RedisClient()

        # 创建代理列表（示例代理，实际使用时需要替换为真实代理）
        proxy_list = [
            "http://proxy1.example.com:8080",
            "http://proxy2.example.com:8080",
            "http://proxy3.example.com:8080"
        ]

        redis_client.set("login_proxy", json.dumps(proxy_list))
        print(f"✅ login_proxy: {len(proxy_list)} 个代理")

        return True

    except Exception as e:
        print(f"❌ 创建代理数据失败: {e}")
        return False


def verify_setup():
    """验证设置"""
    print("\n🔍 验证设置...")

    try:
        # 验证数据库
        from db_utils import get_db_connection
        conn = get_db_connection()
        if conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM orders WHERE is_vfs_order = true")
                order_count = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM date_ranges")
                range_count = cursor.fetchone()[0]

                print(f"✅ 数据库: {order_count} 个VFS订单, {range_count} 个日期范围")
            conn.close()

        # 验证Redis
        from RedisClientAWS import RedisClient
        redis_client = RedisClient()

        # 检查URN数据（使用正确的Redis键格式）
        urn_count = 0
        for i in range(1, 4):
            urn_key = f"vfs_urn:VFS_TEST_00{i}"
            if redis_client.get(urn_key):
                urn_count += 1

        print(f"✅ Redis URN: {urn_count} 个")

        # 检查登录用户和Token
        user_count = 0
        token_count = 0
        for country in ["hun", "fra", "esp"]:
            users = redis_client.hgetall(f"{country}LoginUser")
            if users:
                user_count += len(users)

                # 检查每个用户的Token
                for email in users.keys():
                    token_key = f"vfs_token:{email}:{country}"
                    if redis_client.get(token_key):
                        token_count += 1

        print(f"✅ 登录用户: {user_count} 个")
        print(f"✅ Token缓存: {token_count} 个")

        # 检查代理
        proxy_data = redis_client.get("login_proxy")
        if proxy_data:
            proxies = json.loads(proxy_data)
            print(f"✅ 代理: {len(proxies)} 个")

        return True

    except Exception as e:
        print(f"❌ 验证设置失败: {e}")
        return False


def main():
    """主函数"""
    print("VFS日历扫描器测试数据设置")
    print("=" * 50)

    success_count = 0
    total_steps = 5

    steps = [
        ("创建测试订单和URN", create_test_orders_and_urns),
        ("创建日期范围", create_date_ranges),
        ("创建登录用户和Token", create_login_users_and_tokens),
        ("创建代理数据", create_proxy_data),
        ("验证设置", verify_setup)
    ]

    for step_name, step_func in steps:
        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} - 完成")
            else:
                print(f"❌ {step_name} - 失败")
        except Exception as e:
            print(f"❌ {step_name} - 异常: {e}")

    print("\n" + "=" * 50)
    print(f"📊 设置结果: {success_count}/{total_steps} 步骤完成")

    if success_count == total_steps:
        print("🎉 所有测试数据设置完成！")
        print("\n🚀 现在可以运行VFS日历扫描器:")
        print("   python vfs_calendar_scanner.py")

        return True
    else:
        print("⚠️ 部分设置失败，请检查错误信息")
        return False


if __name__ == "__main__":
    main()
