<template>
  <div class="booked-orders-container">
    <!-- 固定头部区域 -->
    <div class="fixed-header">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1 class="page-title">
              <el-icon class="title-icon"><DocumentChecked /></el-icon>
              已完成订单
            </h1>
            <p class="page-subtitle">查看所有已完成的签证订单</p>
          </div>
          <div class="header-right">
            <div class="header-stats">
              <el-tag type="success" effect="light" size="large">
                <el-icon><CircleCheck /></el-icon>
                已完成: {{ totalCount }} 单
              </el-tag>
            </div>
            <el-button
              type="default"
              size="large"
              :loading="loading"
              @click="loadOrders"
            >
              <el-icon><RefreshLeft /></el-icon>
              刷新列表
            </el-button>
          </div>
        </div>
      </div>

      <!-- 筛选工具栏 -->
      <div class="filter-section">
        <el-card class="filter-card" shadow="never">
          <div class="filter-header">
            <div class="filter-title">
              <el-icon><Filter /></el-icon>
              筛选条件
              <el-button
                type="text"
                :icon="isFilterExpanded ? 'ArrowUp' : 'ArrowDown'"
                @click="toggleFilter"
                class="filter-toggle-btn"
              >
                {{ isFilterExpanded ? '收起' : '展开' }}
              </el-button>
            </div>
            <div class="filter-actions-top">
              <div class="view-controls">
                <el-radio-group v-model="viewMode" size="default">
                  <el-radio-button value="card">
                    <div class="view-button-content">
                      <el-icon><Grid /></el-icon>
                      <span class="control-text">卡片视图</span>
                    </div>
                  </el-radio-button>
                  <el-radio-button value="list">
                    <div class="view-button-content">
                      <el-icon><List /></el-icon>
                      <span class="control-text">列表视图</span>
                    </div>
                  </el-radio-button>
                </el-radio-group>
              </div>
              <div class="filter-stats">
                <el-tag type="info" effect="light" size="default">
                  <el-icon><DataAnalysis /></el-icon>
                  共 {{ totalCount }} 条，显示 {{ filteredCount }} 条
                </el-tag>
              </div>
            </div>
          </div>

          <div class="filter-content" v-show="isFilterExpanded">
            <div class="filter-row">
              <div class="filter-group">
                <label class="filter-label">关键词搜索</label>
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索订单号、姓名、护照号..."
                  clearable
                  class="filter-input"
                  prefix-icon="Search"
                  @input="handleSearch"
                />
              </div>

              <div class="filter-group">
                <label class="filter-label">签证国家</label>
                <el-select
                  v-model="selectedCountry"
                  placeholder="选择国家"
                  clearable
                  class="filter-select"
                  @change="handleCountryChange"
                >
                  <el-option
                    v-for="mission in vfsStore.config"
                    :key="mission.missionCode"
                    :label="mission.missionCodeName"
                    :value="mission.missionCode"
                  />
                </el-select>
              </div>

              <div class="filter-group">
                <label class="filter-label">预约时间</label>
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  class="filter-date"
                  @change="handleDateChange"
                />
              </div>

              <div class="filter-group">
                <label class="filter-label">操作员</label>
                <el-select
                  v-model="selectedOperators"
                  placeholder="选择操作员"
                  multiple
                  clearable
                  class="filter-select"
                  @change="handleOperatorChange"
                >
                  <el-option
                    v-for="operator in operatorList"
                    :key="operator.id"
                    :label="operator.username"
                    :value="operator.username"
                  />
                </el-select>
              </div>
            </div>

            <div class="filter-row">
              <div class="filter-group">
                <label class="filter-label">订单状态</label>
                <el-select
                  v-model="selectedStatuses"
                  placeholder="选择状态"
                  multiple
                  clearable
                  class="filter-select"
                  @change="handleStatusChange"
                >
                  <el-option
                    label="预约信已下载"
                    value="appointment_downloaded"
                  />
                  <el-option label="预约已取消" value="appointment_canceled" />
                  <el-option label="账户已删除" value="account_deleted" />
                  <el-option label="已支付" value="payed" />
                  <el-option label="支付失败" value="payment_failed" />
                </el-select>
              </div>

              <div class="filter-group">
                <label class="filter-label">排序方式</label>
                <el-select
                  v-model="sortBy"
                  placeholder="选择排序"
                  class="filter-select"
                  @change="handleSortChange"
                >
                  <el-option
                    label="预约日期（最新）"
                    value="appointment_date_desc"
                  />
                  <el-option
                    label="预约日期（最早）"
                    value="appointment_date_asc"
                  />
                  <el-option label="更新时间（最新）" value="updated_at_desc" />
                  <el-option label="更新时间（最早）" value="updated_at_asc" />
                </el-select>
              </div>
            </div>

            <div class="filter-actions">
              <el-button @click="resetFilters">
                <el-icon><RefreshLeft /></el-icon>
                重置筛选
              </el-button>
              <el-button type="primary" @click="applyFilters">
                <el-icon><Search /></el-icon>
                应用筛选
              </el-button>
              <el-button
                type="success"
                @click="exportToExcel"
                :loading="exportLoading"
              >
                <el-icon><Download /></el-icon>
                导出Excel
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <div class="orders-container" v-loading="loading">
        <!-- 卡片视图 -->
        <div v-if="viewMode === 'card'" class="cards-grid">
          <div
            v-for="order in displayedOrders"
            :key="order.order_id"
            class="order-card"
          >
            <el-card class="card-wrapper" shadow="hover">
              <!-- 卡片头部 -->
              <div class="card-header">
                <div class="order-info">
                  <div class="order-id">
                    <el-icon><Tickets /></el-icon>
                    {{ order.order_id }}
                  </div>
                  <div class="order-status">
                    <el-tag
                      :type="getStatusInfo(order.order_status).type"
                      effect="light"
                    >
                      <el-icon size="12" style="margin-right: 4px">
                        <component
                          :is="getStatusInfo(order.order_status).icon"
                        />
                      </el-icon>
                      {{ getStatusInfo(order.order_status).text }}
                    </el-tag>
                  </div>
                </div>
                <div class="order-actions">
                  <el-dropdown trigger="click" placement="bottom-end">
                    <el-button type="primary" size="small">
                      操作
                      <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="viewOrderDetail(order)">
                          <el-icon><View /></el-icon>
                          查看详情
                        </el-dropdown-item>
                        <el-dropdown-item
                          @click="downloadAppointmentPdf(order)"
                        >
                          <el-icon><Download /></el-icon>
                          下载预约信
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="order.visaType[0][0] === 'che'"
                          @click="getAccountPassword(order)"
                        >
                          <el-icon><User /></el-icon>
                          账密提取
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="order.order_status === 'account_deleted'"
                          @click="rescheduleOrder(order)"
                        >
                          <el-icon><RefreshRight /></el-icon>
                          重新预约
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="order.order_status === 'appointment_downloaded'"
                          @click="cancelAppointment(order)"
                          divided
                        >
                          <el-icon><CircleClose /></el-icon>
                          取消预约
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>

              <!-- 客户信息 -->
              <div class="customer-section">
                <div class="section-title">
                  <el-icon><User /></el-icon>
                  客户信息
                </div>
                <div class="customer-list">
                  <div
                    v-for="(customer, index) in order.clients || []"
                    :key="index"
                    class="customer-item"
                  >
                    <div class="customer-name-row">
                      <span class="customer-name">{{ customer.name }}</span>
                      <el-button
                        type="text"
                        size="small"
                        class="copy-btn"
                        @click.stop="copyCustomerName(customer.name)"
                        title="复制姓名"
                      >
                        <el-icon><DocumentCopy /></el-icon>
                      </el-button>
                    </div>
                    <div class="customer-passport">{{ customer.passport }}</div>
                  </div>
                </div>
              </div>

              <!-- 签证信息 -->
              <div class="visa-section">
                <div class="section-title">
                  <el-icon><Document /></el-icon>
                  签证信息
                </div>
                <div class="visa-info">
                  <div
                    v-if="order.visaType && Array.isArray(order.visaType)"
                    class="visa-list"
                  >
                    <el-tag
                      v-for="(visa, index) in order.visaType"
                      :key="index"
                      type="info"
                      effect="light"
                      size="small"
                      class="visa-tag"
                    >
                      {{ getVisaDisplayName(visa) }}
                    </el-tag>
                  </div>
                </div>
              </div>

              <!-- 时间信息 -->
              <div class="time-section">
                <div class="time-item">
                  <span class="time-label">创建时间:</span>
                  <span class="time-value">{{
                    formatDateTime(order.created_at)
                  }}</span>
                </div>
                <div class="time-item">
                  <span class="time-label">完成时间:</span>
                  <span class="time-value">{{
                    formatDateTime(order.updated_at)
                  }}</span>
                </div>
                <div v-if="order.travel_date" class="time-item">
                  <span class="time-label">出行日期:</span>
                  <span class="time-value">{{ order.travel_date }}</span>
                </div>
                <div
                  v-if="order.appointment_date"
                  class="time-item appointment-time"
                >
                  <span class="time-label">预约日期:</span>
                  <span class="time-value">{{ order.appointment_date }}</span>
                </div>
                <div
                  v-if="order.appointment_time"
                  class="time-item appointment-time"
                >
                  <span class="time-label">预约时间:</span>
                  <span class="time-value">{{ order.appointment_time }}</span>
                </div>
              </div>

              <!-- 操作员和备注 -->
              <div class="footer-section">
                <div class="operator-info">
                  <el-icon><UserFilled /></el-icon>
                  操作员: {{ order.operator_name || '未知' }}
                </div>
                <div class="customer-info">
                  <el-icon><Connection /></el-icon>
                  来源: {{ order.customer || '未知' }}
                </div>
                <div v-if="order.remark" class="remark-info">
                  <el-icon><EditPen /></el-icon>
                  {{ order.remark }}
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else class="table-container">
          <el-table :data="displayedOrders" style="width: 100%; height: 100%">
            <el-table-column prop="order_id" label="订单号" width="150" />
            <el-table-column label="客户" min-width="250">
              <template #default="{ row }">
                <div class="customer-names">
                  <div
                    v-for="(customer, index) in row.clients || []"
                    :key="index"
                    class="customer-name-item"
                  >
                    <span class="customer-name-text">{{ customer.name }}</span>
                    <el-button
                      type="text"
                      size="small"
                      class="copy-btn"
                      @click.stop="copyCustomerName(customer.name)"
                      title="复制姓名"
                    >
                      <el-icon><DocumentCopy /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="签证类型" min-width="200">
              <template #default="{ row }">
                <div
                  v-if="row.visaType && Array.isArray(row.visaType)"
                  class="visa-list"
                >
                  <el-tag
                    v-for="(visa, index) in row.visaType"
                    :key="index"
                    type="info"
                    effect="light"
                    size="small"
                    class="visa-tag"
                  >
                    {{ getVisaDisplayName(visa) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="travel_date" label="出行日期" width="120" />
            <el-table-column label="状态" width="140">
              <template #default="{ row }">
                <el-tag
                  :type="getStatusInfo(row.order_status).type"
                  effect="light"
                  size="small"
                >
                  <el-icon size="12" style="margin-right: 4px">
                    <component :is="getStatusInfo(row.order_status).icon" />
                  </el-icon>
                  {{ getStatusInfo(row.order_status).text }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="updated_at" label="完成时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.updated_at) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="appointment_date"
              label="预约日期"
              width="120"
            >
              <template #default="{ row }">
                <span v-if="row.appointment_date" class="appointment-date">
                  {{ row.appointment_date }}
                </span>
                <span v-else class="no-appointment">-</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="appointment_time"
              label="预约时间"
              width="120"
            >
              <template #default="{ row }">
                <span v-if="row.appointment_time" class="appointment-time">
                  {{ row.appointment_time }}
                </span>
                <span v-else class="no-appointment">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="operator_name" label="操作员" width="100" />
            <el-table-column prop="customer" label="来源" width="100" />
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-dropdown trigger="click" placement="bottom-end">
                  <el-button type="primary" size="small" @click.stop>
                    操作
                    <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="viewOrderDetail(row)">
                        <el-icon><View /></el-icon>
                        查看详情
                      </el-dropdown-item>
                      <el-dropdown-item @click="downloadAppointmentPdf(row)">
                        <el-icon><Download /></el-icon>
                        下载预约信
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="row.order_status === 'account_deleted'"
                        @click="rescheduleOrder(row)"
                      >
                        <el-icon><RefreshRight /></el-icon>
                        重新预约
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="row.order_status === 'appointment_downloaded'"
                        @click="cancelAppointment(row)"
                        divided
                      >
                        <el-icon><CircleClose /></el-icon>
                        取消预约
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 空状态 -->
        <div
          v-if="displayedOrders.length === 0 && !loading"
          class="empty-state"
        >
          <el-empty
            image="/images/empty-orders.svg"
            description="暂无已完成订单"
          >
            <template #image>
              <el-icon size="80" class="empty-icon"
                ><DocumentChecked
              /></el-icon>
            </template>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 固定分页器 -->
    <div v-if="displayedOrders.length > 0" class="fixed-pagination">
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 订单详情弹窗 -->
    <el-dialog
      v-model="orderDetailVisible"
      title="订单详情"
      width="800px"
      top="5vh"
    >
      <div v-if="selectedOrder" class="order-detail">
        <!-- 订单基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单号">
              {{ selectedOrder.order_id }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag
                :type="getStatusInfo(selectedOrder.order_status).type"
                effect="light"
              >
                <el-icon size="12" style="margin-right: 4px">
                  <component
                    :is="getStatusInfo(selectedOrder.order_status).icon"
                  />
                </el-icon>
                {{ getStatusInfo(selectedOrder.order_status).text }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(selectedOrder.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="完成时间">
              {{ formatDateTime(selectedOrder.updated_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="出行日期">
              {{ selectedOrder.travel_date || '未指定' }}
            </el-descriptions-item>
            <el-descriptions-item label="预约日期">
              <span
                v-if="selectedOrder.appointment_date"
                class="appointment-date"
              >
                {{ selectedOrder.appointment_date }}
              </span>
              <span v-else class="no-appointment">未预约</span>
            </el-descriptions-item>
            <el-descriptions-item label="预约时间">
              <span
                v-if="selectedOrder.appointment_time"
                class="appointment-time"
              >
                {{ selectedOrder.appointment_time }}
              </span>
              <span v-else class="no-appointment">未预约</span>
            </el-descriptions-item>
            <el-descriptions-item label="操作员">
              {{ selectedOrder.operator || '未知' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 客户信息 -->
        <div class="detail-section">
          <h3>客户信息</h3>
          <el-table
            :data="selectedOrder.clients || []"
            border
            style="width: 100%"
          >
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="passport" label="护照号" />
            <el-table-column prop="phone" label="电话" />
            <el-table-column prop="email" label="邮箱" />
          </el-table>
        </div>

        <!-- 签证信息 -->
        <div class="detail-section">
          <h3>签证信息</h3>
          <div class="visa-detail-list">
            <div
              v-for="(visa, index) in selectedOrder.visaType || []"
              :key="index"
              class="visa-detail-item"
            >
              <el-tag type="info" effect="light">
                {{ getVisaDisplayName(visa) }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div v-if="selectedOrder.remark" class="detail-section">
          <h3>备注信息</h3>
          <el-input
            v-model="selectedOrder.remark"
            type="textarea"
            :rows="3"
            readonly
          />
        </div>

        <!-- 如果是已取消的订单，显示取消原因 -->
        <div
          v-if="selectedOrder.order_status === 'appointment_canceled'"
          class="detail-section"
        >
          <h3>取消原因</h3>
          <el-button
            type="danger"
            text
            size="small"
            @click="showCancelReason(selectedOrder)"
          >
            <el-icon><InfoFilled /></el-icon>
            查看取消原因
          </el-button>
        </div>

        <!-- 变更历史 -->
        <div class="detail-section">
          <h3>变更历史</h3>
          <el-button
            type="primary"
            text
            size="small"
            @click="showUpdateHistory(selectedOrder.order_id)"
          >
            <el-icon><EditPen /></el-icon>
            查看变更历史
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 取消原因弹窗 -->
    <el-dialog
      v-model="cancelReasonVisible"
      title="取消原因"
      width="500px"
      center
    >
      <div v-loading="loadingCancelReason" class="cancel-reason-container">
        <div v-if="cancelReasonData" class="cancel-content">
          <div class="cancel-header">
            <el-icon class="cancel-icon"><CircleClose /></el-icon>
            <span class="cancel-title">预约已取消</span>
          </div>

          <div class="cancel-details">
            <div class="cancel-item">
              <label>订单号：</label>
              <span>{{ cancelReasonData.order_id }}</span>
            </div>

            <div class="cancel-item">
              <label>取消时间：</label>
              <span>{{ formatDateTime(cancelReasonData.updated_at) }}</span>
            </div>

            <div class="cancel-message">
              <label>取消原因：</label>
              <div class="message-content">
                {{ cancelReasonData.message }}
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-cancel-message">
          <el-empty description="无法获取取消原因" />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelReasonVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 变更历史弹窗 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="变更历史记录"
      width="70%"
      :close-on-click-modal="false"
      class="history-dialog"
    >
      <div v-loading="historyLoading" class="history-content">
        <div
          v-if="updateHistory.length === 0 && !historyLoading"
          class="no-history"
        >
          <el-empty description="暂无更新记录" />
        </div>

        <div v-else class="history-timeline">
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in updateHistory"
              :key="index"
              :timestamp="formatDateTime(record.updated_at)"
              placement="top"
              :type="getTimelineType(record.action)"
              :icon="getTimelineIcon(record.action)"
            >
              <el-card class="history-card">
                <div class="history-header">
                  <span class="action-type">{{
                    getActionText(record.action)
                  }}</span>
                  <span class="operator"
                    >操作人：{{ record.operator || '系统' }}</span
                  >
                </div>

                <div
                  v-if="record.changes && record.changes.length > 0"
                  class="changes-list"
                >
                  <div class="changes-title">变更内容：</div>
                  <div
                    v-for="(change, changeIndex) in record.changes"
                    :key="changeIndex"
                    class="change-item"
                  >
                    <span class="field-name"
                      >{{ getFieldName(change.field) }}：</span
                    >
                    <span class="old-value">{{
                      change.old_value || '空'
                    }}</span>
                    <span class="arrow">→</span>
                    <span class="new-value">{{
                      change.new_value || '空'
                    }}</span>
                  </div>
                </div>

                <div v-if="record.remark" class="remark">
                  <div class="remark-title">备注：</div>
                  <div class="remark-content">{{ record.remark }}</div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 预约信PDF下载进度弹窗 -->
    <el-dialog
      v-model="appointmentDownloadDialogVisible"
      title="下载预约信PDF"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <div class="download-progress-container">
        <div class="progress-content">
          <!-- 加载图标 -->
          <div class="loading-icon">
            <el-icon
              v-if="appointmentDownloadProgress.status !== 'completed'"
              class="is-loading"
              :size="40"
            >
              <Loading />
            </el-icon>
            <el-icon v-else class="success-icon" :size="40">
              <CircleCheck />
            </el-icon>
          </div>

          <!-- 状态信息 -->
          <div class="status-info">
            <div class="status-message">
              {{ appointmentDownloadProgress.message }}
            </div>
            <div v-if="appointmentDownloadProgress.taskId" class="task-id">
              任务ID: {{ appointmentDownloadProgress.taskId }}
            </div>
          </div>

          <!-- 进度指示器 -->
          <div class="progress-indicator">
            <el-progress
              v-if="appointmentDownloadProgress.status === 'processing'"
              :percentage="100"
              :indeterminate="true"
              :duration="3"
              status="success"
            />
            <el-progress
              v-else-if="appointmentDownloadProgress.status === 'completed'"
              :percentage="100"
              status="success"
            />
            <el-progress
              v-else-if="appointmentDownloadProgress.status === 'failed'"
              :percentage="100"
              status="exception"
            />
            <el-progress
              v-else
              :percentage="30"
              :indeterminate="true"
              :duration="5"
            />
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="progress-actions">
          <el-button
            v-if="appointmentDownloadProgress.status !== 'completed'"
            @click="cancelAppointmentDownload"
            type="default"
          >
            取消下载
          </el-button>
          <el-button
            v-else
            @click="appointmentDownloadDialogVisible = false"
            type="primary"
          >
            完成
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 账密提取弹窗 -->
    <el-dialog v-model="accountDialogVisible" title="账密提取" width="500px">
      <div class="account-dialog">
        <div class="field-row" style="margin-bottom: 10px">
          <span class="field-label" style="color: #666; margin-right: 6px"
            >订单号：</span
          >
          <code>{{ accountDialogData.orderId }}</code>
        </div>

        <div
          v-if="!accountDialogData.username || !accountDialogData.password"
          style="margin-bottom: 12px"
        >
          <el-alert type="warning" title="该订单暂无可用账号或密码" show-icon />
        </div>

        <div
          class="field-row"
          style="display: flex; align-items: center; margin-top: 8px"
        >
          <span class="field-label" style="width: 60px; color: #666"
            >账号：</span
          >
          <el-input
            v-model="accountDialogData.username"
            readonly
            placeholder="暂无"
            style="flex: 1; margin-right: 8px"
          />
          <el-button
            :disabled="!accountDialogData.username"
            @click="copyText(accountDialogData.username, '账号已复制到剪贴板')"
          >
            <el-icon><DocumentCopy /></el-icon>
          </el-button>
        </div>

        <div
          class="field-row"
          style="display: flex; align-items: center; margin-top: 8px"
        >
          <span class="field-label" style="width: 60px; color: #666"
            >密码：</span
          >
          <el-input
            v-model="accountDialogData.password"
            readonly
            placeholder="暂无"
            type="password"
            show-password
            style="flex: 1; margin-right: 8px"
          />
          <el-button
            :disabled="!accountDialogData.password"
            @click="copyText(accountDialogData.password, '密码已复制到剪贴板')"
          >
            <el-icon><DocumentCopy /></el-icon>
          </el-button>
        </div>
      </div>
      <template #footer>
        <el-button @click="accountDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useVfsStore } from '@/stores/useVfsStore';
import { useUserStore } from '@/stores/user';
import http from '@/utils/http';
import { ElMessage, ElMessageBox } from 'element-plus';

import {
  DocumentChecked,
  CircleCheck,
  CircleClose,
  Warning,
  Filter,
  Grid,
  List,
  DataAnalysis,
  Search,
  RefreshLeft,
  RefreshRight,
  Tickets,
  View,
  User,
  Document,
  UserFilled,
  EditPen,
  Download,
  ArrowDown,
  Connection,
  InfoFilled,
  Plus,
  Delete,
  Switch,
  DocumentCopy,
} from '@element-plus/icons-vue';

const vfsStore = useVfsStore();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
// 服务端分页：当前页数据与总数
const pageItems = ref([]);
const total = ref(0);

const orders = ref([]); // 兼容旧逻辑（后续可移除）
const viewMode = ref('card');

// 构造分页+筛选查询参数（顶层通用）
const buildQuery = () => {
  const [start, end] = dateRange.value || [];
  return {
    page: currentPage.value,
    page_size: pageSize.value,
    search: searchKeyword.value || '',
    country: selectedCountry.value || '',
    start_date: start || '',
    end_date: end || '',
    operators: (selectedOperators.value || []).join(','),
    statuses: (selectedStatuses.value || []).join(','),
    sort_by: sortBy.value || 'updated_at_desc',
  };
};

const isFilterExpanded = ref(false);
const operatorLoading = ref(false);
const operatorList = ref([]);
const exportLoading = ref(false);

// 筛选条件
const searchKeyword = ref('');
const selectedCountry = ref('');
const dateRange = ref([]);
const selectedOperators = ref([]);
const selectedStatuses = ref([
  'appointment_downloaded',
  'payed',
  'payment_failed',
]); // 默认显示已下载和已支付的订单
const sortBy = ref('updated_at_desc'); // 默认按更新时间降序排列

// 分页
const currentPage = ref(1);
const pageSize = ref(20);

// 弹窗
const orderDetailVisible = ref(false);
const selectedOrder = ref(null);

// 历史记录相关
const historyDialogVisible = ref(false);
const currentOrderId = ref('');
const updateHistory = ref([]);
const historyLoading = ref(false);

// 取消原因弹窗相关
const cancelReasonVisible = ref(false);
const cancelReasonData = ref(null);
const loadingCancelReason = ref(false);

// 预约信PDF下载进度弹窗相关
const appointmentDownloadDialogVisible = ref(false);
const appointmentDownloadProgress = ref({
  status: 'pending',
  message: '正在准备下载...',
  taskId: null,
});
let appointmentDownloadCancelFn = null;

// 计算属性
const totalCount = computed(() => {
  // 优先使用服务端返回的总数；若无则回退本地数组长度（兼容旧逻辑）
  return total.value || (Array.isArray(orders.value) ? orders.value.length : 0);
});

const filteredOrders = computed(() => {
  // 确保orders.value是数组
  if (!Array.isArray(orders.value)) {
    return [];
  }

  let filtered = orders.value.filter(order => {
    // 确保order对象存在
    if (!order || typeof order !== 'object') {
      return false;
    }

    // 只显示已完成的订单 (appointment_downloaded, appointment_canceled, account_deleted, payed)
    return [
      'appointment_downloaded',
      'appointment_canceled',
      'account_deleted',
      'payed',
      'payment_failed',
    ].includes(order.order_status);
  });

  // 状态筛选
  if (selectedStatuses.value && selectedStatuses.value.length > 0) {
    filtered = filtered.filter(order =>
      selectedStatuses.value.includes(order.order_status)
    );
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(order => {
      const customers = order.clients || [];
      return (
        order.order_id.toLowerCase().includes(keyword) ||
        customers.some(
          customer =>
            customer.name?.toLowerCase().includes(keyword) ||
            customer.passport?.toLowerCase().includes(keyword)
        )
      );
    });
  }

  // 国家筛选
  if (selectedCountry.value) {
    filtered = filtered.filter(order => {
      const visas = order.visaType || [];
      return visas.some(
        visa => Array.isArray(visa) && visa[0] === selectedCountry.value
      );
    });
  }

  // 时间范围筛选
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value;
    filtered = filtered.filter(order => {
      const orderDate = order.updated_at.split(' ')[0]; // 取日期部分
      return orderDate >= startDate && orderDate <= endDate;
    });
  }

  // 操作员筛选
  if (selectedOperators.value && selectedOperators.value.length > 0) {
    filtered = filtered.filter(
      order =>
        selectedOperators.value.includes(order.operator) ||
        selectedOperators.value.includes(order.operator_name)
    );
  }

  // 排序
  if (sortBy.value) {
    filtered.sort((a, b) => {
      switch (sortBy.value) {
        case 'appointment_date_desc':
          return (
            new Date(b.appointment_date || 0) -
            new Date(a.appointment_date || 0)
          );
        case 'appointment_date_asc':
          return (
            new Date(a.appointment_date || 0) -
            new Date(b.appointment_date || 0)
          );
        case 'updated_at_desc':
          return new Date(b.updated_at || 0) - new Date(a.updated_at || 0);
        case 'updated_at_asc':
          return new Date(a.updated_at || 0) - new Date(b.updated_at || 0);
        default:
          return 0;
      }
    });
  }

  return filtered;
});

const filteredCount = computed(() => {
  // 优先使用服务端返回的总数；无总数时回退到前端过滤结果长度（兼容旧逻辑）
  return (
    total.value ||
    (Array.isArray(filteredOrders.value) ? filteredOrders.value.length : 0)
  );
});

const displayedOrders = computed(() => {
  // 服务端分页：当 total 有值或当前页有数据时，直接使用当前页数据
  if (
    total.value ||
    (Array.isArray(pageItems.value) && pageItems.value.length > 0)
  ) {
    return pageItems.value;
  }
  // 回退：前端切片
  if (!Array.isArray(filteredOrders.value)) return [];
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredOrders.value.slice(start, end);
});

// 方法
const toggleFilter = () => {
  isFilterExpanded.value = !isFilterExpanded.value;
};

const handleSearch = () => {
  currentPage.value = 1;
  loadOrders();
};

const handleCountryChange = () => {
  currentPage.value = 1;
  loadOrders();
};

const handleDateChange = () => {
  currentPage.value = 1;
  loadOrders();
};

const handleOperatorChange = () => {
  currentPage.value = 1;
  loadOrders();
};

const handleStatusChange = () => {
  currentPage.value = 1;
  loadOrders();
};

const handleSortChange = () => {
  currentPage.value = 1;
  loadOrders();
};

// 复制客户姓名函数
const copyCustomerName = async name => {
  try {
    await navigator.clipboard.writeText(name);
    ElMessage.success('客户姓名已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    // 降级方案：使用传统方法
    try {
      const textArea = document.createElement('textarea');
      textArea.value = name;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      ElMessage.success('客户姓名已复制到剪贴板');
    } catch (fallbackError) {
      console.error('降级复制也失败:', fallbackError);
      ElMessage.error('复制失败，请手动复制');
    }
  }
};

// 取消预约信下载函数
const cancelAppointmentDownload = () => {
  if (appointmentDownloadCancelFn) {
    appointmentDownloadCancelFn();
  }
  appointmentDownloadDialogVisible.value = false;
  ElMessage.warning('已取消预约信PDF下载');
};

// 异步下载预约信PDF函数
const downloadAppointmentPdfAsync = async (order, pdfUrl, filename) => {
  let pollTimeoutId = null;
  let startTime = Date.now();
  let isCancelled = false;
  const maxWaitTime = 5 * 60 * 1000; // 5分钟超时

  const cleanup = () => {
    if (pollTimeoutId) {
      clearTimeout(pollTimeoutId);
      pollTimeoutId = null;
    }
  };

  // 设置取消函数
  appointmentDownloadCancelFn = () => {
    isCancelled = true;
    cleanup();
  };

  try {
    // 初始化进度状态并显示弹窗
    appointmentDownloadProgress.value = {
      status: 'pending',
      message: '正在启动下载任务...',
      taskId: null,
    };
    appointmentDownloadDialogVisible.value = true;

    // 第一步：启动下载任务
    const startResponse = await http.post(
      '/api/download_appointment_with_filename',
      {
        order_id: order.order_id,
        pdf_url: pdfUrl,
        filename: filename,
      }
    );

    if (startResponse?.code !== 1) {
      appointmentDownloadDialogVisible.value = false;
      ElMessage.warning(startResponse?.message || '启动下载任务失败');
      return;
    }

    const taskId = startResponse.data.task_id;
    console.log('下载任务已启动，任务ID:', taskId);

    // 更新进度状态
    appointmentDownloadProgress.value = {
      status: 'pending',
      message: '下载任务已启动，正在队列中等待...',
      taskId: taskId,
    };

    // 第二步：轮询任务状态
    const pollStatus = async () => {
      try {
        // 检查是否已取消
        if (isCancelled) {
          return;
        }

        // 检查超时
        const elapsed = Date.now() - startTime;
        if (elapsed > maxWaitTime) {
          throw new Error('下载超时，请稍后重试');
        }

        const statusResponse = await http.get(
          `/api/download_appointment_status/${taskId}`
        );

        if (statusResponse?.code !== 1) {
          throw new Error('查询任务状态失败');
        }

        const taskInfo = statusResponse.data;
        console.log('任务状态:', taskInfo);

        // 更新弹窗状态
        appointmentDownloadProgress.value = {
          status: taskInfo.status,
          message: taskInfo.message || '正在处理中...',
          taskId: taskId,
        };

        if (taskInfo.status === 'processing') {
          // 继续轮询
          pollTimeoutId = setTimeout(() => {
            if (!isCancelled) pollStatus();
          }, 2000); // 2秒后再次查询
        } else if (taskInfo.status === 'completed') {
          // 下载完成
          appointmentDownloadProgress.value = {
            status: 'completed',
            message: '下载完成，正在准备文件...',
            taskId: taskId,
          };

          const pdfData = taskInfo.result;
          if (!pdfData || !pdfData.pdf_content) {
            throw new Error('下载的数据格式错误');
          }

          // 将base64数据转换为blob并下载
          const base64Data = pdfData.pdf_content;
          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);

          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }

          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], { type: 'application/pdf' });

          // 创建下载链接
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = pdfData.filename || filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          // 关闭弹窗
          setTimeout(() => {
            appointmentDownloadDialogVisible.value = false;
            ElMessage.success(
              `预约信下载成功: ${pdfData.filename || filename}`
            );
          }, 1000);
        } else if (taskInfo.status === 'failed') {
          // 下载失败
          throw new Error(taskInfo.message || '下载失败');
        } else if (taskInfo.status === 'pending') {
          // 继续轮询
          pollTimeoutId = setTimeout(() => {
            if (!isCancelled) pollStatus();
          }, 2000);
        } else {
          // 未知状态
          throw new Error(`未知的任务状态: ${taskInfo.status}`);
        }
      } catch (pollError) {
        console.error('轮询任务状态失败:', pollError);
        cleanup();
        throw pollError;
      }
    };

    // 开始轮询
    pollTimeoutId = setTimeout(() => {
      if (!isCancelled) pollStatus();
    }, 1000); // 1秒后开始查询
  } catch (error) {
    console.error('异步下载预约信失败:', error);
    cleanup();
    appointmentDownloadDialogVisible.value = false;

    // 降级方案：复制文件名并直接下载
    try {
      await navigator.clipboard.writeText(filename);
      ElMessage.warning('异步下载失败，文件名已复制到剪贴板');
    } catch (clipboardError) {
      console.log('剪贴板复制失败:', clipboardError);
    }

    // 直接下载
    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = filename;
    link.target = '_blank';
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.error(`异步下载失败: ${error.message || error}`);
  }
};

const resetFilters = () => {
  searchKeyword.value = '';
  selectedCountry.value = '';
  dateRange.value = [];
  selectedOperators.value = [];
  selectedStatuses.value = [
    'appointment_downloaded',
    'payment_failed',
    'payed',
  ]; // 重置为默认状态
  sortBy.value = 'appointment_date_desc'; // 重置为默认排序
  currentPage.value = 1;
};

const applyFilters = () => {
  currentPage.value = 1;
};

const accountDialogVisible = ref(false);
const accountDialogData = ref({ orderId: '', username: '', password: '' });

const copyText = async (text, successMsg = '已复制到剪贴板') => {
  try {
    await navigator.clipboard.writeText(text);
    ElMessage.success(successMsg);
  } catch (error) {
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      ElMessage.success(successMsg);
    } catch (fallbackError) {
      ElMessage.error('复制失败，请手动复制');
    }
  }
};

const getAccountPassword = async order => {
  try {
    const res = await http.post('/api/get_account_password', {
      order_id: order.order_id,
    });
    if (res?.code === 1) {
      const { vfs_account, vfs_password } = res.data || {};
      accountDialogData.value = {
        orderId: order.order_id,
        username: vfs_account || '',
        password: vfs_password || '',
      };
      if (!vfs_account || !vfs_password) {
        ElMessage.warning('该订单暂无可用账号或密码');
      }
      accountDialogVisible.value = true;
    } else {
      ElMessage.error(res?.message || '失败,请重试');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('失败,请重试', error);
      ElMessage.error('操作失败');
    }
  }
};

// 重新预约
const rescheduleOrder = async order => {
  try {
    await ElMessageBox.confirm(
      `确认重新安排订单 ${order.order_id} 的预约吗？这将重新启动预约流程。`,
      '重新安排预约',
      {
        confirmButtonText: '确认重新安排',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 调用后端API重新安排预约
    const res = await http.post('/api/reschedule_order', {
      order_id: order.order_id,
    });

    if (res?.code === 1) {
      ElMessage.success('预约已重新安排');
      await loadOrders(); // 重新加载订单列表
    } else {
      ElMessage.error(res?.message || '重新安排失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新安排预约失败:', error);
      ElMessage.error('操作失败');
    }
  }
};

const exportToExcel = async () => {
  try {
    exportLoading.value = true;
    const res = await http.post('/api/export_completed_orders', buildQuery(), {
      responseType: 'blob',
    });

    // 兼容：可能直接返回 Blob，或 { data: Blob, headers: ... }
    const blob =
      res?.data instanceof Blob
        ? res.data
        : new Blob([res?.data || res], {
            type:
              res?.headers?.['content-type'] ||
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          });

    const dispo = res?.headers?.['content-disposition'] || '';
    const match = /filename\*?=([^;]+)/i.exec(dispo);
    const filename = decodeURIComponent(
      (match?.[1] || '已完成订单.xlsx').replace(/UTF-8''/, '').replace(/"/g, '')
    );

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Excel导出失败:', error);
    ElMessage.error('导出失败，请重试');
  } finally {
    exportLoading.value = false;
  }
};

const handleSizeChange = size => {
  pageSize.value = size;
  currentPage.value = 1;
  loadOrders();
};

const handleCurrentChange = page => {
  currentPage.value = page;
  loadOrders();
};

// 状态映射函数
const getStatusInfo = orderStatus => {
  const statusMap = {
    appointment_downloaded: {
      text: '预约信已下载',
      type: 'success',
      icon: CircleCheck,
    },
    appointment_canceled: {
      text: '预约已取消',
      type: 'danger',
      icon: CircleClose,
    },
    account_deleted: {
      text: '账号已删除',
      type: 'warning',
      icon: Warning,
    },
    payed: {
      text: '已支付',
      type: 'success',
      icon: CircleCheck,
    },
    payment_failed: {
      text: '支付失败',
      type: 'warning',
      icon: Warning,
    },
  };

  return (
    statusMap[orderStatus] || {
      text: '已完成',
      type: 'success',
      icon: CircleCheck,
    }
  );
};

const loadOrders = async () => {
  loading.value = true;
  try {
    const res = await http.get('/api/get_completed_orders', {
      params: buildQuery(),
    });
    if (res?.code === 1 && res.data) {
      if (Array.isArray(res.data.items)) {
        pageItems.value = res.data.items;
        total.value = res.data.total ?? 0;
        if (res.data.extra?.operators?.length) {
          operatorList.value = res.data.extra.operators.map(o => ({
            id: o,
            username: o,
          }));
        }
      } else {
        orders.value = res.data || [];
        extractOperatorsFromOrders();
      }
    }
  } catch (error) {
    console.error('加载订单失败:', error);
  } finally {
    loading.value = false;
  }
};

const extractOperatorsFromOrders = () => {
  // 从订单数据中提取唯一的操作员列表
  const operators = new Set();

  // 确保orders.value是数组
  if (Array.isArray(orders.value)) {
    orders.value.forEach(order => {
      if (
        order &&
        order.operator_name &&
        typeof order.operator_name === 'string'
      ) {
        operators.add(order.operator_name);
      }
    });
  }

  // 转换为下拉框需要的格式
  operatorList.value = Array.from(operators)
    .filter(operator => operator && operator.trim()) // 过滤空值
    .sort((a, b) => a.localeCompare(b, 'zh-CN')) // 按中文排序
    .map(operator => ({
      id: operator,
      username: operator,
    }));
};

const viewOrderDetail = order => {
  selectedOrder.value = order;
  orderDetailVisible.value = true;
};

const downloadAppointmentPdf = async order => {
  try {
    // 如果 missionCode != 'spain'，则视为 VFS 订单，直接从服务器本地下载
    const missionCode =
      Array.isArray(order.visaType) &&
      Array.isArray(order.visaType[0]) &&
      order.visaType[0][0]
        ? String(order.visaType[0][0]).toLowerCase()
        : '';

    if (missionCode && missionCode !== 'spain') {
      const resp = await http.get(
        `/api/vfs/orders/${order.order_id}/appointment-pdf`,
        {
          responseType: 'blob',
        }
      );

      const blob = new Blob([resp.data], { type: 'application/pdf' });
      // 从响应头解析文件名
      const cd =
        resp.headers?.['content-disposition'] ||
        resp.headers?.get?.('content-disposition') ||
        '';
      let filename = `${order.order_id}.pdf`;
      try {
        const utf8Match = cd.match(/filename\*=(?:UTF-8'')?([^;\n]+)/i);
        const asciiMatch = cd.match(/filename="?([^";\n]+)"?/i);
        if (utf8Match && utf8Match[1]) {
          filename = decodeURIComponent(utf8Match[1]);
        } else if (asciiMatch && asciiMatch[1]) {
          filename = asciiMatch[1];
        }
      } catch {}

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      return;
    }

    // 非 VFS 订单：沿用原逻辑
    const res = await http.post('/api/get-appointment-pdf', {
      order_id: order.order_id,
    });

    if (res?.code === 1) {
      const pdfData = res.data;

      // 生成新的文件名：客户姓名+预约领区和类型+预约日期和时间
      const customerNames =
        (order.clients || [])
          .map(client => client.name)
          .filter(name => name)
          .join('、') || '未知客户';

      const visaInfo =
        (order.visaType || [])
          .map(visa => getVisaDisplayName(visa))
          .join('、') || '未知签证类型';

      // 格式化日期和时间
      let formattedDate = order.appointment_date || '未知日期';
      let formattedTime = order.appointment_time || '未知时间';

      // 将日期格式从 2025-08-11 改为 2025/08/11
      if (formattedDate !== '未知日期') {
        formattedDate = formattedDate.replace(/-/g, '/');
      }

      // 将时间格式从 10:40-10:50 改为 1040-1050
      if (formattedTime !== '未知时间') {
        formattedTime = formattedTime.replace(/:/g, '');
      }

      const customFilename =
        `${customerNames}_${visaInfo}_${formattedDate}_${formattedTime}.pdf`
          .replace(/[\/:*?"<>|]/g, '_') // 替换非法字符为下划线
          .replace(/\s+/g, '') // 删除所有空格
          .replace(/\(|\)/g, ''); // 删除括号

      // 使用异步下载模式
      await downloadAppointmentPdfAsync(order, pdfData.pdf_url, customFilename);
    } else {
      ElMessage.warning(res?.message || '该订单暂无预约信PDF');
    }
  } catch (error) {
    console.error('下载预约信失败:', error);
    ElMessage.error('下载预约信失败');
  }
};

// 取消预约
const cancelAppointment = async order => {
  try {
    // 使用 ElMessageBox.prompt 获取预约信号输入
    const { value: appointment_no } = await ElMessageBox.prompt(
      `确认要取消订单 ${order.order_id} 的预约吗？此操作不可撤销。\n 请输入预约信号以确认取消预约：`,
      '取消预约',
      {
        confirmButtonText: '确认取消',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入预约信号',
        inputType: 'text',
        inputValidator: value => {
          if (!value || value.trim() === '') {
            return '预约信号不能为空';
          }
          return true;
        },
        inputErrorMessage: '请输入有效的预约信号',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      }
    );

    // 调用取消预约API，将预约信号作为参数传入
    const res = await http.post('/api/cancel_appointment', {
      order_id: order.order_id,
      appointment_no: appointment_no.trim(),
    });

    if (res?.code === 1) {
      ElMessage.success('预约已成功取消');
      await loadOrders(); // 重新加载订单列表
    } else {
      ElMessage.error(res?.message || '取消预约失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消预约失败:', error);
      ElMessage.error('取消预约失败');
    }
  }
};

const getVisaDisplayName = visa => {
  if (!Array.isArray(visa) || visa.length < 2) return '未知签证类型';

  // 确保vfsStore.config存在
  if (!vfsStore.config || !Array.isArray(vfsStore.config)) {
    return `${visa[0]} - ${visa[1]}`;
  }

  // 查找对应的显示名称
  const mission = vfsStore.config.find(m => m && m.missionCode === visa[0]);
  if (!mission) return `${visa[0]} - ${visa[1]}`;

  const missionName = mission.missionCodeName || visa[0];

  if (!mission.children || !Array.isArray(mission.children)) {
    return `${missionName} - ${visa[1]}`;
  }

  const center = mission.children.find(c => c && c.isoCode === visa[1]);
  if (!center) return `${missionName} - ${visa[1]}`;

  const centerName = center.centerName || visa[1];

  // 获取签证类型名称
  let visaTypeName = '';
  if (visa.length >= 4) {
    const [missionCode, centerCode, visaCategoryCode, visaCode] = visa;
    const visaCategory = center.children?.find(
      vc => vc.code === visaCategoryCode
    );
    if (visaCategory) {
      const visaType = visaCategory.children?.find(v => v.code === visaCode);
      visaTypeName = visaType?.name || visaCode || '';
    }
  }

  // 如果有签证类型，添加到显示名称中
  if (visaTypeName) {
    return `${missionName} - ${centerName} (${visaTypeName})`;
  }

  return `${missionName} - ${centerName}`;
};

const formatDateTime = dateStr => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 显示更新历史记录
const showUpdateHistory = async orderId => {
  currentOrderId.value = orderId;
  historyDialogVisible.value = true;
  historyLoading.value = true;

  try {
    // 调用后端接口获取历史记录
    const res = await http.get(`/api/order-history/${orderId}`);
    if (res.code === 1) {
      updateHistory.value = res.data || [];
    } else {
      ElMessage.error(res.message || '获取历史记录失败');
      updateHistory.value = [];
    }
  } catch (error) {
    console.error('获取历史记录失败:', error);
    ElMessage.error('获取历史记录失败');
    updateHistory.value = [];
  } finally {
    historyLoading.value = false;
  }
};

// 历史记录辅助函数
const getTimelineType = action => {
  switch (action) {
    case 'create':
      return 'success';
    case 'update':
      return 'primary';
    case 'delete':
      return 'danger';
    case 'status_change':
      return 'warning';
    default:
      return 'info';
  }
};

const getTimelineIcon = action => {
  switch (action) {
    case 'create':
      return 'Plus';
    case 'update':
      return 'EditPen';
    case 'delete':
      return 'Delete';
    case 'status_change':
      return 'Switch';
    default:
      return 'InfoFilled';
  }
};

const getActionText = action => {
  switch (action) {
    case 'create':
      return '创建订单';
    case 'update':
      return '更新信息';
    case 'delete':
      return '删除订单';
    case 'status_change':
      return '状态变更';
    default:
      return '其他操作';
  }
};

const getFieldName = field => {
  const fieldMap = {
    status: '状态',
    order_status: '订单状态',
    visa_type: '签证类型',
    appointment_date: '预约日期',
    appointment_time: '预约时间',
    clients: '申请人信息',
    remark: '备注',
    priority: '优先级',
    customer: '客户来源',
    price: '价格',
    travel_date: '出行日期',
    action: '操作类型',
    // 添加更多字段映射
  };
  return fieldMap[field] || field;
};

// 显示取消原因
const showCancelReason = async order => {
  cancelReasonVisible.value = true;
  loadingCancelReason.value = true;

  try {
    // 这里可以调用API获取取消原因的详细信息
    // 暂时使用订单中的基本信息
    cancelReasonData.value = {
      order_id: order.order_id,
      updated_at: order.updated_at,
      message: '用户主动取消预约', // 这里可以从API获取真实的取消原因
    };
  } catch (error) {
    console.error('获取取消原因失败:', error);
    ElMessage.error('获取取消原因失败');
    cancelReasonData.value = null;
  } finally {
    loadingCancelReason.value = false;
  }
};

// 生命周期
onMounted(async () => {
  // 确保 VFS 配置已加载
  if (!vfsStore.config || vfsStore.config.length === 0) {
    try {
      const resVfsConfig = await http.post('/get_vfs_config');
      vfsStore.setConfig(resVfsConfig.data);
    } catch (error) {
      console.error('加载 VFS 配置失败:', error);
    }
  }

  // 加载订单数据（同时会提取操作员列表）
  await loadOrders();
});
</script>

<style scoped>
/* 与PendingOrders保持相同的样式，但将主题色调整为绿色系 */
.booked-orders-container {
  background: #f5f7fa;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding-bottom: 100px; /* 为浮动分页器预留空间 */
}

/* 固定头部样式 */
.fixed-header {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 100;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
  padding: 16px 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 26px;
  font-weight: 600;
  margin: 0 0 6px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-icon {
  font-size: 30px;
}

.page-subtitle {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-stats {
  display: flex;
  gap: 12px;
}

/* 筛选工具栏样式 */
.filter-section {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.filter-card {
  border: none;
  box-shadow: none;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.filter-toggle-btn {
  margin-left: 10px;
  font-size: 13px;
}

.filter-actions-top {
  display: flex;
  align-items: center;
  gap: 20px;
}

.view-controls .view-button-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

.control-text {
  font-size: 12px;
}

.filter-content {
  margin-top: 12px;
}

.filter-row {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.filter-label {
  font-size: 13px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 6px;
}

.filter-input,
.filter-select {
  width: 200px;
}

.filter-date {
  width: 280px;
}

.filter-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 内容区域样式 */
.content-wrapper {
  flex: 1;
  padding: 10px;
  margin: 0 auto;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.orders-container {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  overflow: hidden;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

/* 固定分页器样式 */
.fixed-pagination {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 48px;
  width: auto;
  max-width: 90vw;
  box-sizing: border-box;
}

/* 分页器容器样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 分页器内容样式 */
.fixed-pagination .el-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
}

/* 确保分页器组件不会溢出 */
.fixed-pagination .el-pagination .el-pager {
  display: flex;
  align-items: center;
}

/* 移动端分页器优化 */
@media (max-width: 768px) {
  .fixed-pagination {
    bottom: 16px;
    padding: 10px 16px;
    min-height: 44px;
    max-width: 95vw;
  }

  .fixed-pagination .el-pagination {
    font-size: 14px;
    --el-pagination-font-size: 14px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .fixed-pagination {
    bottom: 12px;
    padding: 8px 12px;
    min-height: 40px;
    max-width: 98vw;
  }

  .fixed-pagination .el-pagination {
    font-size: 12px;
    gap: 4px;
  }
}

/* 卡片视图样式 - 完全响应式布局，根据屏幕宽度自动调整列数 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 16px;
  width: 100%;
  flex: 1;
  align-content: start;
  /*
   * auto-fill: 根据容器宽度自动创建列
   * minmax(360px, 1fr): 每列最小360px，剩余空间平均分配
   * 完全根据屏幕宽度自动调整列数，不设置上限
   */
}

/* 响应式优化 - 仅在移动设备上调整 */
/* 移动设备优化 */
@media (max-width: 767px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.order-card {
  transition: transform 0.2s ease;
}

.order-card:hover {
  transform: translateY(-2px);
}

.card-wrapper {
  height: 100%;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f2f5;
}

.order-info {
  flex: 1;
}

.order-id {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
}

.order-status {
  display: flex;
  align-items: center;
}

.order-actions {
  display: flex;
  gap: 8px;
}

/* 下拉菜单样式 */
.order-actions .el-dropdown {
  display: inline-block;
}

.order-actions .el-button {
  min-width: 70px;
}

/* 下拉菜单项样式 */
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

/* 危险操作样式 */
:deep(.el-dropdown-menu__item.is-divided) {
  border-top: 1px solid var(--el-border-color-light);
  margin-top: 4px;
  padding-top: 8px;
}

:deep(.el-dropdown-menu__item.is-divided:hover) {
  background-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}

/* 预约时间样式 */
.appointment-time {
  color: var(--el-color-success);
  font-weight: 500;
}

.appointment-date {
  color: var(--el-color-primary);
  font-weight: 500;
}

.no-appointment {
  color: var(--el-color-info);
  font-style: italic;
}

/* 卡片中的预约时间特殊样式 */
.time-item.appointment-time {
  background-color: var(--el-color-success-light-9);
  border-left: 3px solid var(--el-color-success);
  padding-left: 8px;
  margin: 4px 0;
  border-radius: 4px;
}

.time-item.appointment-time .time-label {
  color: var(--el-color-success-dark-2);
  font-weight: 600;
}

.time-item.appointment-time .time-value {
  color: var(--el-color-success);
  font-weight: 500;
}

/* 客户信息区域 */
.customer-section,
.visa-section,
.time-section,
.footer-section {
  margin-bottom: 12px;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 6px;
}

.customer-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.customer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 6px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
}

.customer-name {
  font-weight: 500;
  color: #303133;
}

.customer-passport {
  color: #909399;
  font-family: monospace;
}

/* 签证信息 */
.visa-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.visa-tag {
  font-size: 11px;
}

/* 时间信息 */
.time-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.time-label {
  color: #909399;
}

.time-value {
  color: #303133;
  font-weight: 500;
}

/* 底部信息 */
.footer-section {
  padding-top: 10px;
  border-top: 1px solid #f0f2f5;
  font-size: 12px;
  color: #606266;
}

.operator-info,
.customer-info,
.remark-info {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

/* 列表视图样式 */
.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.customer-names {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.customer-name-item {
  font-size: 12px;
  color: #303133;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.empty-icon {
  color: #dcdfe6;
}

/* 分页器样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding: 12px 0;
}

/* 订单详情弹窗 */
.order-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section h3 {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
  padding-bottom: 6px;
  border-bottom: 1px solid #ebeef5;
}

.visa-detail-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.visa-detail-item {
  margin-bottom: 6px;
}

/* 取消原因弹窗样式 */
.cancel-reason-container {
  min-height: 200px;
}

.cancel-content {
  text-align: left;
}

.cancel-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.cancel-icon {
  color: #f56c6c;
  font-size: 20px;
}

.cancel-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.cancel-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cancel-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.cancel-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.cancel-message {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cancel-message .message-content {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  padding: 12px;
  color: #f56c6c;
  font-size: 14px;
  line-height: 1.5;
}

.no-cancel-message {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
}

/* 历史记录弹窗样式 */
.history-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.history-content {
  min-height: 300px;
}

.no-history {
  text-align: center;
  padding: 40px 0;
}

.history-timeline {
  padding: 0 20px;
}

.history-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.action-type {
  font-weight: 600;
  color: #409eff;
  font-size: 14px;
}

.operator {
  font-size: 12px;
  color: #909399;
}

.changes-list {
  margin-top: 12px;
}

.changes-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
  font-size: 13px;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 13px;
}

.field-name {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.old-value {
  color: #f56c6c;
  background: #fef0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.new-value {
  color: #67c23a;
  background: #f0f9ff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.arrow {
  color: #909399;
  font-weight: bold;
}

.remark {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f2f5;
}

.remark-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
  font-size: 13px;
}

.remark-content {
  color: #606266;
  font-size: 12px;
  line-height: 1.5;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
}

/* 客户名称和复制按钮样式 */
.customer-name-item {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.customer-name-text {
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.customer-name-item .copy-btn {
  padding: 4px 6px;
  min-width: auto;
  height: 24px;
  color: #409eff;
  opacity: 0.8;
  transition: all 0.2s;
  border: 1px solid transparent;
  flex-shrink: 0;
}

.customer-name-item .copy-btn:hover {
  opacity: 1;
  background-color: #ecf5ff;
  border-color: #409eff;
  border-radius: 4px;
  transform: scale(1.05);
}

.customer-name-item:hover .copy-btn {
  opacity: 1;
}

.customer-name-item .copy-btn .el-icon {
  font-size: 14px;
}

/* 卡片模式中的客户名称和复制按钮样式 */
.customer-name-row {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.customer-name-row .customer-name {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  color: #303133;
}

.customer-name-row .copy-btn {
  padding: 4px 6px;
  min-width: auto;
  height: 24px;
  color: #409eff;
  opacity: 0.8;
  transition: all 0.2s;
  margin-left: 4px;
  border: 1px solid transparent;
  flex-shrink: 0;
}

.customer-name-row .copy-btn:hover {
  opacity: 1;
  background-color: #ecf5ff;
  border-color: #409eff;
  border-radius: 4px;
  transform: scale(1.05);
}

.customer-item:hover .copy-btn {
  opacity: 1;
}

.customer-name-row .copy-btn .el-icon {
  font-size: 14px;
}

.customer-names {
  padding: 4px 0;
}

/* 预约信PDF下载进度弹窗样式 */
.download-progress-container {
  text-align: center;
  padding: 20px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.loading-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-icon .is-loading {
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.loading-icon .success-icon {
  color: #67c23a;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-info {
  text-align: center;
}

.status-message {
  font-size: 16px;
  color: #303133;
  margin-bottom: 8px;
  font-weight: 500;
}

.task-id {
  font-size: 12px;
  color: #909399;
  font-family: 'Courier New', monospace;
}

.progress-indicator {
  width: 100%;
  max-width: 300px;
}

.progress-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.progress-actions .el-button {
  min-width: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filter-row {
    flex-direction: column;
    gap: 12px;
  }

  .filter-group {
    min-width: auto;
  }

  .filter-input,
  .filter-select,
  .filter-date {
    width: 100%;
  }

  .content-wrapper {
    padding: 16px;
  }

  .filter-section {
    padding: 12px 16px;
  }

  .page-header {
    padding: 16px;
  }
}
</style>
