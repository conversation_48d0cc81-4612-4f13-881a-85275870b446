<template>
  <div class="pending-orders-container">
    <!-- 固定头部区域 -->
    <div class="fixed-header">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1 class="page-title">
              <el-icon class="title-icon"><Document /></el-icon>
              订单管理
            </h1>
            <p class="page-subtitle">管理和查看所有待处理的签证订单</p>
          </div>
          <div class="header-right">
            <el-button
              type="default"
              size="large"
              icon="Refresh"
              @click="loadOrders"
              style="margin-left: 10px"
            >
              刷新列表
            </el-button>
            <el-button
              type="primary"
              size="large"
              icon="Plus"
              @click="openNewOrder"
            >
              <span>新增客户</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 筛选工具栏 -->
      <div class="filter-section">
        <el-card class="filter-card" shadow="never">
          <div class="filter-header">
            <div class="filter-title">
              <el-icon><Filter /></el-icon>
              筛选条件
              <el-button
                type="text"
                :icon="isFilterExpanded ? 'ArrowUp' : 'ArrowDown'"
                @click="toggleFilter"
                class="filter-toggle-btn"
              >
                {{ isFilterExpanded ? '收起' : '展开' }}
              </el-button>
            </div>
            <div class="filter-actions-top">
              <div class="view-controls">
                <el-radio-group v-model="viewMode" size="default">
                  <el-radio-button value="card">
                    <div class="view-button-content">
                      <el-icon><Grid /></el-icon>
                      <span class="control-text">卡片视图</span>
                    </div>
                  </el-radio-button>
                  <el-radio-button value="list">
                    <div class="view-button-content">
                      <el-icon><List /></el-icon>
                      <span class="control-text">列表视图</span>
                    </div>
                  </el-radio-button>
                </el-radio-group>
              </div>
              <div class="filter-stats">
                <el-tag type="info" effect="light" size="default">
                  <el-icon><DataAnalysis /></el-icon>
                  共 {{ totalCount }} 条，显示 {{ filteredCount }} 条
                </el-tag>
              </div>
            </div>
          </div>

          <div class="filter-content" v-show="isFilterExpanded">
            <div class="filter-row">
              <div class="filter-group">
                <label class="filter-label">关键词搜索</label>
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索订单号、姓名、护照号..."
                  clearable
                  class="filter-input"
                  prefix-icon="Search"
                  @input="handleSearch"
                />
              </div>

              <div class="filter-group">
                <label class="filter-label">签证国家</label>
                <el-select
                  v-model="selectedCountry"
                  placeholder="选择国家"
                  clearable
                  class="filter-select"
                  @change="handleCountryChange"
                >
                  <el-option
                    v-for="mission in vfsStore.config"
                    :key="mission.missionCode"
                    :label="mission.missionCodeName"
                    :value="mission.missionCode"
                  />
                </el-select>
              </div>

              <div class="filter-group">
                <label class="filter-label">签证中心</label>
                <el-select
                  v-model="selectedCenter"
                  placeholder="选择签证中心"
                  clearable
                  class="filter-select"
                  :disabled="!selectedCountry"
                  @change="handleCenterChange"
                >
                  <el-option
                    v-for="center in availableCenters"
                    :key="center.isoCode"
                    :label="center.centerName"
                    :value="center.isoCode"
                  />
                </el-select>
              </div>

              <div class="filter-group">
                <label class="filter-label">订单状态</label>
                <el-select
                  v-model="selectedStatus"
                  placeholder="选择状态"
                  clearable
                  class="filter-select"
                >
                  <el-option label="待处理" value="pending">
                    <el-icon><Clock /></el-icon>
                    待处理
                  </el-option>
                  <el-option label="处理中" value="processing">
                    <el-icon><Loading /></el-icon>
                    处理中
                  </el-option>
                  <el-option label="已完成" value="completed">
                    <el-icon><Check /></el-icon>
                    已完成
                  </el-option>
                </el-select>
              </div>
            </div>

            <div class="filter-row">
              <div class="filter-group">
                <label class="filter-label">创建日期</label>
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="filter-date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="handleDateRangeChange"
                />
              </div>

              <div class="filter-group">
                <label class="filter-label">排序方式</label>
                <div class="sort-controls">
                  <el-select
                    v-model="sortBy"
                    placeholder="排序字段"
                    class="sort-select"
                  >
                    <el-option label="创建时间" value="created_at" />
                    <el-option label="更新时间" value="updated_at" />
                    <el-option label="订单号" value="order_id" />
                    <el-option label="客户姓名" value="client_name" />
                  </el-select>
                  <el-button
                    :icon="sortOrder === 'desc' ? 'SortDown' : 'SortUp'"
                    @click="toggleSortOrder"
                    :type="sortOrder === 'desc' ? 'primary' : 'default'"
                    class="sort-button"
                  />
                </div>
              </div>

              <div class="filter-group filter-actions">
                <el-button
                  type="info"
                  icon="Refresh"
                  @click="resetFilters"
                  class="reset-button"
                >
                  重置筛选
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <!-- 可滚动内容区域 -->
    <div class="scrollable-content">
      <div
        class="content-container"
        :class="{ 'list-mode': viewMode === 'list' }"
      >
        <!-- 空状态显示 -->
        <div v-if="filteredOrders.length === 0" class="empty-state">
          <div class="empty-icon">
            <el-icon><DocumentRemove /></el-icon>
          </div>
          <h3 class="empty-title">
            {{ orders.length === 0 ? '暂无订单数据' : '没有找到匹配的订单' }}
          </h3>
          <p class="empty-description" v-if="orders.length === 0">
            点击右上角"新增客户"按钮创建第一个订单
          </p>
          <p class="empty-description" v-else>尝试调整筛选条件或搜索关键词</p>
          <el-button
            v-if="
              searchKeyword ||
              selectedCountry ||
              selectedCenter ||
              selectedStatus ||
              dateRange
            "
            type="primary"
            size="large"
            @click="resetFilters"
            class="empty-action"
          >
            <el-icon><Refresh /></el-icon>
            清除所有筛选条件
          </el-button>
        </div>

        <!-- 列表模式 -->
        <div
          v-if="viewMode === 'list' && filteredOrders.length > 0"
          class="list-view"
        >
          <el-card class="table-card" shadow="never">
            <el-table
              :data="filteredOrders"
              style="width: 100%"
              :header-cell-style="{
                background: '#fafbfc',
                color: '#2c3e50',
                fontWeight: '600',
                fontSize: '14px',
                padding: '16px 12px',
                borderBottom: '2px solid #e9ecef',
              }"
              :row-style="{ minHeight: '80px' }"
              @row-click="handleRowClick"
              class="modern-table"
              :height="tableHeight"
            >
              <el-table-column
                prop="order_id"
                label="订单号"
                min-width="160"
                width="200"
                sortable
              >
                <template #default="{ row }">
                  <el-link type="primary" @click.stop="editOrder(row)">
                    {{ row.order_id }}
                  </el-link>
                </template>
              </el-table-column>

              <el-table-column label="客户信息" min-width="280" width="320">
                <template #default="{ row }">
                  <div class="clients-container">
                    <div
                      v-for="(client, idx) in row.clients"
                      :key="idx"
                      class="client-card"
                    >
                      <div class="client-avatar-small">
                        {{ (client.name || '?').charAt(0) }}
                      </div>
                      <div class="client-details">
                        <div class="client-name-row">
                          <span class="client-name">{{
                            client.name || '未知'
                          }}</span>
                          <span
                            v-if="getClientPinyin(client)"
                            class="client-pinyin"
                            >（{{ getClientPinyin(client) }}）</span
                          >
                          <el-tag
                            v-if="idx === 0"
                            type="primary"
                            size="small"
                            effect="light"
                          >
                            主申请人
                          </el-tag>
                          <el-tag
                            v-else
                            type="info"
                            size="small"
                            effect="light"
                          >
                            同行人{{ idx }}
                          </el-tag>
                        </div>
                        <div class="client-passport-row">
                          <span class="passport-label">护照:</span>
                          <span class="passport-number">{{
                            client.passport || '无'
                          }}</span>
                          <el-icon
                            v-if="client.passport_image || client.avatar_image"
                            class="passport-icon"
                            @click.stop="showClientImages(client, row)"
                          >
                            <Picture />
                          </el-icon>
                        </div>
                        <div
                          class="client-extra-info"
                          v-if="client.dob || client.passport_expire"
                        >
                          <span v-if="client.dob" class="info-item">
                            <el-icon><Calendar /></el-icon>
                            {{ client.dob }}
                          </span>
                          <span v-if="client.passport_expire" class="info-item">
                            <el-icon><Clock /></el-icon>
                            {{ client.passport_expire }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="签证类型" min-width="300" width="350">
                <template #default="{ row }">
                  <div class="visa-info-container">
                    <div
                      v-for="(type, idx) in row.visaType"
                      :key="idx"
                      class="visa-item"
                    >
                      <div class="visa-country">
                        <el-icon><Location /></el-icon>
                        <span>{{ getVisaCountry(type) }}</span>
                        <span
                          class="country-flag"
                          :class="`flag-${getCountryFlag(
                            getVisaCountry(type)
                          )}`"
                        ></span>
                      </div>
                      <div class="visa-center">
                        <el-icon><OfficeBuilding /></el-icon>
                        <span>{{ getVisaCenter(type) }}</span>
                      </div>
                      <div class="visa-type">
                        <el-tag type="primary" size="small" effect="light">
                          {{ getVisaType(type) }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="期望日期" min-width="200" width="220">
                <template #default="{ row }">
                  <div class="date-info-container">
                    <div class="date-header">
                      <span class="date-label">期望日期</span>
                      <el-button
                        type="text"
                        size="small"
                        @click.stop="editExpectedDate(row)"
                        class="edit-date-btn"
                        title="编辑期望日期"
                      >
                        <el-icon><EditPen /></el-icon>
                      </el-button>
                    </div>
                    <div
                      v-for="(range, idx) in row.dateRangeList"
                      :key="idx"
                      class="date-range-item"
                    >
                      <div class="date-range">
                        <el-icon><Calendar /></el-icon>
                        <span class="date-text">{{ range[0] }}</span>
                        <el-icon class="arrow-icon"><Right /></el-icon>
                        <span class="date-text">{{ range[1] }}</span>
                      </div>
                      <div class="date-duration">
                        {{ calculateDuration(range[0], range[1]) }}
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="创建时间"
                min-width="160"
                width="200"
                sortable
              >
                <template #default="{ row }">
                  <div class="time-info-container">
                    <div class="time-main">
                      <el-icon><Clock /></el-icon>
                      <span>{{ formatDate(row.created_at) }}</span>
                    </div>
                    <div class="time-sub">
                      {{ formatTime(row.created_at) }}
                    </div>
                    <div class="time-relative">
                      {{ getRelativeTime(row.created_at) }}
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="更新时间"
                min-width="160"
                width="200"
                sortable
              >
                <template #default="{ row }">
                  <div
                    v-if="row.updated_at"
                    class="time-info-container clickable-time"
                    @click.stop="showUpdateHistory(row.order_id)"
                  >
                    <div class="time-main">
                      <el-icon><EditPen /></el-icon>
                      <span>{{ formatDate(row.updated_at) }}</span>
                    </div>
                    <div class="time-sub">
                      {{ formatTime(row.updated_at) }}
                    </div>
                    <div class="time-relative">
                      {{ getRelativeTime(row.updated_at) }}
                    </div>
                  </div>
                  <div v-else class="time-info-container">
                    <span class="no-update">未更新</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="状态"
                min-width="120"
                width="140"
                align="center"
              >
                <template #default="{ row }">
                  <div class="status-container">
                    <el-tag
                      :type="getStatusType(row.order_status)"
                      :effect="
                        isErrorStatus(row.order_status) ? 'dark' : 'light'
                      "
                      @click="
                        isErrorStatus(row.order_status)
                          ? showErrorMessage(row)
                          : null
                      "
                      :class="[
                        'status-tag',
                        { clickable: isErrorStatus(row.order_status) },
                      ]"
                    >
                      <el-icon>
                        <component :is="getStatusIcon(row.order_status)" />
                      </el-icon>
                      {{ getStatusDisplay(row.order_status) }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>

              <!-- 操作员列 - 仅管理员可见 -->
              <el-table-column
                v-if="
                  userStore.permission === 'admin' ||
                  userStore.permission === 'kefu'
                "
                label="操作员"
                min-width="100"
                width="120"
              >
                <template #default="{ row }">
                  <div class="operator-info-container">
                    <span class="operator-name">{{
                      row.operator_name || '系统'
                    }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="操作"
                min-width="120"
                width="140"
                fixed="right"
                align="center"
              >
                <template #default="{ row }">
                  <el-dropdown trigger="click" placement="bottom" @click.stop>
                    <el-button type="primary" size="small">
                      操作
                      <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <!-- 编辑订单 -->
                        <el-dropdown-item @click.stop="editOrder(row)">
                          <el-icon><EditPen /></el-icon>
                          编辑订单
                        </el-dropdown-item>

                        <!-- 重新预约 -->
                        <el-dropdown-item
                          v-if="
                            ['registe_error', 'schedule_error'].includes(
                              row.order_status
                            )
                          "
                          @click.stop="rescheduleOrder(row)"
                        >
                          <el-icon><RefreshRight /></el-icon>
                          重新预约
                        </el-dropdown-item>

                        <!-- 立即预约（仅VFS订单且状态允许） -->
                        <el-dropdown-item
                          v-if="
                            row.is_vfs_order &&
                            [
                              'registe_success',
                              'pending',
                              'waitlist_available',
                            ].includes(row.order_status)
                          "
                          @click.stop="openBookNowDialog(row)"
                        >
                          <el-icon><Calendar /></el-icon>
                          立即预约
                        </el-dropdown-item>

                        <!-- 编辑备注 -->
                        <el-dropdown-item @click.stop="openRemarkDialog(row)">
                          <el-icon><EditPen /></el-icon>
                          编辑备注
                        </el-dropdown-item>

                        <!-- 暂停/恢复预约 -->
                        <el-dropdown-item @click.stop="pauseOrder(row)">
                          <el-icon><VideoPause /></el-icon>
                          {{
                            row.order_status === 'pause'
                              ? '恢复预约'
                              : '暂停预约'
                          }}
                        </el-dropdown-item>

                        <!-- 删除订单 -->
                        <el-dropdown-item
                          divided
                          @click.stop="deleteOrder(row.order_id)"
                        >
                          <el-icon><Delete /></el-icon>
                          删除订单
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>

        <!-- 卡片模式 -->
        <div
          v-if="viewMode === 'card' && filteredOrders.length > 0"
          class="card-view"
        >
          <div class="cards-grid">
            <el-card
              v-for="item in filteredOrders"
              :key="item.order_id"
              class="order-card"
              shadow="hover"
            >
              <template #header>
                <div class="card-header">
                  <div class="card-header-left">
                    <div class="order-id">
                      <!-- <span
                        class="order-flag"
                        :class="`flag-${getCountryFlag(
                          getOrderMainCountry(item.visaType)
                        )}`"
                      ></span> -->
                      订单号：{{ item.order_id }}
                    </div>
                    <div class="order-status-badge">
                      <el-tag
                        :type="getStatusType(item.order_status)"
                        :effect="
                          isErrorStatus(item.order_status) ? 'dark' : 'light'
                        "
                        @click="
                          isErrorStatus(item.order_status)
                            ? showErrorMessage(item)
                            : null
                        "
                        :class="[
                          'status-tag-card',
                          { clickable: isErrorStatus(item.order_status) },
                        ]"
                      >
                        <el-icon>
                          <component :is="getStatusIcon(item.order_status)" />
                        </el-icon>
                        {{ getStatusDisplay(item.order_status) }}
                        <span v-if="isErrorStatus(item.order_status)">
                          - 点击查看详情</span
                        >
                      </el-tag>
                    </div>
                    <div class="timestamps">
                      <el-tag type="success" effect="plain" size="small">
                        创建时间：{{ item.created_at }}
                      </el-tag>
                      <el-tag
                        v-if="item.updated_at"
                        type="warning"
                        effect="plain"
                        size="small"
                        class="clickable-tag"
                        @click.stop="showUpdateHistory(item.order_id)"
                      >
                        上次更新：{{ item.updated_at }}
                      </el-tag>
                      <el-tag
                        v-if="
                          userStore.permission === 'admin' ||
                          userStore.permission === 'kefu'
                        "
                        type="info"
                        effect="plain"
                        size="small"
                        class="operator-tag"
                      >
                        操作员：{{ item.operator_name || '系统' }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="card-section card-actions-section">
                    <div class="card-actions">
                      <el-dropdown
                        trigger="click"
                        placement="bottom-end"
                        @command="onCardDropdownCommand(item, $event)"
                      >
                        <el-button type="primary" size="small">
                          操作
                          <el-icon class="el-icon--right"
                            ><ArrowDown
                          /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <!-- 编辑订单 -->
                            <el-dropdown-item @click="editOrder(item)">
                              <el-icon><EditPen /></el-icon>
                              编辑订单
                            </el-dropdown-item>

                            <!-- 重新预约 -->
                            <el-dropdown-item
                              v-if="
                                ['registe_error', 'schedule_error'].includes(
                                  item.order_status
                                )
                              "
                              @click="rescheduleOrder(item)"
                            >
                              <el-icon><RefreshRight /></el-icon>
                              重新预约
                            </el-dropdown-item>

                            <!-- 立即预约（仅VFS订单且状态允许） -->
                            <el-dropdown-item
                              v-if="
                                item.is_vfs_order &&
                                [
                                  'registe_success',
                                  'pending',
                                  'waitlist_available',
                                ].includes(item.order_status)
                              "
                              @click="openBookNowDialog(item)"
                            >
                              <el-icon><Calendar /></el-icon>
                              立即预约
                            </el-dropdown-item>

                            <!-- 分割线 -->
                            <el-dropdown-item divided>
                              <div
                                class="dropdown-toggle-item"
                                @click="toggleVipAccept(item)"
                              >
                                <el-icon><Star /></el-icon>
                                <span>{{
                                  item.accept_vip
                                    ? '关闭VIP接受'
                                    : '开启VIP接受'
                                }}</span>
                                <el-switch
                                  v-model="item.accept_vip"
                                  size="small"
                                  @change="toggleVipAccept(item)"
                                  @click.stop
                                />
                              </div>
                            </el-dropdown-item>

                            <!-- 隔天接受切换 -->
                            <el-dropdown-item>
                              <div
                                class="dropdown-toggle-item"
                                @click="toggleNextDayAccept(item)"
                              >
                                <el-icon><Timer /></el-icon>
                                <span>{{
                                  item.accept_next_day
                                    ? '关闭隔天接受'
                                    : '开启隔天接受'
                                }}</span>
                                <el-switch
                                  v-model="item.accept_next_day"
                                  size="small"
                                  @change="toggleNextDayAccept(item)"
                                  @click.stop
                                />
                              </div>
                            </el-dropdown-item>

                            <el-dropdown-item>
                              <div
                                class="dropdown-toggle-item"
                                @click="toggleAutoSchedule(item)"
                              >
                                <el-icon><Pointer /></el-icon>
                                <span>{{
                                  item.auto_schedule
                                    ? '关闭自动预约'
                                    : '开启自动预约'
                                }}</span>
                                <el-switch
                                  v-model="item.auto_schedule"
                                  size="small"
                                  @change="toggleAutoSchedule(item)"
                                  @click.stop
                                />
                              </div>
                            </el-dropdown-item>

                            <!-- 暂停预约 -->
                            <el-dropdown-item @click="pauseOrder(item)" divided>
                              <el-icon><VideoPause /></el-icon>
                              {{
                                item.order_status === 'pause'
                                  ? '恢复预约'
                                  : '暂停预约'
                              }}
                            </el-dropdown-item>

                            <!-- 编辑备注 -->
                            <el-dropdown-item command="edit-remark">
                              <el-icon><EditPen /></el-icon>
                              编辑备注
                            </el-dropdown-item>

                            <!-- 删除订单 -->
                            <el-dropdown-item
                              @click="deleteOrder(item.order_id)"
                            >
                              <el-icon><Delete /></el-icon>
                              删除订单
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </div>
              </template>
              <div class="card-content">
                <div class="card-section">
                  <div class="section-title">
                    <el-icon><User /></el-icon>
                    申请人信息
                  </div>
                  <div class="clients-list">
                    <div
                      v-for="(client, idx) in item.clients"
                      :key="idx"
                      class="client-item"
                    >
                      <div class="client-avatar">
                        {{ (client.name || '?').charAt(0) }}
                      </div>
                      <div class="client-info">
                        <div class="client-name">
                          {{ client.name || '未知' }}
                          <span
                            v-if="getClientPinyin(client)"
                            class="client-pinyin"
                            >（{{ getClientPinyin(client) }}）</span
                          >
                        </div>
                        <div class="client-details">
                          <span class="client-passport">
                            护照: {{ client.passport || '无' }}
                            <el-icon
                              v-if="
                                client.passport_image || client.avatar_image
                              "
                              class="passport-icon"
                              @click="showClientImages(client, item)"
                            >
                              <Picture />
                            </el-icon>
                          </span>
                          <span class="client-dob"
                            >生日: {{ client.dob || '无' }}</span
                          >
                          <span class="client-expire"
                            >有效期: {{ client.passport_expire || '无' }}</span
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card-section">
                  <div class="section-title">
                    <el-icon><Document /></el-icon>
                    签证类型
                  </div>
                  <div class="visa-types-card">
                    <div
                      v-for="(type, idx) in item.visaType"
                      :key="idx"
                      class="visa-item-card"
                    >
                      <div class="visa-country-card">
                        <span
                          class="country-flag order-flag"
                          :class="`flag-${getCountryFlag(
                            getVisaCountry(type)
                          )}`"
                        ></span>
                        <span class="country-name">{{
                          getVisaCountry(type)
                        }}</span>
                      </div>
                      <div class="visa-details-card">
                        <div class="visa-center-card">
                          <el-icon class="center-icon"
                            ><OfficeBuilding
                          /></el-icon>
                          <span class="center-text">{{
                            getVisaCenter(type)
                          }}</span>
                        </div>
                        <el-tag
                          type="primary"
                          size="small"
                          effect="light"
                          class="visa-type-tag"
                        >
                          {{ getVisaType(type) }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card-section">
                  <div class="section-title">
                    <el-icon><Calendar /></el-icon>
                    期望日期
                    <el-button
                      type="text"
                      size="small"
                      @click.stop="editExpectedDate(item)"
                      class="edit-date-btn-card"
                      title="编辑期望日期"
                    >
                      <el-icon><EditPen /></el-icon>
                    </el-button>
                  </div>
                  <div class="date-ranges">
                    <el-tag
                      v-for="(range, idx) in item.dateRangeList"
                      :key="idx"
                      type="warning"
                      effect="light"
                      size="default"
                      class="date-tag"
                    >
                      {{ range[0] }} 至 {{ range[1] }}
                      <span class="date-duration">
                        ({{ calculateDuration(range[0], range[1]) }})
                      </span>
                    </el-tag>
                  </div>
                </div>

                <div class="card-section">
                  <div class="section-title">
                    <el-icon><ChatLineRound /></el-icon>
                    备注信息
                    <el-button
                      v-if="inlineEditOrderId !== item.order_id"
                      type="text"
                      size="small"
                      class="edit-remark-btn-card"
                      @click.stop="startInlineEdit(item)"
                      title="编辑备注"
                    >
                      <el-icon><EditPen /></el-icon>
                    </el-button>
                    <el-tag
                      v-if="!item.remark && inlineEditOrderId !== item.order_id"
                      type="info"
                      effect="light"
                      size="small"
                    >
                      暂无备注
                    </el-tag>
                  </div>
                  <div
                    v-if="inlineEditOrderId === item.order_id"
                    class="remark-edit-inline"
                  >
                    <el-input
                      type="textarea"
                      v-model="inlineRemarkDraft"
                      :rows="4"
                      maxlength="500"
                      show-word-limit
                      class="remark-textarea"
                    />
                    <div class="inline-actions">
                      <el-button size="small" @click="cancelInlineEdit"
                        >取消</el-button
                      >
                      <el-button
                        size="small"
                        type="primary"
                        :loading="savingRemark"
                        @click="saveRemark(item, inlineRemarkDraft, 'inline')"
                        >保存</el-button
                      >
                    </div>
                  </div>
                  <div
                    v-else
                    class="remark-content"
                    :class="{ empty: !item.remark }"
                  >
                    <span v-if="item.remark">{{ item.remark }}</span>
                    <el-button
                      v-else
                      size="small"
                      type="primary"
                      link
                      @click="startInlineEdit(item)"
                      >添加备注</el-button
                    >
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
    <NewOrder
      v-model:visible="showNewOrder"
      @update:visible="
        val => {
          showNewOrder = val;
          if (!val) {
            loadOrders();
          }
        }
      "
      direction="ltr"
      size="800px"
      :editing-order="editingOrder"
      :visa-options="visaOptions"
    >
    </NewOrder>

    <!-- 客户图片展示弹窗 -->
    <el-dialog
      v-model="clientImagesDialogVisible"
      :draggable="true"
      :title="`${currentClientImages.name} - 客户图片`"
      width="800px"
      class="client-images-dialog"
    >
      <div v-if="clientImagesLoading" class="loading-container">
        <el-icon class="is-loading" :size="30">
          <Loading />
        </el-icon>
        <span>正在加载图片...</span>
      </div>
      <div v-else class="images-container">
        <div class="image-section">
          <div class="image-header">
            <el-icon><CreditCard /></el-icon>
            <span>护照图片</span>
          </div>
          <el-image
            :src="currentClientImages.passport"
            fit="contain"
            class="client-image"
            :preview-src-list="[currentClientImages.passport]"
            preview-teleported
          />
        </div>

        <div class="image-section">
          <div class="image-header">
            <el-icon><Avatar /></el-icon>
            <span>头像图片</span>
            <div class="avatar-actions">
              <el-button
                type="primary"
                size="small"
                @click="triggerAvatarUpload"
                :loading="avatarUploading"
              >
                <el-icon><Upload /></el-icon>
                {{ currentClientImages.avatar ? '更换头像' : '上传头像' }}
              </el-button>
            </div>
          </div>
          <div v-if="currentClientImages.avatar" class="image-wrapper">
            <el-image
              :src="currentClientImages.avatar"
              fit="contain"
              class="client-image"
              :preview-src-list="[currentClientImages.avatar]"
              preview-teleported
            />
          </div>
          <div v-else class="no-avatar-placeholder">
            <el-icon :size="60" class="placeholder-icon">
              <Avatar />
            </el-icon>
            <span class="placeholder-text">暂无头像</span>
            <el-button
              type="primary"
              @click="triggerAvatarUpload"
              :loading="avatarUploading"
            >
              上传头像
            </el-button>
          </div>
        </div>

        <div
          v-if="!currentClientImages.passport && !currentClientImages.avatar"
          class="no-images"
        >
          <el-empty description="暂无图片数据" :image-size="80" />
        </div>
      </div>

      <!-- 隐藏的文件上传输入框 -->
      <input
        ref="fileInputRef"
        type="file"
        accept="image/*"
        style="display: none"
        @change="handleAvatarUpload"
      />
    </el-dialog>

    <!-- 单独护照图片弹窗（向后兼容） -->
    <el-dialog
      v-model="imageDialogVisible"
      :draggable="true"
      title="护照图像"
      width="500px"
    >
      <div v-if="imageDialogLoading" style="text-align: center; padding: 20px">
        加载中...
      </div>
      <el-image
        v-else
        style="width: 100%"
        :src="imageDialogUrl"
        fit="contain"
      />
    </el-dialog>

    <!-- 编辑备注对话框 -->
    <el-dialog
      v-model="remarkDialogVisible"
      title="编辑备注"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-input
        type="textarea"
        v-model="remarkDraft"
        :rows="6"
        maxlength="500"
        show-word-limit
        placeholder="请输入备注（最多500字）"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="remarkDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="savingRemark"
            @click="saveRemark(remarkEditingOrder, remarkDraft, 'dialog')"
            >保存</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 更新历史记录 Dialog -->
    <el-dialog
      v-model="historyDialogVisible"
      title="更新历史记录"
      width="70%"
      :close-on-click-modal="false"
      class="history-dialog"
    >
      <div v-loading="historyLoading" class="history-content">
        <div
          v-if="updateHistory.length === 0 && !historyLoading"
          class="no-history"
        >
          <el-empty description="暂无更新记录" />
        </div>

        <div v-else class="history-timeline">
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in updateHistory"
              :key="index"
              :timestamp="formatDateTime(record.updated_at)"
              placement="top"
              :type="getTimelineType(record.action)"
              :icon="getTimelineIcon(record.action)"
            >
              <el-card class="history-card">
                <div class="history-header">
                  <span class="action-type">{{
                    getActionText(record.action)
                  }}</span>
                  <el-tag
                    v-if="
                      record.action === 'remark_update' ||
                      record.changes?.some?.(c => c.field === '备注')
                    "
                    type="info"
                    effect="dark"
                    size="small"
                    >备注变更</el-tag
                  >
                  <span class="operator"
                    >操作人：{{ record.operator || '系统' }}</span
                  >
                </div>

                <div
                  v-if="record.changes && record.changes.length > 0"
                  class="changes-list"
                >
                  <div class="changes-title">变更内容：</div>
                  <div
                    v-for="(change, changeIndex) in record.changes"
                    :key="changeIndex"
                    class="change-item"
                  >
                    <span class="field-name"
                      >{{ getFieldName(change.field) }}：</span
                    >
                    <span class="old-value">{{
                      change.old_value || '空'
                    }}</span>
                    <span class="arrow">→</span>
                    <span class="new-value">{{
                      change.new_value || '空'
                    }}</span>
                  </div>
                </div>

                <div v-if="record.remark" class="remark">
                  <div class="remark-title">备注：</div>
                  <div class="remark-content">{{ record.remark }}</div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>

    <!-- 期望日期编辑弹窗 -->
    <el-dialog
      v-model="dateEditDialogVisible"
      title="编辑期望日期"
      width="600px"
      :close-on-click-modal="false"
      class="date-edit-dialog"
    >
      <div class="date-edit-content">
        <div class="order-info">
          <span class="order-label">订单编号：</span>
          <span class="order-id">{{ editingDateOrder?.order_id }}</span>
          <span class="client-name">{{
            editingDateOrder?.clients?.[0]?.name
          }}</span>
        </div>

        <div class="date-ranges-section">
          <div class="section-header">
            <h4>期望日期段</h4>
            <div class="header-actions">
              <!-- 快捷日期设置 -->
              <el-dropdown trigger="click" @command="handleQuickDateSet">
                <el-button size="small" type="text">
                  快捷设置
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="next-week"
                      >下周 (7天)</el-dropdown-item
                    >
                    <el-dropdown-item command="next-two-weeks"
                      >未来两周 (14天)</el-dropdown-item
                    >
                    <el-dropdown-item command="next-month"
                      >下个月 (30天)</el-dropdown-item
                    >
                    <el-dropdown-item command="working-days" divided
                      >仅工作日 (未来两周)</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <el-button
                type="primary"
                size="small"
                @click="addDateRange"
                :disabled="
                  editingDateOrder?.visa_type?.some?.(v =>
                    v?.toLowerCase().includes('spain')
                  ) && editingDateRanges.length >= 1
                "
              >
                <el-icon><Plus /></el-icon>
                添加日期段
              </el-button>
            </div>
          </div>

          <div class="date-ranges-list">
            <div
              v-for="(range, index) in editingDateRanges"
              :key="index"
              class="date-range-item-edit"
            >
              <div class="range-header">
                <span class="range-number">日期段 {{ index + 1 }}</span>
                <el-button
                  v-if="editingDateRanges.length > 1"
                  type="text"
                  size="small"
                  @click="removeDateRange(index)"
                  class="remove-btn"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
              <el-date-picker
                v-model="editingDateRanges[index]"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="date-picker-full"
                :disabled-date="
                  time => time.getTime() < Date.now() - 24 * 60 * 60 * 1000
                "
              />
              <div v-if="range && range.length === 2" class="range-preview">
                预览：{{ range[0] }} 至 {{ range[1] }} ({{
                  calculateDuration(range[0], range[1])
                }})
              </div>
            </div>
          </div>

          <div
            v-if="
              editingDateOrder?.visa_type?.some?.(v =>
                v?.toLowerCase().includes('spain')
              )
            "
            class="spain-notice"
          >
            <el-alert
              title="注意：西班牙签证只能设置一个期望日期段"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDateEdit">取消</el-button>
          <el-button type="primary" @click="saveExpectedDate">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 立即预约弹窗 -->
    <el-dialog
      v-model="bookNowDialogVisible"
      title="立即预约"
      width="70%"
      class="book-now-dialog"
      :close-on-click-modal="false"
    >
      <div class="book-now-container" v-loading="loadingDates || submitting">
        <div class="order-brief-card" v-if="bookNowOrder">
          <el-space wrap>
            <el-tag type="info" size="small">
              <el-icon style="margin-right: 4px"><Document /></el-icon>
              订单：{{ bookNowOrder.order_id }}
            </el-tag>
            <el-tooltip
              :content="`${getVisaCountry(
                bookNowOrder.visaType?.[0]
              )} / ${getVisaCenter(bookNowOrder.visaType?.[0])} / ${getVisaType(
                bookNowOrder.visaType?.[0]
              )}`"
              placement="top"
            >
              <el-tag type="primary" size="small" effect="dark">
                <el-icon style="margin-right: 4px"><Location /></el-icon>
                {{ getVisaCountry(bookNowOrder.visaType?.[0]) }} /
                {{ getVisaCenter(bookNowOrder.visaType?.[0]) }} /
                {{ getVisaType(bookNowOrder.visaType?.[0]) }}
              </el-tag>
            </el-tooltip>
          </el-space>
        </div>

        <div class="book-now-content">
          <el-card class="panel-card">
            <template #header>
              <div class="panel-header">
                <span
                  >可预约日期 · {{ formatMonth(calendarMonth) }}（{{
                    availableDates.length
                  }}
                  天）</span
                >
                <el-space class="legend" size="small">
                  <span class="dot dot-available"></span> 可预约
                  <span class="dot dot-selected"></span> 已选
                  <span class="dot dot-disabled"></span> 不可用
                </el-space>
              </div>
            </template>

            <el-calendar v-model="calendarMonth">
              <template #header>
                <div class="cal-header">
                  <el-button size="small" @click="changeMonth(-1)"
                    >上个月</el-button
                  >
                  <span class="cal-title">{{
                    formatMonth(calendarMonth)
                  }}</span>
                  <el-button size="small" @click="changeMonth(1)"
                    >下个月</el-button
                  >
                </div>
              </template>
              <template #date-cell="{ data }">
                <div
                  :class="[
                    'date-cell',
                    availableDateSet.has(data.day) ? 'clickable' : 'disabled',
                    selectedDate === data.day ? 'selected' : '',
                  ]"
                  @click="onCalendarDateClick(data.day)"
                >
                  {{ new Date(data.day).getDate() }}
                </div>
              </template>
            </el-calendar>
          </el-card>

          <el-card class="panel-card">
            <template #header>
              <div class="panel-header">
                <span>选择时间段</span>
                <span class="sub" v-if="selectedDate"
                  >已选：{{ selectedDate }}</span
                >
              </div>
            </template>

            <div v-if="!selectedDate" class="empty-tip">请先选择日期</div>
            <div v-else>
              <div class="slot-actions">
                <el-button
                  size="small"
                  @click="onDateChange(selectedDate)"
                  :loading="loadingSlots"
                  >刷新时段</el-button
                >
                <el-button
                  size="small"
                  text
                  @click="selectedAllocationId = null"
                  >清空选择</el-button
                >
              </div>
              <el-radio-group
                v-model="selectedAllocationId"
                class="slots-grid"
                v-loading="loadingSlots"
              >
                <el-radio-button
                  v-for="slot in timeSlots"
                  :key="slot.allocationId"
                  :label="slot.allocationId"
                >
                  {{ slot.slot || '时间段' }}
                </el-radio-button>
              </el-radio-group>
              <el-empty
                v-if="selectedDate && !loadingSlots && timeSlots.length === 0"
                description="该日暂无可选时间段"
              />
            </div>
          </el-card>
        </div>

        <div class="selection-summary">
          <el-alert
            v-if="!selectedDate"
            type="info"
            title="请先从左侧选择可预约日期"
            show-icon
          />
          <el-alert
            v-else-if="!selectedAllocationId"
            type="warning"
            title="请选择一个时间段"
            show-icon
          />
          <el-tag v-else type="success" effect="dark"
            >已选：{{ selectedDate }} · {{ currentSlotText }}</el-tag
          >
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="bookNowDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :disabled="!selectedAllocationId"
            :loading="submitting"
            @click="scheduleNow"
          >
            立即预约
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 错误消息弹窗 -->
    <el-dialog
      v-model="errorMessageVisible"
      title="错误信息"
      width="500px"
      center
    >
      <div v-loading="loadingErrorMessage" class="error-message-container">
        <div v-if="errorMessageData" class="error-content">
          <div class="error-header">
            <el-icon class="error-icon"><CircleClose /></el-icon>
            <span class="error-title">订单处理失败</span>
          </div>

          <div class="error-details">
            <div class="error-item">
              <label>订单号：</label>
              <span>{{ errorMessageData.order_id }}</span>
            </div>

            <div class="error-item">
              <label>错误类型：</label>
              <el-tag
                :type="
                  errorMessageData.message_type === 'registe_error'
                    ? 'danger'
                    : 'warning'
                "
                effect="light"
                size="small"
              >
                {{
                  errorMessageData.message_type === 'registe_error'
                    ? '注册失败'
                    : '预约失败'
                }}
              </el-tag>
            </div>

            <div class="error-item">
              <label>错误时间：</label>
              <span>{{ formatDateTime(errorMessageData.updated_at) }}</span>
            </div>

            <div class="error-message">
              <label>详细说明：</label>
              <div class="message-content">
                {{ errorMessageData.message }}
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-error-message">
          <el-empty description="无法获取错误信息" />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="errorMessageVisible = false">关闭</el-button>
          <el-button
            v-if="errorMessageData"
            type="warning"
            @click="
              rescheduleOrder({
                order_id: errorMessageData.order_id,
                order_status: errorMessageData.status,
              });
              errorMessageVisible = false;
            "
          >
            重新安排预约
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import http from '@/utils/http';
import { useVfsStore } from '@/stores/useVfsStore';
import { useUserStore } from '@/stores/user';
import NewOrder from '@/components/NewOrder.vue';
import {
  EditPen,
  Calendar,
  Location,
  OfficeBuilding,
  RefreshRight,
  Delete,
  ArrowDown,
  Star,
  Timer,
  Pointer,
  Grid,
  List,
  Search,
  User,
  Plus,
  VideoPause,
  Close,
  Right,
  CreditCard,
  Avatar,
  Upload,
  ChatLineRound,
  Document,
  Clock,
  Loading,
} from '@element-plus/icons-vue';

const calendarMonth = ref(new Date());

const formatMonth = d => {
  try {
    const dt = new Date(d);
    const y = dt.getFullYear();
    const m = String(dt.getMonth() + 1).padStart(2, '0');
    return `${y}-${m}`;
  } catch {
    return '';
  }
};

const fetchAvailableDatesForMonth = async (month, reuse = false) => {
  try {
    loadingDates.value = true;
    const url = `/api/vfs/orders/${bookNowOrder.value.order_id}/available-dates`;
    const params =
      reuse && sessionId.value
        ? { month, session_id: sessionId.value }
        : { month };
    const res = await http.get(url, { params });
    if (res?.code === 1) {
      availableDates.value = res.data?.dates || [];
      sessionId.value = res.data?.session_id || sessionId.value || '';
    } else {
      if ((res?.message || '').includes('会话已过期')) {
        // 不复用会话，重新拉
        const res2 = await http.get(url, { params: { month } });
        if (res2?.code === 1) {
          availableDates.value = res2.data?.dates || [];
          sessionId.value = res2.data?.session_id || '';
        } else {
          ElMessage.error(res2?.message || '获取可预约日期失败');
        }
      } else {
        ElMessage.error(res?.message || '获取可预约日期失败');
      }
    }
  } catch (e) {
    console.error(e);
    ElMessage.error('获取可预约日期失败');
  } finally {
    loadingDates.value = false;
  }
};

const changeMonth = async delta => {
  const dt = new Date(calendarMonth.value);
  dt.setMonth(dt.getMonth() + delta);
  calendarMonth.value = dt;
  selectedDate.value = null;
  timeSlots.value = [];
  await fetchAvailableDatesForMonth(formatMonth(dt), true);
};

const onCalendarDateClick = async dayStr => {
  if (!availableDateSet.value?.has?.(dayStr)) return;
  selectedDate.value = dayStr;
  await onDateChange(dayStr);
};

const router = useRouter();
const bookNowDialogVisible = ref(false);
const bookNowOrder = ref(null);
const sessionId = ref('');
const availableDates = ref([]);
const selectedDate = ref(null);
const timeSlots = ref([]);

const formatYmd = d => {
  try {
    const dt = new Date(d);
    const y = dt.getFullYear();
    const m = String(dt.getMonth() + 1).padStart(2, '0');
    const dd = String(dt.getDate()).padStart(2, '0');
    return `${y}-${m}-${dd}`;
  } catch {
    return '';
  }
};

const selectedAllocationId = ref(null);

// 处理卡片 Actions 下拉命令（顶层定义供模板绑定）
const onCardDropdownCommand = (order, cmd) => {
  if (cmd === 'edit-remark') {
    openRemarkDialog(order);
  }
};

const loadingDates = ref(false);
const loadingSlots = ref(false);
const submitting = ref(false);

const currentSlotText = computed(() => {
  const s = timeSlots.value.find(
    it => it.allocationId === selectedAllocationId.value
  );
  return s?.slot || '';
});

const availableDateSet = computed(() => new Set(availableDates.value || []));

const openBookNowDialog = async order => {
  bookNowOrder.value = order;
  sessionId.value = '';
  availableDates.value = [];
  selectedDate.value = null;
  timeSlots.value = [];
  selectedAllocationId.value = null;
  bookNowDialogVisible.value = true;

  try {
    loadingDates.value = true;
    calendarMonth.value = new Date();
    await fetchAvailableDatesForMonth(formatMonth(calendarMonth.value), false);
  } catch (e) {
    console.error(e);
    ElMessage.error('获取可预约日期失败');
  } finally {
    loadingDates.value = false;
  }
};

const onDateChange = async date => {
  selectedAllocationId.value = null;
  timeSlots.value = [];
  if (!date || !sessionId.value) return;
  try {
    loadingSlots.value = true;
    const res = await http.get(
      `/api/vfs/orders/${bookNowOrder.value.order_id}/timeslots`,
      {
        params: { date, session_id: sessionId.value },
      }
    );
    if (res?.code === 1) {
      timeSlots.value = res.data?.slots || [];
    } else {
      if ((res?.message || '').includes('会话已过期')) {
        // 自动重拉日期
        await openBookNowDialog(bookNowOrder.value);
        ElMessage.warning('会话已过期，请重新选择日期');
      } else {
        ElMessage.error(res?.message || '获取时间段失败');
      }
    }
  } catch (e) {
    console.error(e);
    ElMessage.error('获取时间段失败');
  } finally {
    loadingSlots.value = false;
  }
};

const isDateWithinRanges = (dateStr, ranges) => {
  if (!Array.isArray(ranges) || ranges.length === 0) return true;
  try {
    const d = new Date(dateStr);
    const yyyy = d.getFullYear();
    const mm = String(d.getMonth() + 1).padStart(2, '0');
    const dd = String(d.getDate()).padStart(2, '0');
    const norm = `${yyyy}-${mm}-${dd}`;
    return ranges.some(
      r => Array.isArray(r) && r.length === 2 && norm >= r[0] && norm <= r[1]
    );
  } catch (_) {
    return true;
  }
};

const scheduleNow = async () => {
  if (!bookNowOrder.value || !selectedDate.value || !selectedAllocationId.value)
    return;

  // 期望日期提醒但允许继续
  const ranges = bookNowOrder.value?.dateRangeList || [];
  if (!isDateWithinRanges(selectedDate.value, ranges)) {
    try {
      await ElMessageBox.confirm(
        '所选日期不在期望日期段内，是否继续？',
        '提示',
        {
          type: 'warning',
          confirmButtonText: '继续',
          cancelButtonText: '取消',
        }
      );
    } catch {
      return; // 用户取消
    }
  }

  try {
    submitting.value = true;
    const res = await http.post(
      `/api/vfs/orders/${bookNowOrder.value.order_id}/schedule`,
      {
        allocationId: selectedAllocationId.value,
        date: selectedDate.value,
        session_id: sessionId.value,
      }
    );
    if (res?.code === 1) {
      ElMessage.success(res?.message || '预约成功，已进入待支付');
      bookNowDialogVisible.value = false;
      await loadOrders();
      try {
        await ElMessageBox.confirm('是否前往“待支付订单”完成支付？', '提示', {
          type: 'info',
          confirmButtonText: '前往',
          cancelButtonText: '留在本页',
        });
        // 跳转到待支付订单
        await router.push('/dashboard/waitpay');
      } catch {
        // 用户选择留在本页
      }
    } else {
      if ((res?.message || '').includes('会话已过期')) {
        await openBookNowDialog(bookNowOrder.value);
        ElMessage.warning('会话已过期，请重新选择日期');
      } else {
        ElMessage.error(res?.message || '预约失败');
      }
    }
  } catch (e) {
    console.error(e);
    ElMessage.error('预约失败');
  } finally {
    submitting.value = false;
  }
};

const emit = defineEmits(['update:visible']);

const vfsStore = useVfsStore();
const userStore = useUserStore();
const orders = ref([]);
const showNewOrder = ref(false);
const visaOptions = ref([]);

// 搜索与筛选相关
const searchKeyword = ref('');
const selectedCountry = ref(null);
const selectedCenter = ref(null);
const selectedStatus = ref(null);
const dateRange = ref(null);
const sortBy = ref('updated_at');
const sortOrder = ref('desc'); // 'asc' 或 'desc'
const viewMode = ref('card'); // 'card' 或 'list'

// 筛选区域展开/收起控制
const isFilterExpanded = ref(false);

// 表格高度计算
const tableHeight = computed(() => {
  // 根据筛选区域是否展开来动态计算表格高度
  const baseHeight = window.innerHeight || 800;
  const headerHeight = 120; // 页面头部
  const filterHeaderHeight = 80; // 筛选区域头部
  const filterContentHeight = isFilterExpanded.value ? 200 : 0; // 筛选内容
  const padding = 100; // 其他边距

  const availableHeight =
    baseHeight -
    headerHeight -
    filterHeaderHeight -
    filterContentHeight -
    padding;
  return Math.max(300, availableHeight); // 最小高度300px
});

// 防抖搜索
let searchTimeout = null;

// 错误消息弹窗
const errorMessageVisible = ref(false);
const errorMessageData = ref(null);
const loadingErrorMessage = ref(false);

// 计算可用的签证中心
const availableCenters = computed(() => {
  if (!selectedCountry.value) return [];
  const mission = vfsStore.config.find(
    m => m.missionCode === selectedCountry.value
  );
  return mission?.children || [];
});

// 统计信息
const totalCount = computed(() => orders.value.length);
const filteredCount = computed(() => filteredOrders.value.length);

const filteredOrders = computed(() => {
  let filtered = orders.value.filter(order => {
    // 关键词搜索
    const keyword = searchKeyword.value.trim().toLowerCase();
    let matchKeyword = true;
    if (keyword) {
      const matchId = order.order_id
        ?.toString()
        .toLowerCase()
        .includes(keyword);
      const matchClient = order.clients?.some(
        c =>
          (c.name || '').toLowerCase().includes(keyword) ||
          (c.passport || '').toLowerCase().includes(keyword)
      );
      const matchCustomer = (order.customer || '')
        .toLowerCase()
        .includes(keyword);
      const matchRemark = (order.remark || '').toLowerCase().includes(keyword);
      matchKeyword = matchId || matchClient || matchCustomer || matchRemark;
    }

    // 国家筛选
    const matchCountry =
      !selectedCountry.value ||
      order.visaType?.some(visa => visa[0] === selectedCountry.value);

    // 签证中心筛选
    const matchCenter =
      !selectedCenter.value ||
      order.visaType?.some(visa => visa[1] === selectedCenter.value);

    // 状态筛选（这里假设有status字段，如果没有可以根据实际情况调整）
    const matchStatus =
      !selectedStatus.value || order.status === selectedStatus.value;

    // 日期范围筛选
    let matchDateRange = true;
    if (dateRange.value && dateRange.value.length === 2) {
      const [startDate, endDate] = dateRange.value;
      const orderDate = order.created_at
        ? new Date(order.created_at).toISOString().split('T')[0]
        : null;
      if (orderDate) {
        matchDateRange = orderDate >= startDate && orderDate <= endDate;
      }
    }

    return (
      matchKeyword &&
      matchCountry &&
      matchCenter &&
      matchStatus &&
      matchDateRange
    );
  });

  // 排序
  filtered.sort((a, b) => {
    let aValue, bValue;

    switch (sortBy.value) {
      case 'created_at':
      case 'updated_at':
        aValue = new Date(a[sortBy.value] || 0).getTime();
        bValue = new Date(b[sortBy.value] || 0).getTime();
        break;
      case 'order_id':
        aValue = a.order_id || '';
        bValue = b.order_id || '';
        break;
      case 'client_name':
        aValue = a.clients?.[0]?.name || '';
        bValue = b.clients?.[0]?.name || '';
        break;
      default:
        aValue = a[sortBy.value] || '';
        bValue = b[sortBy.value] || '';
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortOrder.value === 'desc'
        ? bValue.localeCompare(aValue)
        : aValue.localeCompare(bValue);
    } else {
      return sortOrder.value === 'desc' ? bValue - aValue : aValue - bValue;
    }
  });

  return filtered;
});

const imageDialogVisible = ref(false);
const imageDialogLoading = ref(false);
const imageDialogUrl = ref('');

const editingOrder = ref(null);

// 期望日期编辑相关
const dateEditDialogVisible = ref(false);
const editingDateOrder = ref(null);
const editingDateRanges = ref([]);

// 客户图片展示相关
const clientImagesDialogVisible = ref(false);
const clientImagesLoading = ref(false);
const currentClientImages = ref({
  passport: null,
  avatar: null,
  name: '',
  clientInfo: null,
});

// 头像上传相关
const avatarUploading = ref(false);
const fileInputRef = ref(null);

// 历史记录相关
const historyDialogVisible = ref(false);
const currentOrderId = ref('');
const updateHistory = ref([]);
const historyLoading = ref(false);

// 备注编辑相关
const remarkDialogVisible = ref(false);
const remarkEditingOrder = ref(null);
const remarkDraft = ref('');
const savingRemark = ref(false);
const inlineEditOrderId = ref(null);
const inlineRemarkDraft = ref('');

const openRemarkDialog = order => {
  remarkEditingOrder.value = order;
  remarkDraft.value = order?.remark || '';
  remarkDialogVisible.value = true;
};

const cancelInlineEdit = () => {
  inlineEditOrderId.value = null;
  inlineRemarkDraft.value = '';
};

const startInlineEdit = order => {
  inlineEditOrderId.value = order.order_id;
  inlineRemarkDraft.value = order.remark || '';
};

const saveRemark = async (targetOrder, draft, source = 'dialog') => {
  const text = (draft ?? '').trim();
  if (text.length > 500) {
    ElMessage.warning('备注不能超过500字');
    return;
  }
  try {
    savingRemark.value = true;
    const res = await http.post('/api/update_order_remark', {
      order_id: targetOrder.order_id,
      remark: text,
    });
    if (res?.code === 1) {
      // 本地更新 orders 列表
      const idx = orders.value.findIndex(
        o => o.order_id === targetOrder.order_id
      );
      if (idx !== -1) {
        orders.value[idx].remark = text;
      }
      // 关闭对应编辑UI
      if (source === 'dialog') {
        remarkDialogVisible.value = false;
      } else {
        cancelInlineEdit();
      }
      ElMessage.success('备注已更新');
    } else {
      ElMessage.error(res?.message || '更新失败');
    }
  } catch (e) {
    console.error('更新备注失败:', e);
    ElMessage.error('更新失败');
  } finally {
    savingRemark.value = false;
  }
};

// 显示更新历史记录
const showUpdateHistory = async orderId => {
  currentOrderId.value = orderId;
  historyDialogVisible.value = true;
  historyLoading.value = true;

  // 处理卡片 Actions 下拉命令
  const onCardDropdownCommand = (order, cmd) => {
    if (cmd === 'edit-remark') {
      openRemarkDialog(order);
    }
  };

  try {
    // 调用后端接口获取历史记录
    const res = await http.get(`/api/order-history/${orderId}`);
    if (res.code === 1) {
      updateHistory.value = res.data || [];
    } else {
      ElMessage.error(res.message || '获取历史记录失败');
      updateHistory.value = [];
    }
  } catch (error) {
    console.error('获取历史记录失败:', error);
    ElMessage.error('获取历史记录失败');
    updateHistory.value = [];
  } finally {
    historyLoading.value = false;
  }
};

// 处理函数
const handleSearch = () => {
  // 搜索已经通过computed自动处理，这里可以添加额外逻辑
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
  searchTimeout = setTimeout(() => {
    // 可以在这里添加搜索统计或其他逻辑
    console.log('搜索关键词:', searchKeyword.value);
  }, 300);
};

const handleCountryChange = () => {
  // 当国家改变时，清空签证中心选择
  selectedCenter.value = null;
};

const handleCenterChange = () => {
  // 签证中心改变的处理逻辑
  console.log('选择的签证中心:', selectedCenter.value);
};

const handleDateRangeChange = () => {
  // 日期范围改变的处理逻辑
  console.log('日期范围:', dateRange.value);
};

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc';
};

const resetFilters = () => {
  searchKeyword.value = '';
  selectedCountry.value = null;
  selectedCenter.value = null;
  selectedStatus.value = null;
  dateRange.value = null;
  sortBy.value = 'updated_at';
  sortOrder.value = 'desc';
};

// 手动切换筛选区域展开/收起
const toggleFilter = () => {
  isFilterExpanded.value = !isFilterExpanded.value;
};

// 列表模式相关方法
const handleRowClick = row => {
  // 点击行时的处理，可以用于快速预览或其他操作
  console.log('点击行:', row.order_id);
};

// 格式化日期时间的辅助函数
const formatDate = dateTime => {
  if (!dateTime) return '-';
  try {
    const date = new Date(dateTime);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  } catch (error) {
    return dateTime;
  }
};

const formatTime = dateTime => {
  if (!dateTime) return '-';
  try {
    const date = new Date(dateTime);
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (error) {
    return dateTime;
  }
};

const getRelativeTime = dateTime => {
  if (!dateTime) return '';
  try {
    const date = new Date(dateTime);
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return '今天';
    if (diffDays === 1) return '昨天';
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
    return `${Math.floor(diffDays / 30)}月前`;
  } catch (error) {
    return '';
  }
};

const calculateDuration = (startDate, endDate) => {
  if (!startDate || !endDate) return '';
  try {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffMs = end - start;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24)) + 1;
    return `${diffDays}天`;
  } catch (error) {
    return '';
  }
};

// 获取签证国家名称
const getVisaCountry = typeArr => {
  if (!Array.isArray(typeArr) || typeArr.length === 0) return '未知';
  const [missionCode] = typeArr;
  const mission = vfsStore.config.find(m => m.missionCode === missionCode);
  return mission?.missionCodeName || missionCode || '未知';
};

// 获取签证中心名称
const getVisaCenter = typeArr => {
  if (!Array.isArray(typeArr) || typeArr.length < 2) return '未知';
  const [missionCode, centerCode] = typeArr;
  const mission = vfsStore.config.find(m => m.missionCode === missionCode);
  const center = mission?.children?.find(c => c.isoCode === centerCode);
  return center?.centerName || centerCode || '未知';
};

// 获取签证类型名称
const getVisaType = typeArr => {
  if (!Array.isArray(typeArr) || typeArr.length < 4) return '未知';
  const [missionCode, centerCode, visaCategoryCode, visaCode] = typeArr;

  const mission = vfsStore.config.find(m => m.missionCode === missionCode);
  if (!mission) return visaCode || '未知';

  const center = mission.children?.find(c => c.isoCode === centerCode);
  if (!center) return visaCode || '未知';

  const visaCategory = center.children?.find(
    vc => vc.code === visaCategoryCode
  );
  if (!visaCategory) return visaCode || '未知';

  const visa = visaCategory.children?.find(v => v.code === visaCode);
  return visa?.name || visaCode || '未知';
};

// 获取国旗 CSS 类名
const getCountryFlag = countryName => {
  const flagMap = {
    西班牙: 'es',
    法国: 'fr',
    德国: 'de',
    意大利: 'it',
    荷兰: 'nl',
    比利时: 'be',
    奥地利: 'at',
    瑞士: 'ch',
    葡萄牙: 'pt',
    希腊: 'gr',
    丹麦: 'dk',
    瑞典: 'se',
    挪威: 'no',
    芬兰: 'fi',
    冰岛: 'is',
    波兰: 'pl',
    捷克: 'cz',
    匈牙利: 'hu',
    斯洛伐克: 'sk',
    斯洛文尼亚: 'si',
    克罗地亚: 'hr',
    保加利亚: 'bg',
    罗马尼亚: 'ro',
    爱沙尼亚: 'ee',
    拉脱维亚: 'lv',
    立陶宛: 'lt',
    马耳他: 'mt',
    塞浦路斯: 'cy',
    美国: 'us',
    加拿大: 'ca',
    英国: 'gb',
    澳大利亚: 'au',
    新西兰: 'nz',
    日本: 'jp',
    韩国: 'kr',
    新加坡: 'sg',
    马来西亚: 'my',
    泰国: 'th',
    印度: 'in',
    俄罗斯: 'ru',
    土耳其: 'tr',
    巴西: 'br',
    阿根廷: 'ar',
    智利: 'cl',
    墨西哥: 'mx',
    南非: 'za',
    埃及: 'eg',
    以色列: 'il',
    阿联酋: 'ae',
    沙特阿拉伯: 'sa',
  };

  return flagMap[countryName] || 'unknown';
};

// 获取订单的主要国家（用于卡片模式显示国旗）
const getOrderMainCountry = visaTypes => {
  if (!Array.isArray(visaTypes) || visaTypes.length === 0) return '未知';
  // 取第一个签证类型的国家作为主要国家
  const firstVisaType = visaTypes[0];
  return getVisaCountry(firstVisaType);
};

// 构建签证选项数据
const buildVisaOptions = () => {
  if (!vfsStore.config || !Array.isArray(vfsStore.config)) {
    // 如果没有配置数据，返回测试数据
    return [
      {
        code: 'spain',
        name: '西班牙',
        children: [
          {
            code: 'SHANGHAI',
            name: '上海',
            children: [
              {
                code: 'schengen',
                name: '申根签证',
                children: [
                  { code: 'tourism', name: '旅游签证' },
                  { code: 'business', name: '商务签证' },
                ],
              },
            ],
          },
        ],
      },
    ];
  }

  return vfsStore.config.map(mission => ({
    code: mission.missionCode,
    name: mission.missionCodeName,
    children:
      mission.children?.map(center => ({
        code: center.isoCode,
        name: center.centerName,
        children:
          center.children?.map(category => ({
            code: category.code,
            name: category.name,
            children:
              category.children?.map(visa => ({
                code: visa.code,
                name: visa.name,
              })) || [],
          })) || [],
      })) || [],
  }));
};

const loadOrders = async () => {
  const res = await http.get('/api/get_pending_orders');
  if (res?.code === 1) {
    orders.value = res.data || [];
  }
};

const deleteOrder = async order_id => {
  ElMessageBox.confirm('确认删除该订单吗？删除后将无法恢复。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res = await http.post('/api/delete_order', { order_id });
      if (res?.code === 1) {
        ElMessage.success('删除成功');
        loadOrders();
      } else {
        ElMessage.error(res.message || '删除失败');
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 切换VIP接受状态
const toggleVipAccept = async order => {
  try {
    // 当从@change事件调用时，v-model已经更新了值，直接使用当前值
    const currentVipStatus = order.accept_vip;
    const res = await http.post('/api/toggle_vip_accept', {
      order_id: order.order_id,
      accept_vip: currentVipStatus,
    });

    if (res?.code === 1) {
      // 值已经通过v-model更新，无需再次设置
      ElMessage.success(currentVipStatus ? '已开启VIP接受' : '已关闭VIP接受');
    } else {
      // 如果后端操作失败，恢复原来的值
      order.accept_vip = !currentVipStatus;
      ElMessage.error(res.message || '操作失败');
    }
  } catch (error) {
    // 如果请求失败，恢复原来的值
    order.accept_vip = !order.accept_vip;
    console.error('切换VIP状态失败:', error);
    ElMessage.error('操作失败');
  }
};

// 切换隔天接受状态
const toggleNextDayAccept = async order => {
  try {
    // 当从@change事件调用时，v-model已经更新了值，直接使用当前值
    const currentNextDayStatus = order.accept_next_day;
    const res = await http.post('/api/toggle_next_day_accept', {
      order_id: order.order_id,
      accept_next_day: currentNextDayStatus,
    });

    if (res?.code === 1) {
      // 值已经通过v-model更新，无需再次设置
      ElMessage.success(
        currentNextDayStatus ? '已开启隔天接受' : '已关闭隔天接受'
      );
    } else {
      // 如果后端操作失败，恢复原来的值
      order.accept_next_day = !currentNextDayStatus;
      ElMessage.error(res.message || '操作失败');
    }
  } catch (error) {
    // 如果请求失败，恢复原来的值
    order.accept_next_day = !order.accept_next_day;
    console.error('切换隔天状态失败:', error);
    ElMessage.error('操作失败');
  }
};

const toggleAutoSchedule = async order => {
  try {
    // 当从@change事件调用时，v-model已经更新了值，直接使用当前值
    const currentAutoSchedule = order.auto_schedule;
    const res = await http.post('/api/toggle_auto_schedule', {
      order_id: order.order_id,
      auto_schedule: currentAutoSchedule,
    });

    if (res?.code === 1) {
      // 值已经通过v-model更新，无需再次设置
      ElMessage.success(
        currentAutoSchedule ? '已开启自动预约' : '已关闭自动预约'
      );
    } else {
      // 如果后端操作失败，恢复原来的值
      order.auto_schedule = !currentAutoSchedule;
      ElMessage.error(res.message || '操作失败');
    }
  } catch (error) {
    // 如果请求失败，恢复原来的值
    order.auto_schedule = !order.auto_schedule;
    console.error('切换自动预约状态失败:', error);
    ElMessage.error('操作失败');
  }
};

// 监听 vfsStore.config 变化，更新签证选项
watch(
  () => vfsStore.config,
  () => {
    visaOptions.value = buildVisaOptions();
  },
  { immediate: true, deep: true }
);

// 暂停/恢复预约
const pauseOrder = async order => {
  try {
    const isPaused = order.order_status === 'pause';

    // 只有注册成功的订单才可以暂停预约
    if (
      order.order_status !== 'registe_success' &&
      order.order_status !== 'pause'
    ) {
      ElMessage.warning('只有注册成功的订单才可以暂停预约');
      return;
    }

    const confirmMessage = isPaused
      ? `确认恢复订单 ${order.order_id} 的预约吗？订单将重新进入预约队列。`
      : `确认暂停订单 ${order.order_id} 的预约吗？暂停期间将不会自动预约。`;

    await ElMessageBox.confirm(
      confirmMessage,
      isPaused ? '恢复预约' : '暂停预约',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 调用API更新订单状态
    const newStatus = isPaused ? 'registe_success' : 'pause';
    const res = await http.post('/api/update_order_status', {
      order_id: order.order_id,
      order_status: newStatus,
    });

    if (res?.code === 1) {
      ElMessage.success(isPaused ? '已恢复预约' : '已暂停预约');
      // 重新加载订单列表
      await loadOrders();
    } else {
      ElMessage.error(res?.message || '操作失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('暂停/恢复预约失败:', error);
      ElMessage.error('操作失败');
    }
  }
};

onMounted(async () => {
  loadOrders();

  // 确保 VFS 配置已加载
  if (!vfsStore.config || vfsStore.config.length === 0) {
    try {
      const resVfsConfig = await http.post('/get_vfs_config');
      vfsStore.setConfig(resVfsConfig.data);
    } catch (error) {
      console.error('加载 VFS 配置失败:', error);
    }
  }

  // 构建签证选项数据
  visaOptions.value = buildVisaOptions();
});

const showClientImages = async (client, order) => {
  try {
    clientImagesDialogVisible.value = true;
    clientImagesLoading.value = true;

    // 重置图片数据，将order_id添加到clientInfo中
    currentClientImages.value = {
      passport: null,
      avatar: null,
      name: client.name || '未知客户',
      clientInfo: {
        ...client,
        order_id: order.order_id,
      },
    };

    // 同时加载护照图片和头像图片
    const promises = [];

    if (client.passport_image) {
      promises.push(
        http
          .get(`/api/passport_image/${client.passport_image}`, {
            responseType: 'blob',
          })
          .then(response => {
            const blob = new Blob([response], { type: 'image/jpeg' });
            const imageUrl = URL.createObjectURL(blob);
            currentClientImages.value.passport = imageUrl;
          })
          .catch(error => {
            console.error('无法获取护照图像', error);
          })
      );
    }

    if (client.avatar_image) {
      promises.push(
        http
          .get(`/api/avatar_image/${client.avatar_image}`, {
            responseType: 'blob',
          })
          .then(response => {
            const blob = new Blob([response], { type: 'image/jpeg' });
            const imageUrl = URL.createObjectURL(blob);
            currentClientImages.value.avatar = imageUrl;
          })
          .catch(error => {
            console.error('无法获取头像图像', error);
          })
      );
    }

    // 等待所有图片加载完成
    await Promise.allSettled(promises);
    clientImagesLoading.value = false;
  } catch (error) {
    console.error('无法获取客户图像', error);
    clientImagesLoading.value = false;
  }
};

// 触发头像文件选择
const triggerAvatarUpload = () => {
  fileInputRef.value?.click();
};

// 处理头像文件上传
const handleAvatarUpload = async event => {
  const file = event.target.files?.[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件');
    return;
  }

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB');
    return;
  }

  try {
    avatarUploading.value = true;

    // 将文件转换为base64
    const base64 = await fileToBase64(file);

    // 调用后端接口更新头像
    const response = await http.post('/api/update_client_avatar', {
      order_id: currentClientImages.value.clientInfo.order_id,
      passport: currentClientImages.value.clientInfo.passport,
      avatar_image: base64,
    });

    if (response.code === 1) {
      // 更新本地显示的头像
      const newAvatarUrl = URL.createObjectURL(file);
      currentClientImages.value.avatar = newAvatarUrl;

      // 更新页面中的客户数据
      await refreshOrderData();

      ElMessage.success('头像更新成功');
    } else {
      ElMessage.error(response.message || '头像更新失败');
    }
  } catch (error) {
    console.error('上传头像失败:', error);
    ElMessage.error('上传头像失败，请重试');
  } finally {
    avatarUploading.value = false;
    // 清空input
    if (event.target) {
      event.target.value = '';
    }
  }
};

// 获取客户拼音
const getClientPinyin = client => {
  if (!client) return '';
  const parts = [client.surname_pinyin, client.firstname_pinyin].filter(
    Boolean
  );
  return parts.join(' ').trim();
};

// 文件转base64工具函数
const fileToBase64 = file => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = reader.result.split(',')[1]; // 移除data:image/xxx;base64,前缀
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

// 刷新订单数据
const refreshOrderData = async () => {
  try {
    await loadOrders();
  } catch (error) {
    console.error('刷新订单数据失败:', error);
  }
};

// 保留原有的单独显示护照图片功能（向后兼容）
const showPassportImage = async passport_image => {
  try {
    imageDialogVisible.value = true;
    imageDialogLoading.value = true;
    imageDialogUrl.value = '';

    const response = await http.get(`/api/passport_image/${passport_image}`, {
      responseType: 'blob',
    });
    const blob = new Blob([response], { type: 'image/jpeg' });
    const imageUrl = URL.createObjectURL(blob);
    imageDialogUrl.value = imageUrl;
    imageDialogLoading.value = false;
  } catch (error) {
    console.error('无法获取护照图像', error);
    imageDialogLoading.value = false;
  }
};

const openNewOrder = () => {
  editingOrder.value = null;
  showNewOrder.value = true;
};

const editOrder = order => {
  editingOrder.value = JSON.parse(JSON.stringify(order)); // 深拷贝
  showNewOrder.value = true;
};

// 编辑期望日期
const editExpectedDate = order => {
  editingDateOrder.value = order;
  // 深拷贝日期范围数据，如果没有则初始化空数组
  editingDateRanges.value = order.dateRangeList
    ? JSON.parse(JSON.stringify(order.dateRangeList))
    : [[]];
  dateEditDialogVisible.value = true;
};

// 添加日期范围
const addDateRange = () => {
  // 检查是否为西班牙签证（简化判断）
  const isSpainVisa = editingDateOrder.value?.visa_type?.some?.(visa =>
    visa?.toLowerCase().includes('spain')
  );

  if (!isSpainVisa || editingDateRanges.value.length < 1) {
    editingDateRanges.value.push([]);
  }
};

// 删除日期范围
const removeDateRange = index => {
  if (editingDateRanges.value.length > 1) {
    editingDateRanges.value.splice(index, 1);
  }
};

// 保存期望日期
const saveExpectedDate = async () => {
  // 验证数据完整性
  if (
    !editingDateRanges.value.length ||
    editingDateRanges.value.some(range => !range || range.length !== 2)
  ) {
    ElMessage.warning('请填写完整的期望日期段');
    return;
  }

  try {
    // 参考 toggle_next_day_accept 的实现方式，使用POST请求并只传递需要的参数
    const response = await http.post('/api/update_expected_dates', {
      order_id: editingDateOrder.value.order_id,
      dateRangeList: editingDateRanges.value,
    });

    if (response.code === 1) {
      // 更新本地数据
      const orderIndex = orders.value.findIndex(
        o => o.order_id === editingDateOrder.value.order_id
      );
      if (orderIndex !== -1) {
        orders.value[orderIndex].dateRangeList = [...editingDateRanges.value];
      }

      ElMessage.success('期望日期更新成功');
      dateEditDialogVisible.value = false;
    } else {
      ElMessage.error(response.message || '更新失败');
    }
  } catch (error) {
    console.error('更新期望日期失败:', error);
    ElMessage.error('更新失败，请重试');
  }
};

// 快捷日期设置
const handleQuickDateSet = command => {
  const today = new Date();
  const formatDate = date => {
    return date.toISOString().split('T')[0];
  };

  let ranges = [];

  switch (command) {
    case 'next-week':
      const nextWeekStart = new Date(today);
      nextWeekStart.setDate(today.getDate() + 1);
      const nextWeekEnd = new Date(today);
      nextWeekEnd.setDate(today.getDate() + 7);
      ranges = [[formatDate(nextWeekStart), formatDate(nextWeekEnd)]];
      break;

    case 'next-two-weeks':
      const twoWeeksStart = new Date(today);
      twoWeeksStart.setDate(today.getDate() + 1);
      const twoWeeksEnd = new Date(today);
      twoWeeksEnd.setDate(today.getDate() + 14);
      ranges = [[formatDate(twoWeeksStart), formatDate(twoWeeksEnd)]];
      break;

    case 'next-month':
      const monthStart = new Date(today);
      monthStart.setDate(today.getDate() + 1);
      const monthEnd = new Date(today);
      monthEnd.setDate(today.getDate() + 30);
      ranges = [[formatDate(monthStart), formatDate(monthEnd)]];
      break;

    case 'working-days':
      // 生成未来两周的工作日日期段
      const workStart = new Date(today);
      workStart.setDate(today.getDate() + 1);
      const workEnd = new Date(today);
      workEnd.setDate(today.getDate() + 14);

      // 找到第一个和最后一个工作日
      while (workStart.getDay() === 0 || workStart.getDay() === 6) {
        workStart.setDate(workStart.getDate() + 1);
      }
      while (workEnd.getDay() === 0 || workEnd.getDay() === 6) {
        workEnd.setDate(workEnd.getDate() - 1);
      }

      ranges = [[formatDate(workStart), formatDate(workEnd)]];
      ElMessage.info('已设置为工作日范围，请避免周末时段');
      break;
  }

  if (ranges.length > 0) {
    editingDateRanges.value = ranges;
    ElMessage.success('快捷日期设置成功');
  }
};

// 取消编辑期望日期
const cancelDateEdit = () => {
  dateEditDialogVisible.value = false;
  editingDateOrder.value = null;
  editingDateRanges.value = [];
};

const rescheduleOrder = async order => {
  try {
    await ElMessageBox.confirm(
      `确认重新安排订单 ${order.order_id} 的预约吗？这将重新启动预约流程。`,
      '重新安排预约',
      {
        confirmButtonText: '确认重新安排',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 调用后端API重新安排预约
    const res = await http.post('/api/reschedule_order', {
      order_id: order.order_id,
    });

    if (res?.code === 1) {
      ElMessage.success('预约已重新安排');
      await loadOrders(); // 重新加载订单列表
    } else {
      ElMessage.error(res?.message || '重新安排失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新安排预约失败:', error);
      ElMessage.error('操作失败');
    }
  }
};

// 显示错误消息
const showErrorMessage = async order => {
  if (
    ![
      'registe_error',
      'avatar_not_available',
      'schedule_error',
      'urn_create_error',
    ].includes(order.order_status)
  ) {
    return;
  }

  loadingErrorMessage.value = true;
  errorMessageVisible.value = true;
  errorMessageData.value = null;

  try {
    const response = await http.post('/api/get-order-message', {
      order_id: order.order_id,
    });

    if (response?.code === 1) {
      errorMessageData.value = {
        order_id: order.order_id,
        message: response.data.message,
        message_type: response.data.message_type,
        status: response.data.status,
        updated_at: response.data.updated_at,
      };
    } else {
      errorMessageData.value = {
        order_id: order.order_id,
        message: response?.message || '无法获取错误信息',
        message_type: order.order_status,
        status: order.order_status,
        updated_at: new Date().toISOString(),
      };
    }
  } catch (error) {
    console.error('获取错误消息失败:', error);
    errorMessageData.value = {
      order_id: order.order_id,
      message: '获取错误信息失败',
      message_type: order.order_status,
      status: order.order_status,
      updated_at: new Date().toISOString(),
    };
  } finally {
    loadingErrorMessage.value = false;
  }
};

// 历史记录相关辅助函数
const formatDateTime = dateStr => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

const getTimelineType = action => {
  switch (action) {
    case 'create':
      return 'success';
    case 'update':
      return 'primary';
    case 'delete':
      return 'danger';
    case 'status_change':
      return 'warning';
    case 'remark_update':
      return 'info';
    default:
      return 'info';
  }
};

const getTimelineIcon = action => {
  switch (action) {
    case 'create':
      return 'Plus';
    case 'update':
      return 'EditPen';
    case 'delete':
      return 'Delete';
    case 'status_change':
      return 'Switch';
    case 'remark_update':
      return 'ChatLineRound';
    default:
      return 'InfoFilled';
  }
};

const getActionText = action => {
  switch (action) {
    case 'create':
      return '创建订单';
    case 'update':
      return '更新信息';
    case 'delete':
      return '删除订单';
    case 'status_change':
      return '状态变更';
    case 'remark_update':
      return '备注变更';
    default:
      return '其他操作';
  }
};

const getFieldName = field => {
  const fieldMap = {
    status: '状态',
    visa_type: '签证类型',
    appointment_date: '预约日期',
    appointment_time: '预约时间',
    clients: '申请人信息',
    remark: '备注',
    priority: '优先级',
    // 添加更多字段映射
  };
  return fieldMap[field] || field;
};

// 获取订单状态的中文显示
const getStatusDisplay = status => {
  const statusMap = {
    wait_registe: '待注册',
    regist_error: '注册失败',
    registe_error: '注册失败', // 兼容可能的拼写变体
    avatar_not_available: '头像不可用',
    schedule_error: '预约失败',
    appointment_downloaded: '预约成功',
    appointment_canceled: '已取消',
    account_deleted: '账号已删除',
    waiting_for_payment: '待付款',
    wait_pay: '待付款',
    registe_success: '注册成功',
    regist_success: '注册成功', // 兼容可能的拼写变体
    pending: '待预约',
    normal: '待处理',
    deleted: '已删除',
    waitlisted: '候补队列中',
    waitlist_available: '已候补成功,待预约',
    pause: '已暂停',
    urn_create_error: 'URN创建失败',
  };
  return statusMap[status?.toLowerCase()] || status || '未知';
};

// 获取状态标签类型
const getStatusType = status => {
  const statusLower = status?.toLowerCase();
  if (
    statusLower === 'regist_error' ||
    statusLower === 'registe_error' ||
    statusLower === 'schedule_error' ||
    statusLower === 'avatar_not_available' ||
    statusLower === 'urn_create_error'
  )
    return 'danger';
  if (statusLower === 'schedule_error') return 'warning';
  if (statusLower === 'appointment_downloaded') return 'success';
  if (statusLower === 'waiting_for_payment' || statusLower === 'wait_pay')
    return 'warning';
  if (statusLower === 'pause') return 'info';
  if (
    statusLower === 'registe_success' ||
    statusLower === 'regist_success' ||
    statusLower === 'pending' ||
    statusLower === 'waitlisted' ||
    statusLower === 'waitlist_available'
  )
    return 'primary';
  return 'info';
};

// 获取状态图标
const getStatusIcon = status => {
  const statusLower = status?.toLowerCase();
  if (
    statusLower === 'regist_error' ||
    statusLower === 'registe_error' ||
    statusLower === 'schedule_error' ||
    statusLower === 'avatar_not_available' ||
    statusLower === 'urn_create_error'
  )
    return 'CircleClose';
  if (
    statusLower === 'appointment_downloaded' ||
    statusLower === 'registe_success' ||
    statusLower === 'regist_success'
  )
    return 'Check';
  if (statusLower === 'waiting_for_payment' || statusLower === 'wait_pay')
    return 'Wallet';
  if (statusLower === 'pending') return 'Clock';
  if (statusLower === 'pause') return 'VideoPause';
  if (statusLower === 'account_deleted') return 'Delete';
  if (statusLower === 'appointment_canceled') return 'CircleClose';
  return 'Document';
};

// 判断状态是否可点击查看错误详情
const isErrorStatus = status => {
  const statusLower = status?.toLowerCase();
  return (
    statusLower === 'regist_error' ||
    statusLower === 'registe_error' ||
    statusLower === 'schedule_error' ||
    statusLower === 'avatar_not_available' ||
    statusLower === 'urn_create_error'
  );
};
</script>

<style scoped>
/* ==================== 全局容器 ==================== */
.pending-orders-container {
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ==================== 固定头部区域 ==================== */
.fixed-header {
  position: relative;
  z-index: 100;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ==================== 页面头部 ==================== */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 24px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 26px;
  font-weight: 600;
  margin: 0 0 6px 0;
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
}

.title-icon {
  font-size: 30px;
  background: rgba(255, 255, 255, 0.2);
  padding: 6px;
  border-radius: 8px;
}

.page-subtitle {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-right .el-button {
  padding: 6px 12px; /* 缩小一半 */
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  transition: all 0.3s ease;
}

.header-right .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}
/* ==================== 筛选区域 ==================== */
.filter-section {
  width: 100%; /* 完全填满容器宽度 */
  padding: 8px clamp(12px, 1.5vw, 24px); /* 响应式内边距 - 缩小一半 */
}

.filter-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px 8px 12px; /* 缩小一半 */
  border-bottom: 1px solid #f0f2f5;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-title .el-icon {
  font-size: 20px;
  color: #667eea;
}

.filter-toggle-btn {
  margin-left: 12px;
  font-size: 14px;
  color: #667eea;
  padding: 4px 8px;
  transition: all 0.3s ease;
}

.filter-toggle-btn:hover {
  color: #5a67d8;
  background-color: rgba(102, 126, 234, 0.1);
}

.filter-actions-top {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-content {
  padding: 12px; /* 缩小一半 */
  transition: all 0.3s ease;
  overflow: hidden;
}

.filter-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  align-items: end;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  flex: 1;
}

.filter-group.filter-actions {
  justify-content: flex-end;
  flex-direction: row;
  align-items: center;
  min-width: auto;
  flex: none;
}

.filter-label {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.filter-input,
.filter-select,
.filter-date {
  width: 100%;
}

.sort-controls {
  display: flex;
  gap: 8px;
  width: 100%;
}

.sort-select {
  flex: 1;
}

.sort-button {
  min-width: 40px;
  padding: 8px;
}

.reset-button {
  padding: 8px 16px;
  border-radius: 8px;
}

/* ==================== 视图控件 ==================== */
.view-controls .el-radio-group {
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.view-controls .el-radio-button__inner {
  padding: 10px 16px;
  border: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.view-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.view-button-content .el-icon {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-text {
  font-size: 14px;
  line-height: 1;
}

.filter-stats .el-tag {
  padding: 8px 12px;
  font-weight: 500;
  border-radius: 8px;
  border: none;
}

.filter-stats .el-icon {
  margin-right: 4px;
}

/* ==================== 可滚动内容区域 ==================== */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 12px 12px 12px; /* 缩小一半 */
  background: transparent;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

.content-container {
  width: 100%; /* 完全填满容器宽度 */
  height: 100%;
  padding: 8px clamp(12px, 1.5vw, 12px) 0; /* 顶部内边距，左右响应式 - 缩小一半 */
  display: flex;
  flex-direction: column;
}

.content-container.list-mode {
  display: flex;
  flex-direction: column;
}

/* ==================== 空状态 ==================== */
.empty-state {
  text-align: center;
  padding: 40px 10px; /* 缩小一半 */
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.empty-icon {
  margin-bottom: 24px;
}

.empty-icon .el-icon {
  font-size: 80px;
  color: #c0c4cc;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 12px; /* 缩小一半 */
  border-radius: 50%;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 16px;
  color: #6c757d;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.empty-action {
  padding: 6px 12px; /* 缩小一半 */
  font-size: 16px;
  border-radius: 12px;
}

/* ==================== 列表视图 ==================== */
.list-view {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.table-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-card .el-card__body {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modern-table {
  border-radius: 16px;
  flex: 1;
  overflow: hidden;
}

.modern-table .el-table__header-wrapper {
  position: sticky;
  top: 0;
  z-index: 10;
}

.modern-table .el-table__header {
  margin: 0;
}

.modern-table .el-table__body-wrapper {
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

.modern-table .el-table__row {
  transition: all 0.3s ease;
  border-bottom: 1px solid #f0f2f5;
}

.modern-table .el-table__row:hover {
  background-color: #f8fafe;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.08);
}

/* 表格滚动条样式 */
.modern-table .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px;
}

.modern-table .el-table__body-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modern-table .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.modern-table .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* ==================== 表格内容样式 ==================== */

/* 客户信息容器 */
.clients-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 0;
}

.client-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.client-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.client-details {
  flex: 1;
  min-width: 0;
}

.client-name-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.client-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.client-passport-row {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  font-size: 12px;
}

.passport-label {
  color: #6c757d;
}

.passport-number {
  color: #495057;
  font-family: monospace;
}

.passport-icon {
  cursor: pointer;
  color: #667eea;
  transition: all 0.3s ease;
  margin-left: 4px;
}

.passport-icon:hover {
  color: #5a67d8;
  transform: scale(1.2);
}

.client-extra-info {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #6c757d;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

/* 签证信息容器 */
.visa-info-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 0;
}

.visa-item {
  padding: 12px;
  background: #f0f8ff;
  border-radius: 8px;
  border-left: 3px solid #667eea;
  border: 1px solid #e1ecf4;
  margin-bottom: 8px;
}

.visa-item:last-child {
  margin-bottom: 0;
}

.visa-country {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 6px;
  font-size: 14px;
}

.visa-center {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 13px;
  margin-bottom: 6px;
}

.visa-type {
  display: flex;
  align-items: center;
  gap: 6px;
}

.visa-type .el-tag {
  font-weight: 600;
  font-size: 12px;
}

/* 国旗样式 */
.country-flag {
  display: inline-block;
  width: 20px;
  height: 15px;
  margin-left: 8px;
  border-radius: 2px;
  position: relative;
  vertical-align: middle;
}

.order-flag {
  display: inline-block;
  width: 24px;
  height: 18px;
  margin-right: 8px;
  border-radius: 3px;
  position: relative;
  vertical-align: middle;
}

/* 国旗背景色 */
.flag-es {
  background: linear-gradient(
    to bottom,
    #c60b1e 0%,
    #c60b1e 25%,
    #ffc400 25%,
    #ffc400 75%,
    #c60b1e 75%
  );
}
.flag-fr {
  background: linear-gradient(
    to right,
    #0055a4 0%,
    #0055a4 33%,
    #ffffff 33%,
    #ffffff 66%,
    #ef4135 66%
  );
}
.flag-de {
  background: linear-gradient(
    to bottom,
    #000000 0%,
    #000000 33%,
    #dd0000 33%,
    #dd0000 66%,
    #ffce00 66%
  );
}
.flag-it {
  background: linear-gradient(
    to right,
    #009246 0%,
    #009246 33%,
    #ffffff 33%,
    #ffffff 66%,
    #ce2b37 66%
  );
}
.flag-nl {
  background: linear-gradient(
    to bottom,
    #ae1c28 0%,
    #ae1c28 33%,
    #ffffff 33%,
    #ffffff 66%,
    #21468b 66%
  );
}
.flag-be {
  background: linear-gradient(
    to right,
    #000000 0%,
    #000000 33%,
    #ffd700 33%,
    #ffd700 66%,
    #ef3340 66%
  );
}
.flag-at {
  background: linear-gradient(
    to bottom,
    #ed2939 0%,
    #ed2939 33%,

    #ffffff 33%,
    #ffffff 66%,
    #ed2939 66%
  );
}
.flag-ch {
  background: #ff0000;
}
.flag-pt {
  background: linear-gradient(to right, #046a38 0%, #046a38 40%, #da020e 40%);
}
.flag-gr {
  background: linear-gradient(
    to bottom,
    #0d5eaf 0%,
    #0d5eaf 11%,
    #ffffff 11%,
    #ffffff 22%,
    #0d5eaf 22%,
    #0d5eaf 33%,
    #ffffff 33%,
    #ffffff 44%,
    #0d5eaf 44%,
    #0d5eaf 55%,
    #ffffff 55%,
    #ffffff 66%,
    #0d5eaf 66%,
    #0d5eaf 77%,
    #ffffff 77%,
    #ffffff 88%,
    #0d5eaf 88%
  );
}
.flag-dk {
  background: #c60c30;
}
.flag-se {
  background: #006aa7;
}
.flag-no {
  background: #ef2b2d;
}
.flag-fi {
  background: #ffffff;
}
.flag-is {
  background: #02529c;
}
.flag-pl {
  background: linear-gradient(to bottom, #ffffff 0%, #ffffff 50%, #dc143c 50%);
}
.flag-cz {
  background: linear-gradient(to bottom, #ffffff 0%, #ffffff 50%, #d7141a 50%);
}
.flag-hu {
  background: linear-gradient(
    to bottom,
    #ce2939 0%,
    #ce2939 33%,
    #ffffff 33%,
    #ffffff 66%,
    #436f4d 66%
  );
}
.flag-us {
  background: #b22234;
}
.flag-ca {
  background: #ff0000;
}
.flag-gb {
  background: #012169;
}
.flag-au {
  background: #012169;
}
.flag-nz {
  background: #012169;
}
.flag-jp {
  background: #ffffff;
}
.flag-kr {
  background: #ffffff;
}
.flag-sg {
  background: linear-gradient(to bottom, #ed2939 0%, #ed2939 50%, #ffffff 50%);
}
.flag-my {
  background: linear-gradient(
    to bottom,
    #cc0001 0%,
    #cc0001 7%,
    #ffffff 7%,
    #ffffff 14%,
    #cc0001 14%,
    #cc0001 21%,
    #ffffff 21%,
    #ffffff 28%,
    #cc0001 28%,
    #cc0001 35%,
    #ffffff 35%,
    #ffffff 42%,
    #cc0001 42%,
    #cc0001 49%,
    #ffffff 49%,
    #ffffff 56%,
    #cc0001 56%,
    #cc0001 63%,
    #ffffff 63%,
    #ffffff 70%,
    #cc0001 70%,
    #cc0001 77%,
    #ffffff 77%,
    #ffffff 84%,
    #cc0001 84%,
    #cc0001 91%,
    #ffffff 91%
  );
}
.flag-th {
  background: linear-gradient(
    to bottom,
    #ed1c24 0%,
    #ed1c24 16%,
    #ffffff 16%,
    #ffffff 33%,
    #241d4f 33%,
    #241d4f 66%,
    #ffffff 66%,
    #ffffff 83%,
    #ed1c24 83%
  );
}
.flag-in {
  background: linear-gradient(
    to bottom,
    #ff9933 0%,
    #ff9933 33%,
    #ffffff 33%,
    #ffffff 66%,
    #138808 66%
  );
}
.flag-ru {
  background: linear-gradient(
    to bottom,
    #ffffff 0%,
    #ffffff 33%,
    #0039a6 33%,
    #0039a6 66%,
    #d52b1e 66%
  );
}
.flag-tr {
  background: #e30a17;
}
.flag-br {
  background: #009739;
}
.flag-ar {
  background: linear-gradient(
    to bottom,
    #74acdf 0%,
    #74acdf 33%,
    #ffffff 33%,
    #ffffff 66%,
    #74acdf 66%
  );
}
.flag-cl {
  background: linear-gradient(to bottom, #ffffff 0%, #ffffff 50%, #d52b1e 50%);
}
.flag-mx {
  background: linear-gradient(
    to right,
    #006847 0%,
    #006847 33%,
    #ffffff 33%,
    #ffffff 66%,
    #ce1126 66%
  );
}
.flag-za {
  background: linear-gradient(
    135deg,
    #007a4d 0%,
    #007a4d 16%,
    #ffffff 16%,
    #ffffff 20%,
    #ffb612 20%,
    #ffb612 24%,
    #ffffff 24%,
    #ffffff 28%,
    #de3831 28%,
    #de3831 32%,
    #ffffff 32%,
    #ffffff 36%,
    #002395 36%
  );
}
.flag-eg {
  background: linear-gradient(
    to bottom,
    #ce1126 0%,
    #ce1126 33%,
    #ffffff 33%,
    #ffffff 66%,
    #000000 66%
  );
}
.flag-il {
  background: linear-gradient(
    to bottom,
    #0038b8 0%,
    #0038b8 15%,
    #ffffff 15%,
    #ffffff 85%,
    #0038b8 85%
  );
}
.flag-ae {
  background: linear-gradient(
    to bottom,
    #00732f 0%,
    #00732f 33%,
    #ffffff 33%,
    #ffffff 66%,
    #000000 66%
  );
}
.flag-sa {
  background: #006c35;
}
.flag-unknown {
  background: #cccccc;
}

/* 为瑞士国旗添加十字 */
.flag-ch::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 12px;
  background: #ffffff;
}

.flag-ch::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 6px;
  background: #ffffff;
}

/* 为日本国旗添加红圆 */
.flag-jp::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background: #bc002d;
  border-radius: 50%;
}

/* 为韩国国旗添加太极图案 */
.flag-kr::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: linear-gradient(45deg, #cd2e3a 0%, #cd2e3a 50%, #0047a0 50%);
  border-radius: 50%;
}

/* 日期信息容器 */
.date-info-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 0;
}

.date-range-item {
  padding: 8px;
  background: #fff8f0;
  border-radius: 6px;
  border-left: 3px solid #f39c12;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #2c3e50;
  margin-bottom: 4px;
}

.date-text {
  font-weight: 500;
}

.arrow-icon {
  color: #f39c12;
  font-size: 12px;
}

.date-duration {
  font-size: 11px;
  color: #e67e22;
  font-weight: 600;
}

/* 时间信息容器 */
.time-info-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px 0;
}

.time-main {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.time-sub {
  font-size: 12px;
  color: #6c757d;
  margin-left: 20px;
}

.time-relative {
  font-size: 11px;
  color: #28a745;
  margin-left: 20px;
  font-weight: 500;
}

/* 操作按钮容器 */
.action-buttons-container {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

/* 下拉菜单样式 */
.card-actions .el-dropdown {
  display: inline-block;
}

.card-actions .el-button {
  min-width: 70px;
}

/* 下拉菜单项样式 */
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

/* 切换项样式 */
.dropdown-toggle-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
  cursor: pointer;
}

.dropdown-toggle-item .el-icon {
  font-size: 14px;
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.dropdown-toggle-item span {
  flex: 1;
  font-size: 14px;
}

.dropdown-toggle-item .el-switch {
  flex-shrink: 0;
}

/* 危险操作样式 */
:deep(.el-dropdown-menu__item.is-divided) {
  border-top: 1px solid var(--el-border-color-light);
  margin-top: 4px;
  padding-top: 8px;
}

:deep(.el-dropdown-menu__item.is-divided:hover) {
  background-color: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}

.action-btn {
  padding: 6px 8px;
  min-width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn .el-icon {
  font-size: 14px;
}
/* ==================== 卡片视图 ==================== */
.card-view {
  width: 100%;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.cards-grid {
  display: grid;
  gap: clamp(16px, 2vw, 24px); /* 响应式间距 */
  padding: 0;
  width: 100%;
}

/* 响应式列数布局 - 最多3列，按窗口宽度除以数量计算卡片宽度 */
@media (max-width: 767px) {
  .cards-grid {
    grid-template-columns: 1fr; /* 小屏：1列 */
    gap: 16px;
  }
}

@media (min-width: 768px) and (max-width: 1599px) {
  .cards-grid {
    grid-template-columns: repeat(2, 1fr); /* 中屏：2列 */
    gap: clamp(16px, 2vw, 20px);
  }
}

@media (min-width: 1600px) {
  .cards-grid {
    grid-template-columns: repeat(3, 1fr); /* 大屏及以上：最多3列 */
    gap: clamp(20px, 2vw, 24px);
  }
}

/* 大屏幕优化 - 3列布局下的卡片高度调整 */
@media (min-width: 1600px) {
  .order-card {
    min-height: 400px; /* 3列布局下的标准高度 */
  }

  /* 3列布局下的内容优化 */
  .card-section {
    padding: 7px 9px; /* 缩小一半 */
  }

  .section-title {
    font-size: 13px; /* 稍微减少标题字体 */
    margin-bottom: 10px;
  }

  .visa-country-card {
    margin-bottom: 6px; /* 减少间距 */
  }

  .country-name {
    font-size: 13px; /* 保持合适的字体大小 */
  }
}

.order-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  background: white;
  min-height: 420px; /* 设置最小高度确保卡片一致 */
  min-width: 0; /* 允许卡片收缩以适应网格 */
  display: flex;
  flex-direction: column;
  padding: 12px; /* 缩小一半 */
}

.order-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px 12px 8px 12px; /* 缩小一半 */
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #f0f2f5;
}

.card-header-left {
  flex: 1;
}

.order-id {
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.timestamps {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.timestamps .el-tag {
  border-radius: 8px;
  font-size: 12px;
  padding: 4px 8px;
}

.card-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.card-actions .el-button {
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.card-actions .el-button:hover {
  transform: scale(1.1);
}
/* 卡片内容样式优化 */
.card-content {
  padding: 0;
}

.card-section {
  padding: 8px 10px; /* 缩小一半 */
  border-bottom: 1px solid #f0f2f5;
  flex-shrink: 0; /* 防止内容区域被压缩 */
}

.card-section:last-child {
  border-bottom: none;
}

.card-section.card-actions-section {
  margin-top: auto; /* 将操作区域推到底部 */
  padding-top: 10px; /* 缩小一半 */
  border-top: 1px solid #f0f2f5;
  border-bottom: none;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.section-title .el-icon {
  font-size: 16px;
  color: #667eea;
}

.clients-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.client-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.client-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
}

.client-info {
  flex: 1;
  min-width: 0;
}

.client-name {
  font-size: 15px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 6px;
}

.client-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.client-passport,
.client-dob,
.client-expire {
  font-size: 13px;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 4px;
}

.passport-icon {
  cursor: pointer;
  color: #667eea;
  transition: all 0.3s ease;
  margin-left: 4px;
}

.passport-icon:hover {
  color: #5a67d8;
  transform: scale(1.2);
}

.visa-types,
.date-ranges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.visa-tag,
.date-tag {
  margin: 0;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
}

.remark-content {
  color: #e74c3c;
  font-size: 14px;
  line-height: 1.5;
  background: #fef2f2;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #e74c3c;
}

/* 移除重复样式，已在上面重新定义 */

/* 筛选栏增强样式 */
.filter-inputs .el-input,
.filter-inputs .el-select,
.filter-inputs .el-date-editor {
  transition: all 0.3s ease;
}

.filter-inputs .el-input:hover,
.filter-inputs .el-select:hover,
.filter-inputs .el-date-editor:hover {
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.sort-controls .el-button {
  min-width: 32px;
  padding: 8px;
}

.filter-stats .el-tag {
  font-weight: 500;
}

/* 状态标签样式 */
.status-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-tag {
  font-size: 13px;
  padding: 6px 12px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.status-tag.clickable {
  cursor: pointer;
}

.status-tag.clickable:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 卡片中的状态标签 */
.order-status-badge {
  margin: 8px 0;
}

.status-tag-card {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 10px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-weight: 700;
  transition: all 0.3s ease;
}

.status-tag-card.clickable {
  cursor: pointer;
  animation: pulse 2s infinite;
}

.status-tag-card.clickable:hover {
  transform: scale(1.08);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* 错误状态脉冲动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state .el-icon {
  font-size: 64px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-state h3 {
  margin: 16px 0 8px;
  font-size: 18px;
  font-weight: 500;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 列表模式样式优化 */
.list-container .client-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.list-container .client-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 4px 0;
}

.list-container .client-name {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.list-container .client-passport {
  font-size: 12px;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 4px;
}

.list-container .passport-icon {
  cursor: pointer;
  color: #667eea;
  font-size: 14px;
  transition: all 0.3s ease;
}

.list-container .passport-icon:hover {
  color: #5a67d8;
  transform: scale(1.1);
}

.list-container .visa-types {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.list-container .visa-tag {
  margin: 0;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
}

.list-container .date-ranges {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.list-container .date-tag {
  margin: 0;
  font-size: 12px;
}

.list-container .time-info {
  font-size: 12px;
  color: #6c757d;
  white-space: nowrap;
}

.list-container .action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* 确保卡片头部样式正确 */
.order-card .card-header {
  padding: 0;
  background: transparent;
  border-bottom: none;
}

.card-header-left {
  flex: 1;
}

.order-id {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.timestamps {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.timestamps .el-tag {
  font-size: 11px;
  padding: 2px 6px;
}

.card-actions {
  display: flex;
  gap: 4px;
  align-items: flex-start;
}

.card-actions .el-button {
  padding: 6px;
  min-width: 32px;
}

/* ==================== 卡片模式签证类型样式 ==================== */
.visa-types-card {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 180px; /* 3列布局下减少最大高度 */
  overflow-y: auto; /* 超出时显示滚动条 */
}

.visa-item-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 5px; /* 缩小一半 */
  transition: all 0.3s ease;
}

.visa-item-card:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.15);
}

.visa-country-card {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.country-name {
  margin-left: 8px;
  font-size: 14px;
}

.visa-details-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.visa-center-card {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; /* 允许收缩 */
}

.center-icon {
  font-size: 14px;
  color: #6c757d;
  margin-right: 6px;
  flex-shrink: 0;
}

.center-text {
  font-size: 12px; /* 3列布局下使用更小字体 */
  color: #6c757d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.visa-type-tag {
  flex-shrink: 0;
  font-size: 11px; /* 3列布局下使用更小字体 */
  max-width: 100px; /* 3列布局下减少最大宽度 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 签证类型容器滚动条美化 */
.visa-types-card::-webkit-scrollbar {
  width: 4px;
}

.visa-types-card::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.visa-types-card::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.visa-types-card::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ==================== 历史记录 Dialog ==================== */
.history-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.history-content {
  min-height: 300px;
}

.no-history {
  text-align: center;
  padding: 40px 0;
}

.history-timeline {
  padding: 0 20px;
}

.history-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.action-type {
  font-weight: 600;
  color: #409eff;
  font-size: 14px;
}

.operator {
  color: #909399;
  font-size: 12px;
}

/* 操作员列样式 */
.operator-info-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.operator-name {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.changes-list {
  margin-bottom: 12px;
}

.changes-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
  font-size: 13px;
}

.change-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
  line-height: 1.5;
}

.field-name {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.old-value {
  color: #f56c6c;
  background: #fef0f0;
  padding: 2px 6px;
  border-radius: 4px;
  margin: 0 8px;
}

.arrow {
  color: #909399;
  margin: 0 4px;
}

.new-value {
  color: #67c23a;
  background: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.remark {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.remark-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
  font-size: 13px;
}

.remark-content {
  color: #606266;
  font-size: 12px;
  line-height: 1.5;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  white-space: pre-wrap;
}
.remark-content.empty {
  color: #909399;
  background: transparent;
  padding: 0;
}

/* 可点击的时间样式 */
.clickable-time {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-time:hover {
  background: #f0f9ff;
  border-radius: 4px;
  transform: translateY(-1px);
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* ==================== 宽屏优化 ==================== */
@media (min-width: 1600px) {
  .filter-inputs {
    gap: 32px; /* 增加筛选项之间的间距 */
  }

  .filter-group {
    min-width: 220px; /* 增加筛选组的最小宽度 */
  }

  .table-card {
    border-radius: 16px; /* 更大的圆角 */
  }
}

@media (min-width: 2000px) {
  .filter-group {
    min-width: 250px;
  }
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .filter-row {
    flex-direction: column;
    gap: 16px;
  }

  .filter-group {
    min-width: 100%;
  }
}

@media (max-width: 768px) {
  .pending-orders-container {
    height: 100%;
  }

  .page-header {
    padding: 8px 0; /* 缩小一半 */
  }

  .page-title {
    font-size: 20px;
  }

  .filter-section {
    padding: 6px 8px; /* 缩小一半 */
  }

  .scrollable-content {
    padding: 0 8px 8px 8px; /* 缩小一半 */
  }

  .filter-content {
    padding: 8px; /* 缩小一半 */
  }

  .filter-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-actions-top {
    justify-content: space-between;
  }

  /* 移动端签证类型优化 */
  .visa-types-card {
    max-height: 150px; /* 移动端减少高度 */
  }

  .visa-item-card {
    padding: 5px; /* 缩小一半 */
  }

  .visa-details-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .visa-center-card {
    width: 100%;
  }

  .center-text {
    font-size: 12px;
  }

  .visa-type-tag {
    max-width: 100%;
    align-self: flex-end;
  }

  .country-name {
    font-size: 13px;
  }

  /* 移动端卡片高度优化 */
  .order-card {
    min-height: 350px; /* 移动端减少卡片高度 */
    padding: 8px; /* 缩小一半 */
  }
}

/* ==================== 动画效果 ==================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.order-card,
.table-card,
.filter-card {
  animation: fadeInUp 0.6s ease-out;
}

.order-card:nth-child(even) {
  animation-delay: 0.1s;
}

.order-card:nth-child(3n) {
  animation-delay: 0.2s;
}

/* ==================== 错误消息弹窗样式 ==================== */
.error-message-container {
  min-height: 200px;
}

.error-content {
  text-align: left;
}

.error-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.error-icon {
  color: #f56c6c;
  font-size: 20px;
}

.error-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.error-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.error-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.error-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.error-message {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-content {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  padding: 12px;
  color: #f56c6c;
  font-size: 14px;
  line-height: 1.5;
}

.no-error-message {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
}

/* ==================== 期望日期编辑弹窗 ==================== */
.date-edit-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.date-edit-content {
  margin-bottom: 20px;
}

.order-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.order-label {
  font-weight: 500;
  color: #606266;
}

.order-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #409eff;
}

.client-name {
  color: #303133;
  font-weight: 500;
}

.date-ranges-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-ranges-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.date-range-item-edit {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s;
}

.date-range-item-edit:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.range-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.range-number {
  font-weight: 500;
  color: #606266;
}

.remove-btn {
  color: #f56c6c;
  padding: 4px;
}

.remove-btn:hover {
  background-color: #fef0f0;
  border-radius: 4px;
}

.date-picker-full {
  width: 100%;
}

.range-preview {
  margin-top: 10px;
  padding: 8px 12px;
  background: #e7f7ff;
  border-radius: 4px;
  font-size: 13px;
  color: #409eff;
}

.spain-notice {
  margin-top: 15px;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.date-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.edit-date-btn {
  padding: 4px 8px;
  color: #409eff;
  opacity: 0.7;
  transition: all 0.2s;
}

.edit-date-btn:hover {
  opacity: 1;
  background-color: #ecf5ff;
}

.edit-date-btn .el-icon {
  font-size: 14px;
}

/* 卡片模式编辑按钮样式 */
.edit-date-btn-card {
  padding: 2px 6px;
  color: #409eff;
  opacity: 0.7;
  transition: all 0.2s;
  margin-left: auto;
}

.edit-date-btn-card:hover {
  opacity: 1;
  background-color: #ecf5ff;
  border-radius: 4px;
}

.edit-date-btn-card .el-icon {
  font-size: 14px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-tag {
  margin-right: 8px;
  margin-bottom: 6px;
}

.date-duration {
  font-size: 12px;
  color: #909399;
  margin-left: 6px;
}

/* ==================== 客户图片弹窗样式 ==================== */
.client-images-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 15px;
  color: #606266;
}

.loading-container .is-loading {
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.images-container {
  display: flex;
  gap: 20px;
  min-height: 400px;
}

.image-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #fafafa;
}

.image-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
  color: #303133;
}

.image-header .el-icon {
  color: #409eff;
  font-size: 16px;
}

.avatar-actions {
  margin-left: auto;
}

.image-wrapper {
  flex: 1;
}

.no-avatar-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 20px;
  color: #909399;
}

.placeholder-icon {
  color: #c0c4cc;
}

.placeholder-text {
  font-size: 14px;
  color: #909399;
}

.client-image {
  width: 100%;
  height: 350px;
  flex: 1;
}

.client-image :deep(.el-image__inner) {
  transition: transform 0.3s ease;
}

.client-image:hover :deep(.el-image__inner) {
  transform: scale(1.02);
}

.no-images {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ========== 预约弹窗布局优化 ========== */
.order-brief-card {
  margin-bottom: 10px;
}
.book-now-content {
  display: grid;
  grid-template-columns: 1fr 1.1fr;
  gap: 16px;
  align-items: start;
}
.panel-card {
  --el-card-padding: 12px;
}
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}
.panel-header .sub {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}
.legend {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}
.legend .dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin: 0 6px 0 12px;
  vertical-align: middle;
}
.legend .dot-available {
  background: var(--el-color-primary-light-7);
}
.legend .dot-selected {
  background: var(--el-color-primary);
}
.legend .dot-disabled {
  background: var(--el-text-color-disabled);
}

/* 日历缩小些：紧凑日期单元 */
.el-calendar .el-calendar-day {
  padding: 2px !important;
}
.date-cell {
  height: 24px;
  line-height: 24px;
  font-size: 12px;
}

/* 时段栅格 */
.slots-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.slot-actions {
  margin-bottom: 8px;
}
.selection-summary {
  margin-top: 10px;
}

/* 响应式：小屏上下结构 */
@media (max-width: 768px) {
  .book-now-content {
    grid-template-columns: 1fr;
  }
  .client-images-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 2vh auto !important;
  }
  .images-container {
    flex-direction: column;
    gap: 15px;
  }
  .client-image {
    height: 250px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* ==================== 滚动条美化 ==================== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* 立即预约弹窗整体高度 80% 视窗，并让内容区可滚动（Dialog 使用 Teleport，需用 :deep） */
:deep(.book-now-dialog) {
  height: 80vh;
  display: flex;
  flex-direction: column;
}
:deep(.book-now-dialog .el-dialog__body) {
  flex: 1;
  overflow: auto;
}

/* 固定头部与底部，仅内容区滚动 */
:deep(.book-now-dialog .el-dialog__header) {
  flex: 0 0 auto;
}
:deep(.book-now-dialog .el-dialog__footer) {
  flex: 0 0 auto;
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.04);
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}
/* ==================== 立即预约弹窗 - 日历高亮 ==================== */
:deep(.book-now-dialog .el-calendar-table td) {
  padding: 0 !important;
}
:deep(.book-now-dialog .el-calendar-day) {
  height: 40px !important;
  min-height: 40px !important;
  padding: 2px !important;
}
:deep(.book-now-dialog .el-calendar-day .date-cell) {
  height: 36px;
  line-height: 36px;
  border-radius: 6px;
  text-align: center;
  font-size: 13px;
  transition: all 0.15s ease;
}
.el-calendar .el-calendar-day .date-cell.clickable {
  cursor: pointer;
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  font-weight: 600;
  border: 1px solid var(--el-color-primary-light-5);
}
.el-calendar .el-calendar-day .date-cell.clickable:hover {
  background: var(--el-color-primary-light-8);
}
.el-calendar .el-calendar-day .date-cell.selected {
  background: var(--el-color-primary);
  color: #fff;
  border: 1px solid var(--el-color-primary);
}
.el-calendar .el-calendar-day .date-cell.disabled {
  color: var(--el-text-color-disabled);
  opacity: 0.5;
}
</style>
