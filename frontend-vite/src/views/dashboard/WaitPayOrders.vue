<template>
  <div class="wait-pay-orders-container">
    <!-- 固定头部区域 -->
    <div class="fixed-header">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1 class="page-title">
              <el-icon class="title-icon"><Money /></el-icon>
              待支付订单
            </h1>
            <p class="page-subtitle">查看所有等待支付的签证订单</p>
          </div>
          <div class="header-right">
            <div class="header-stats">
              <el-tag type="warning" effect="light" size="large">
                <el-icon><Clock /></el-icon>
                待支付: {{ totalCount }} 单
              </el-tag>
            </div>
            <el-button
              type="default"
              size="large"
              :loading="loading"
              @click="loadOrders"
            >
              <el-icon><Refresh /></el-icon>
              刷新列表
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-wrapper">
      <div class="orders-container" v-loading="loading">
        <!-- 列表视图 -->
        <div class="table-container">
          <el-table :data="displayedOrders" style="width: 100%" height="500">
            <el-table-column label="客户" min-width="220">
              <template #default="{ row }">
                <div class="customer-info">
                  <div class="customer-names">
                    <div
                      v-for="(customer, index) in row.clients || []"
                      :key="index"
                      class="customer-name-item"
                    >
                      {{ customer.name }}
                    </div>
                  </div>
                  <div class="visa-info">
                    <div
                      v-if="row.visaType && Array.isArray(row.visaType)"
                      class="visa-list"
                    >
                      <el-tag
                        v-for="(visa, index) in row.visaType"
                        :key="index"
                        type="info"
                        effect="light"
                        size="small"
                        class="visa-tag"
                      >
                        {{ getVisaDisplayName(visa) }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="
                userStore.permission === 'admin' ||
                userStore.permission === 'kefu'
              "
              prop="operator_name"
              label="操作员"
              width="100"
            />
            <el-table-column prop="customer" label="来源" width="100">
              <template #default="{ row }">
                <span v-if="row.customer" class="customer-text">
                  {{ row.customer }}
                </span>
                <span v-else class="no-customer">-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="140">
              <template #default="{ row }">
                <el-button
                  type="success"
                  size="small"
                  @click.stop="openPaymentDialog(row)"
                >
                  <el-icon><CreditCard /></el-icon>
                  支付
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="预约信息" min-width="180">
              <template #default="{ row }">
                <div class="appointment-info">
                  <div
                    v-if="row.appointment_date || row.appointment_time"
                    class="appointment-details"
                  >
                    <div v-if="row.appointment_date" class="appointment-date">
                      <el-icon><Calendar /></el-icon>
                      {{ row.appointment_date }}
                    </div>
                    <div v-if="row.appointment_time" class="appointment-time">
                      <el-icon><Clock /></el-icon>
                      {{ row.appointment_time }}
                    </div>
                  </div>
                  <div v-else class="no-appointment">
                    <el-icon><Warning /></el-icon>
                    未预约
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="120">
              <template #default="{ row }">
                <span v-if="row.remark" class="remark-text">{{
                  row.remark
                }}</span>
                <span v-else class="no-remark">-</span>
              </template>
            </el-table-column>

            <el-table-column prop="travel_date" label="出行日期" width="120" />

            <el-table-column prop="created_at" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="支付倒计时" width="120" fixed="right">
              <template #default="{ row }">
                <div class="countdown-container">
                  <el-tag
                    :type="getCountdownType(row.updated_at)"
                    effect="light"
                    size="small"
                  >
                    <el-icon><Clock /></el-icon>
                    {{ getCountdownText(row.updated_at) }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-dropdown trigger="click" placement="bottom-end">
                  <el-button type="primary" size="small">
                    操作 <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click.stop="viewOrderDetail(row)">
                        <el-icon><View /></el-icon>
                        查看详情
                      </el-dropdown-item>
                      <el-dropdown-item @click.stop="rescheduleOrder(row)">
                        <el-icon><RefreshRight /></el-icon>
                        重新预约
                      </el-dropdown-item>
                      <el-dropdown-item
                        divided
                        @click.stop="deleteOrder(row.order_id)"
                      >
                        <el-icon><Delete /></el-icon>
                        删除订单
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 分页器 -->
      <div v-if="displayedOrders.length > 0" class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 订单详情弹窗 -->
    <el-dialog
      v-model="orderDetailVisible"
      title="订单详情"
      width="800px"
      top="5vh"
    >
      <div v-if="selectedOrder" class="order-detail">
        <!-- 订单基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单号">
              {{ selectedOrder.order_id }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag type="warning" effect="light">
                <el-icon><Clock /></el-icon>
                待支付
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="金额">
              <span class="price-text">¥{{ selectedOrder.price || '0' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(selectedOrder.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="出行日期">
              {{ selectedOrder.travel_date || '未指定' }}
            </el-descriptions-item>
            <el-descriptions-item label="预约日期">
              <span
                v-if="selectedOrder.appointment_date"
                class="appointment-date"
              >
                {{ selectedOrder.appointment_date }}
              </span>
              <span v-else class="no-appointment">未预约</span>
            </el-descriptions-item>
            <el-descriptions-item label="预约时间">
              <span
                v-if="selectedOrder.appointment_time"
                class="appointment-time"
              >
                {{ selectedOrder.appointment_time }}
              </span>
              <span v-else class="no-appointment">未预约</span>
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              <span v-if="selectedOrder.remark" class="remark-text">
                {{ selectedOrder.remark }}
              </span>
              <span v-else class="no-remark">无备注</span>
            </el-descriptions-item>
            <el-descriptions-item
              v-if="
                userStore.permission === 'admin' ||
                userStore.permission === 'kefu'
              "
              label="操作员"
            >
              {{ selectedOrder.operator_name || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="来源">
              <span v-if="selectedOrder.customer" class="customer-text">
                {{ selectedOrder.customer }}
              </span>
              <span v-else class="no-customer">-</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 客户信息 -->
        <div class="detail-section">
          <h3>客户信息</h3>
          <el-table
            :data="selectedOrder.clients || []"
            border
            style="width: 100%"
          >
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="passport" label="护照号" />
            <el-table-column prop="phone" label="电话" />
            <el-table-column prop="email" label="邮箱" />
          </el-table>
        </div>

        <!-- 签证信息 -->
        <div class="detail-section">
          <h3>签证信息</h3>
          <div class="visa-detail-list">
            <div
              v-for="(visa, index) in selectedOrder.visaType || []"
              :key="index"
              class="visa-detail-item"
            >
              <el-tag type="info" effect="light">
                {{ getVisaDisplayName(visa) }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 期望日期段 -->
        <div class="detail-section">
          <h3>期望日期段</h3>
          <div
            v-if="
              selectedOrder.dateRangeList &&
              selectedOrder.dateRangeList.length > 0
            "
            class="date-ranges-detail"
          >
            <div
              v-for="(range, index) in selectedOrder.dateRangeList"
              :key="index"
              class="date-range-item"
            >
              <el-tag type="primary" effect="light" size="large">
                {{ range[0] }} ~ {{ range[1] }}
              </el-tag>
            </div>
          </div>
          <div v-else class="no-date-ranges">
            <el-empty description="暂无期望日期段" :image-size="60" />
          </div>
        </div>

        <!-- 备注信息 -->
        <div v-if="selectedOrder.remark" class="detail-section">
          <h3>备注信息</h3>
          <el-input
            v-model="selectedOrder.remark"
            type="textarea"
            :rows="3"
            readonly
          />
        </div>
      </div>
    </el-dialog>

    <!-- 支付弹窗 -->
    <el-dialog
      v-model="paymentDialogVisible"
      title="订单支付"
      width="500px"
      center
      :before-close="handlePaymentDialogClose"
    >
      <div v-if="selectedPaymentOrder" class="payment-dialog-container">
        <div class="payment-info">
          <h4>订单信息</h4>
          <p><strong>订单号：</strong>{{ selectedPaymentOrder.order_id }}</p>
          <p>
            <strong>金额：</strong
            ><span class="price-text"
              >¥{{ selectedPaymentOrder.price || '0' }}</span
            >
          </p>
          <p>
            <strong>客户：</strong>
            <span
              v-for="(customer, index) in selectedPaymentOrder.clients || []"
              :key="index"
            >
              {{ customer.name
              }}{{
                index < (selectedPaymentOrder.clients || []).length - 1
                  ? ', '
                  : ''
              }}
            </span>
          </p>
        </div>

        <div v-if="selectedPaymentOrder.payment_qrcode" class="qr-code-section">
          <h4>扫码支付</h4>
          <div class="qr-display">
            <img
              :src="selectedPaymentOrder.payment_qrcode"
              alt="支付二维码"
              class="qr-image"
            />
            <p class="qr-instruction">请使用微信扫码支付</p>
          </div>
          <div class="payment-actions">
            <el-button
              type="success"
              :loading="paymentLoading"
              @click="confirmPayment"
            >
              <el-icon><Check /></el-icon>
              我已支付
            </el-button>
          </div>
        </div>

        <div v-else class="no-qr-section">
          <h4>支付方式</h4>
          <el-alert
            title="暂无支付二维码"
            description="请联系客服获取其他支付方式"
            type="warning"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useVfsStore } from '@/stores/useVfsStore';
import { useUserStore } from '@/stores/user';
import { ElMessage } from 'element-plus';
import http from '@/utils/http';
import {
  Money,
  Clock,
  View,
  CreditCard,
  RefreshRight,
  Calendar,
  Warning,
  Check,
  ArrowDown,
  Delete,
  Refresh,
} from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';

const vfsStore = useVfsStore();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const orders = ref([]);

// 分页
const currentPage = ref(1);
const pageSize = ref(20);

// 弹窗
const orderDetailVisible = ref(false);
const selectedOrder = ref(null);
const selectedPaymentOrder = ref(null);
const paymentDialogVisible = ref(false);

// 重新预约加载状态
const rescheduleLoading = ref({});

// 支付加载状态
const paymentLoading = ref(false);

// 倒计时相关
const currentTime = ref(Date.now());
let countdownTimer = null;

// 计算属性
const waitPayOrders = computed(() => {
  return orders.value.filter(order => {
    // 只显示待支付的订单
    return order.order_status === 'waiting_for_payment';
  });
});

const totalCount = computed(() => waitPayOrders.value.length);

const displayedOrders = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return waitPayOrders.value.slice(start, end);
});

// 方法
const handleSizeChange = size => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = page => {
  currentPage.value = page;
};

const loadOrders = async () => {
  loading.value = true;
  try {
    const res = await http.get('/api/get_payment_orders');
    if (res?.code === 1) {
      orders.value = res.data || [];
    }
  } catch (error) {
    console.error('加载订单失败:', error);
    ElMessage.error('加载订单失败');
  } finally {
    loading.value = false;
  }
};

const viewOrderDetail = order => {
  selectedOrder.value = order;
  orderDetailVisible.value = true;
};

// 检查预约日期是否在期望日期段内
const isAppointmentInDateRange = (appointmentDate, dateRangeList) => {
  if (!appointmentDate || !dateRangeList || dateRangeList.length === 0) {
    return true; // 如果没有预约日期或没有期望日期段，则不检查
  }

  const appointmentTime = new Date(appointmentDate).getTime();

  return dateRangeList.some(range => {
    const startTime = new Date(range[0]).getTime();
    const endTime = new Date(range[1]).getTime();
    return appointmentTime >= startTime && appointmentTime <= endTime;
  });
};

const openPaymentDialog = async order => {
  try {
    // 先检查订单状态
    const statusCheckRes = await http.get(
      `/api/check_order_status/${order.order_id}`
    );

    if (statusCheckRes?.code === 1) {
      const currentStatus = statusCheckRes.data.order_status;

      // 根据订单状态进行处理
      if (currentStatus === 'payed') {
        ElMessage.info('该订单已支付');
        // 刷新订单列表以移除已支付的订单
        await loadOrders();
        return;
      } else if (currentStatus !== 'waiting_for_payment') {
        ElMessage.warning(
          `订单状态异常，当前状态：${getStatusText(currentStatus)}`
        );
        return;
      }

      // 订单状态正常，继续支付流程
    } else {
      ElMessage.error('获取订单状态失败，请重试');
      return;
    }
  } catch (error) {
    console.error('检查订单状态失败:', error);
    ElMessage.error('检查订单状态失败，请重试');
    return;
  }

  // 检查预约日期是否在期望日期段内
  if (!isAppointmentInDateRange(order.appointment_date, order.dateRangeList)) {
    try {
      await ElMessageBox.confirm(
        `预约日期 ${order.appointment_date} 不在期望日期段内。\n\n期望日期段：${
          order.dateRangeList && order.dateRangeList.length > 0
            ? order.dateRangeList
                .map(range => `${range[0]} ~ ${range[1]}`)
                .join(', ')
            : '无'
        }\n\n是否继续支付？`,
        '预约日期提醒',
        {
          confirmButtonText: '继续支付',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false,
        }
      );
    } catch (error) {
      if (error === 'cancel') {
        return; // 用户取消，不打开支付弹窗
      }
    }
  }

  selectedPaymentOrder.value = order;
  paymentDialogVisible.value = true;
};

// 获取订单状态的中文描述
const getStatusText = status => {
  const statusMap = {
    waiting_for_payment: '待支付',
    payed: '已支付',
    pending: '待预约',
    registe_success: '注册成功',
    registe_error: '注册失败',
    schedule_error: '预约失败',
    appointment_downloaded: '预约成功',
    appointment_canceled: '预约已取消',
    account_deleted: '账号已删除',
    wait_registe: '等待注册',
  };
  return statusMap[status] || status;
};

// 重新预约订单
const rescheduleOrder = async order => {
  try {
    await ElMessageBox.confirm(
      `确定要重新预约订单 ${order.order_id} 吗？这将把订单状态恢复到注册成功状态，并重新进入预约队列。`,
      '确认重新预约',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 设置加载状态
    rescheduleLoading.value[order.order_id] = true;

    const res = await http.post('/api/reschedule_payment_order', {
      order_id: order.order_id,
    });

    if (res?.code === 1) {
      ElMessage.success('重新预约成功，订单已重新进入预约队列');
      // 重新加载订单列表
      await loadOrders();
    } else {
      ElMessage.error(res?.message || '重新预约失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新预约失败:', error);
      ElMessage.error('重新预约失败，请重试');
    }
  } finally {
    // 清除加载状态
    rescheduleLoading.value[order.order_id] = false;
  }
};

// 确认支付
// 支付弹窗关闭前的处理
const handlePaymentDialogClose = async done => {
  try {
    await ElMessageBox.confirm('请确认您的支付状态', '关闭支付窗口', {
      confirmButtonText: '已完成支付',
      cancelButtonText: '暂未支付',
      type: 'warning',
      distinguishCancelAndClose: true,
    });

    // 用户选择了"已完成支付"
    try {
      paymentLoading.value = true;

      // 调用API更新订单状态为已支付
      const res = await http.post('/api/update_order_status', {
        order_id: selectedPaymentOrder.value.order_id,
        order_status: 'payed',
      });

      if (res?.code === 1) {
        ElMessage.success('支付确认成功');
        // 重新加载订单列表
        await loadOrders();
        done(); // 关闭弹窗
      } else {
        ElMessage.error(res?.message || '支付确认失败');
        // 不关闭弹窗，让用户重试
      }
    } catch (error) {
      console.error('支付确认失败:', error);
      ElMessage.error('支付确认失败，请重试');
      // 不关闭弹窗，让用户重试
    } finally {
      paymentLoading.value = false;
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户选择了"暂未支付"，直接关闭弹窗
      done();
    }
    // 如果是其他错误或用户点击了X，不关闭弹窗
  }
};

const confirmPayment = async () => {
  if (!selectedPaymentOrder.value) return;

  try {
    await ElMessageBox.confirm('请确认您已通过扫码完成支付', '确认支付', {
      confirmButtonText: '确认已支付',
      cancelButtonText: '取消',
      type: 'info',
    });

    paymentLoading.value = true;

    // 调用API更新订单状态为已支付
    const res = await http.post('/api/update_order_status', {
      order_id: selectedPaymentOrder.value.order_id,
      order_status: 'payed',
    });

    if (res?.code === 1) {
      ElMessage.success('支付确认成功');
      paymentDialogVisible.value = false;
      // 重新加载订单列表
      await loadOrders();
    } else {
      ElMessage.error(res?.message || '支付确认失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('支付确认失败:', error);
      ElMessage.error('支付确认失败，请重试');
    }
  } finally {
    paymentLoading.value = false;
  }
};

const getVisaDisplayName = visa => {
  if (!Array.isArray(visa) || visa.length < 2) return '未知签证类型';

  // 查找对应的显示名称
  const mission = vfsStore.config?.find(m => m.missionCode === visa[0]);
  if (!mission) return `${visa[0]} - ${visa[1]}`;

  const center = mission.children?.find(c => c.isoCode === visa[1]);
  if (!center) return `${mission.missionCodeName} - ${visa[1]}`;

  return `${mission.missionCodeName} - ${center.centerName}`;
};

const formatDateTime = dateStr => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 获取倒计时文本
const getCountdownText = updatedAt => {
  if (!updatedAt) return '已过期';

  const updatedTime = new Date(updatedAt).getTime();
  const tenMinutesLater = updatedTime + 10 * 60 * 1000; // 10分钟后
  const remaining = tenMinutesLater - currentTime.value;

  if (remaining <= 0) {
    return '已过期';
  }

  const minutes = Math.floor(remaining / (60 * 1000));
  const seconds = Math.floor((remaining % (60 * 1000)) / 1000);

  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

// 获取倒计时标签类型
const getCountdownType = updatedAt => {
  if (!updatedAt) return 'danger';

  const updatedTime = new Date(updatedAt).getTime();
  const tenMinutesLater = updatedTime + 10 * 60 * 1000;
  const remaining = tenMinutesLater - currentTime.value;

  if (remaining <= 0) {
    return 'danger';
  } else if (remaining <= 3 * 60 * 1000) {
    // 剩余3分钟以内
    return 'warning';
  } else {
    return 'success';
  }
};

// 启动倒计时定时器
const startCountdownTimer = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }

  countdownTimer = setInterval(() => {
    currentTime.value = Date.now();
  }, 1000);
};

// 停止倒计时定时器
const stopCountdownTimer = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
};

// 生命周期
onMounted(async () => {
  // 确保 VFS 配置已加载
  if (!vfsStore.config || vfsStore.config.length === 0) {
    try {
      const resVfsConfig = await http.post('/get_vfs_config');
      vfsStore.setConfig(resVfsConfig.data);
    } catch (error) {
      console.error('加载 VFS 配置失败:', error);
    }
  }

  await loadOrders();

  // 启动倒计时定时器
  startCountdownTimer();
});

onUnmounted(() => {
  // 清理定时器
  stopCountdownTimer();
});

// 删除订单（与 PendingOrders 保持一致接口）
const deleteOrder = order_id => {
  ElMessageBox.confirm('确认删除该订单吗？删除后将无法恢复。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res = await http.post('/api/delete_order', { order_id });
      if (res?.code === 1) {
        ElMessage.success('删除成功');
        loadOrders();
      } else {
        ElMessage.error(res.message || '删除失败');
      }
    })
    .catch(() => {
      // 用户取消
    });
};
</script>

<style scoped>
.wait-pay-orders-container {
  background: #f5f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 固定头部样式 */
.fixed-header {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 100;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #e6a23c 0%, #f56c6c 100%);
  color: white;
  padding: 16px 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;

  align-items: center;
  margin: 0 auto;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 26px;
  font-weight: 600;
  margin: 0 0 6px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-icon {
  font-size: 30px;
}

.page-subtitle {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-stats {
  display: flex;
  gap: 12px;
}

/* 内容区域样式 */
.content-wrapper {
  flex: 1;
  padding: 10px;
  margin: 0 auto;
  width: 100%;
}

/* 列表视图样式 */
.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.customer-names {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.customer-name-item {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

.visa-info {
  margin-top: 4px;
}

.visa-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.visa-tag {
  font-size: 10px;
  padding: 2px 6px;
}

.price-text {
  font-weight: 600;
  color: #e6a23c;
  font-size: 13px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.empty-icon {
  color: #dcdfe6;
}

/* 分页器样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding: 12px 0;
}

/* 订单详情弹窗 */
.order-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.visa-detail-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.visa-detail-item {
  margin-bottom: 8px;
}

/* 预约信息样式 */
.appointment-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.appointment-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.appointment-date {
  color: var(--el-color-primary);
  font-weight: 500;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.appointment-time {
  color: var(--el-color-success);
  font-weight: 500;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.no-appointment {
  color: var(--el-color-warning);
  font-style: italic;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 期望日期段样式 */
.date-ranges {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.date-range-tag {
  margin-bottom: 4px;
}

.no-date-range {
  color: var(--el-color-info);
  font-style: italic;
}

.date-ranges-detail {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.date-range-item {
  margin-bottom: 8px;
}

.no-date-ranges {
  text-align: center;
  padding: 20px;
}

/* 备注样式 */
.remark-text {
  color: var(--el-color-primary);
  line-height: 1.5;
}

.no-remark {
  color: var(--el-color-info);
  font-style: italic;
}

/* 来源样式 */
.customer-text {
  color: var(--el-color-success);
  font-weight: 500;
  font-size: 13px;
}

.no-customer {
  color: var(--el-color-info);
  font-style: italic;
}

/* 倒计时样式 */
.countdown-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.countdown-container .el-tag {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  min-width: 70px;
  text-align: center;
}

/* 支付二维码弹窗样式 */
.payment-qr-container {
  text-align: center;
}

.payment-info {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.payment-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.payment-info p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.qr-code-section {
  margin: 16px 0;
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  background: #fafafa;
}

.qr-icon {
  color: #909399;
  margin-bottom: 12px;
}

.qr-placeholder p {
  margin: 8px 0;
  color: #606266;
  font-size: 16px;
  font-weight: 500;
}

.payment-actions {
  margin-top: 20px;
  text-align: center;
}

.payment-actions .el-button {
  min-width: 140px;
  height: 40px;
  font-size: 15px;
  font-weight: 500;
}

/* 二维码图片样式 */
.qr-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.qr-image {
  max-width: 180px;
  max-height: 180px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  margin-bottom: 10px;
}

.qr-instruction {
  margin: 6px 0;
  color: #606266;
  font-size: 13px;
  text-align: center;
}

/* 支付弹窗样式 */
.payment-dialog-container {
  text-align: center;
}

.payment-dialog-container .payment-info {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  text-align: left;
}

.payment-dialog-container .payment-info h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.payment-dialog-container .payment-info p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.no-qr-section {
  margin: 16px 0;
}

.no-qr-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .content-wrapper {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }
}
</style>
