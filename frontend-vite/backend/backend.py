"""
签证管理系统 API
重构版本 - 更加模块化和清晰的代码结构
"""
from pydantic import BaseModel, Field, field_validator
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse, StreamingResponse
from fastapi import FastAPI, UploadFile, File, HTTPException, Depends, Request, BackgroundTasks
from psycopg2.pool import SimpleConnectionPool
import psycopg2
import requests
import redis
import jwt
import os
import json
import time
import uuid
import random
import base64
import datetime
import logging
import traceback
from typing import Optional, List, Dict, Any
from collections import defaultdict
from contextlib import contextmanager
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
from RedisClient import RedisClient
import io
from PyPDF2 import PdfReader, PdfWriter
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from urllib.parse import quote

from openpyxl import Workbook

import sys

# Add vfs module path for importing VFS calendar/booking helpers
VFS_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'vfs'))
if VFS_DIR not in sys.path:
    sys.path.append(VFS_DIR)

# VFS booking helpers
try:
    from vfs_calendar_scanner import (
        scan_calendar_for_customer,
        request_time_slots_for_date,
        schedule_appointment,
        process_payment_flow,
        fetch_cf_bypass_params,
        get_login_user_for_country,
        convert_ddmmyyyy_to_yyyy_mm_dd,
        convert_mmddyyyy_to_yyyy_mm_dd,
    )
except Exception as _vfs_import_err:
    # 记录但不阻塞整体服务启动，具体接口内部再报错
    logger = logging.getLogger(__name__)
    logger.warning(f"VFS helpers import failed: {_vfs_import_err}")
    try:
        from vfs_calendar_scanner import (
            scan_calendar_for_customer,
            request_time_slots_for_date,
            schedule_appointment,
            process_payment_flow,
            fetch_cf_bypass_params,
            get_login_user_for_country,
            convert_ddmmyyyy_to_yyyy_mm_dd,
            convert_mmddyyyy_to_yyyy_mm_dd,
        )
    except Exception as _vfs_import_err:
        logger = logging.getLogger(__name__)
        logger.warning(f"VFS helpers import failed: {_vfs_import_err}")


# ==================== 线程池配置 ====================
# 创建全局线程池，用于执行阻塞的IO操作
thread_pool = ThreadPoolExecutor(max_workers=10)

# ==================== 日志配置 ====================

# 配置日志（仅在root无handler时配置，避免重复输出）
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('api.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 创建日志记录器
logger = logging.getLogger(__name__)

# 日志记录装饰器


def log_api_call(func):
    """API调用日志装饰器"""
    async def wrapper(*args, **kwargs):
        # 获取请求信息
        request = None
        user_payload = None

        # 从参数中提取request和payload
        for arg in args:
            if hasattr(arg, 'method') and hasattr(arg, 'url'):
                request = arg
                break

        for key, value in kwargs.items():
            if hasattr(value, 'user_id'):
                user_payload = value
                break

        # 记录请求开始
        start_time = time.time()
        request_id = str(uuid.uuid4())[:8]

        if request:
            logger.info(f"[{request_id}] API调用开始 - {request.method} {request.url.path}")
            logger.info(f"[{request_id}] 请求IP: {request.client.host if request.client else 'unknown'}")

        if user_payload:
            logger.info(f"[{request_id}] 用户信息: ID={user_payload.user_id}, 权限={user_payload.permission}")

        try:
            # 执行原函数
            result = await func(*args, **kwargs)

            # 记录成功响应
            duration = time.time() - start_time
            logger.info(f"[{request_id}] API调用成功 - 耗时: {duration:.3f}s")

            if isinstance(result, dict) and 'code' in result:
                logger.info(f"[{request_id}] 响应代码: {result['code']}")

            return result

        except Exception as e:
            # 记录错误
            duration = time.time() - start_time
            logger.error(f"[{request_id}] API调用失败 - 耗时: {duration:.3f}s")
            logger.error(f"[{request_id}] 错误信息: {str(e)}")
            logger.error(f"[{request_id}] 错误堆栈: {traceback.format_exc()}")
            raise

    return wrapper

# 简化的日志记录函数


def generate_request_id() -> str:
    """生成唯一的请求ID"""
    return str(uuid.uuid4())[:8]


def log_info(message: str, request_id: str = None, **kwargs):
    """记录信息日志"""
    prefix = f"[{request_id}] " if request_id else ""
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()]) if kwargs else ""
    full_message = f"{prefix}{message}"
    if extra_info:
        full_message += f" | {extra_info}"
    logger.info(full_message)


def log_error(message: str, error: Exception = None, request_id: str = None, **kwargs):
    """记录错误日志"""
    prefix = f"[{request_id}] " if request_id else ""
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()]) if kwargs else ""
    full_message = f"{prefix}{message}"
    if extra_info:
        full_message += f" | {extra_info}"

    logger.error(full_message)
    if error:
        logger.error(f"{prefix}错误详情: {str(error)}")
        logger.error(f"{prefix}错误堆栈: {traceback.format_exc()}")


def log_warning(message: str, request_id: str = None, **kwargs):
    """记录警告日志"""
    prefix = f"[{request_id}] " if request_id else ""
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()]) if kwargs else ""
    full_message = f"{prefix}{message}"
    if extra_info:
        full_message += f" | {extra_info}"
    logger.warning(full_message)


# ==================== 配置类 ====================


class Settings:
    """系统配置"""
    # JWT配置
    JWT_SECRET_KEY = "zhenghaoduoqian#qwy666888@#2025"
    JWT_ALGORITHM = "HS256"
    JWT_EXPIRE_HOURS = 18

    # 限流配置
    RATE_LIMIT_MAX = 30
    RATE_LIMIT_WINDOW = 60  # 秒

    # Redis配置
    REDIS_REMOTE = {
        "host": "************",
        "port": 6379,
        "db": 0,
        "password": "TicketsCache#2023",
        "decode_responses": True
    }

    REDIS_LOCAL = {
        "host": "localhost",
        "port": 6379,
        "db": 0,
        "password": "TicketsCache#2023",
        "decode_responses": True
    }

    # PostgreSQL配置
    DATABASE = {
        "host": "localhost",
        "port": 5432,
        "dbname": "user_db",
        "user": "qwyvisa",
        "password": "HAfhqh1fn0fbua8vb7v!aa"
    }

    # 百度OCR配置
    OCR_API_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/passport"
    OCR_TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token"
    OCR_CLIENT_ID = "3V7ndB0PVh8deAcz5GUsTwPQ"
    OCR_CLIENT_SECRET = "V0AnY5T3996RpYLRQeXadP9rawnWbkfN"

    # CORS配置
    CORS_ORIGINS = [
        "http://localhost:5173",
        "http://localhost:8080",
        "http://*************:5173",
        "http://*************:5555",
        "http://visa.qianwuyouvisa.com"
    ]

    # 文件路径配置
    PASSPORT_IMAGE_DIR = "passport_images"
    AVATAR_IMAGE_DIR = "avatar_images"

    # 支持的图片格式
    ALLOWED_IMAGE_EXTENSIONS = {"jpg", "jpeg", "png"}


settings = Settings()

# ==================== OCR Token 管理 ====================


class OCRTokenManager:
    """百度OCR Token管理器"""

    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.token_key = "baidu_ocr_access_token"
        self.token_expire_key = "baidu_ocr_token_expire"

    def get_access_token(self) -> str:
        """获取有效的access_token"""
        # 检查Redis中是否有有效的token
        token = self.redis_client.get(self.token_key)
        expire_time = self.redis_client.get(self.token_expire_key)

        current_time = int(time.time())

        # 如果token存在且未过期，直接返回
        if token and expire_time and int(expire_time) > current_time:
            return token

        # 否则获取新的token
        return self._refresh_token()

    def _refresh_token(self) -> str:
        """刷新access_token"""
        try:
            url = settings.OCR_TOKEN_URL
            params = {
                'client_id': settings.OCR_CLIENT_ID,
                'client_secret': settings.OCR_CLIENT_SECRET,
                'grant_type': 'client_credentials'
            }

            response = requests.post(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()

            if 'access_token' in data:
                access_token = data['access_token']
                expires_in = data.get('expires_in', 2592000)  # 默认30天

                # 提前7天刷新，所以实际存储时间为 expires_in - 7*24*3600
                actual_expire_time = int(time.time()) + expires_in - (7 * 24 * 3600)

                # 存储到Redis
                self.redis_client.set(self.token_key, access_token)
                self.redis_client.set(self.token_expire_key, actual_expire_time)

                print(f"OCR Token刷新成功，有效期至: {datetime.datetime.fromtimestamp(actual_expire_time)}")
                return access_token
            else:
                raise Exception(f"获取token失败: {data}")

        except Exception as e:
            print(f"刷新OCR Token失败: {e}")
            # 如果刷新失败，尝试返回Redis中的旧token（即使可能过期）
            old_token = self.redis_client.get(self.token_key)
            if old_token:
                print("使用旧的OCR Token")
                return old_token
            raise Exception("无法获取OCR Token")

# ==================== 数据模型 ====================


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str = Field(..., min_length=1, max_length=50)
    password: str = Field(..., min_length=1, max_length=100)


class ClientInfo(BaseModel):
    """客户信息模型"""
    name: str
    surname_pinyin: str
    firstname_pinyin: str
    dob: str
    passport: str
    passport_expire: str
    passport_image: Optional[str] = None
    avatar_image: Optional[str] = None
    gender: str
    nationality: str
    passport_date: str
    sign_location: str
    bornplace: str
    marital_status: str
    country: Optional[str] = None
    region: Optional[str] = None


class USVisaQA(BaseModel):
    """美国签证验证问题答案对"""
    DISP: str  # 问题
    VAL: str   # 答案

class USVisaClientInfo(BaseModel):
    """美国签证客户信息模型"""
    name: str
    surname_pinyin: str
    firstname_pinyin: str
    dob: str
    passport: str
    passport_expire: str
    passport_image: Optional[str] = None
    gender: str
    nationality: Optional[str] = None
    passport_date: Optional[str] = None
    # 美国签证特殊字段
    us_username: str
    us_password: str
    us_qa: List[USVisaQA]  # 验证问题和答案数组


class OrderData(BaseModel):
    """订单数据模型"""
    clients: List[ClientInfo]
    visaType: List[List[str]]
    dateRangeList: List[List[str]]
    accept_vip: bool = False
    accept_next_day: bool = False
    travel_date: str = ""
    customer: str = ""
    price: str = ""
    remark: str = ""
    auto_schedule: bool = False

    @field_validator('price', mode='before')
    @classmethod
    def convert_price_to_string(cls, v):
        """将price字段转换为字符串"""
        if v is None:
            return ""
        return str(v)

    @field_validator('travel_date', mode='before')
    @classmethod
    def format_travel_date(cls, v):
        """格式化travel_date，只保留日期部分"""
        if not v:
            return ""
        # 如果是ISO格式的日期时间字符串，只取日期部分
        if 'T' in str(v):
            return str(v).split('T')[0]
        return str(v)


class EditOrderData(OrderData):
    """编辑订单数据模型"""
    order_id: str


class USVisaUserInfoRequest(BaseModel):
    """美国签证用户信息提交请求"""
    clients: List[USVisaClientInfo]
    visaType: List[List[str]]
    dateRangeList: List[List[str]]
    travel_date: str = ""
    customer: str = ""
    remark: str = ""


# ==================== 申根签证表单数据模型 ====================

class DateDto(BaseModel):
    """日期数据传输对象"""
    year: Optional[int] = None
    month: Optional[int] = None
    day: Optional[int] = None


class Step1Dto(BaseModel):
    """第一步：基本资格信息"""
    routeId: Optional[int] = 1
    webRefNo: Optional[str] = None
    firstName: str
    lastName: str
    genderId: str
    dateOfBirth: DateDto
    nationalityId: str
    passportNumber: str
    expiryDate: DateDto
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'eligibilityCriteria'


class Step2Dto(BaseModel):
    """第二步：护照详细信息"""
    surName: str
    surnameAtBirth: Optional[str] = None
    givenName: str
    dateOfBirth: DateDto
    countryOfBirth: str
    placeOfBirth: str
    nationalityId: str
    nationalityAtBirthId: str
    genderId: str
    maritalStatusId: str
    isMinorApplicant: int
    idNumber: str
    passportTypeId: str
    passportNumber: str
    reenternumberOfPassport: str
    issueDate: DateDto
    issuedcountry: str
    expiryDate: DateDto
    issuedBy: str
    # 监护人信息（未成年申请者需要）
    nationalityGuardian: Optional[str] = None
    parentalAuthority: Optional[str] = None
    guardianSurName: Optional[str] = None
    guardianFirstName: Optional[str] = None
    guardianAddress: Optional[str] = None
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'applicantInformation'


class Step3Dto(BaseModel):
    """第三步：申请人信息"""
    applicationDate: Optional[str] = None
    applicantCountry: str
    applicantAddress: str
    occupationId: str
    occupationOthers: Optional[str] = None
    applicantEmail: str
    applicantTelephoneIsdCode: Optional[str] = '+86'
    applicantTelephoneNumber: str
    residenceOtherNationality: int
    residenceCountryPermitNo: Optional[str] = None
    residenceCountryPermitValidUntil: Optional[DateDto] = None
    employerName: Optional[str] = None
    employerAddress: Optional[str] = None
    employerMobile: Optional[str] = None
    employerCity: Optional[str] = None
    employerHomePostalCode: Optional[str] = None
    employerHomeCountry: Optional[str] = None
    fingerprintsCollected: int
    dateOfCollection: Optional[DateDto] = None
    previousApplicationNumber: Optional[str] = None
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'travelInformation'


class Step4Dto(BaseModel):
    """第四步：旅行计划信息"""
    purposeOfTravel: str
    purposeOfTravelOthers: Optional[str] = None
    purposeOfTravelAddInfo: str
    numberOfEntries: str
    isSchengenVisaIssued: int
    validFrom: Optional[DateDto] = None
    validTill: Optional[DateDto] = None
    isAdequateMedicalInsurance: int
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'accommodationInformation'


class Step5Dto(BaseModel):
    """第五步：住宿安排信息"""
    invitingPartyId: int
    nameOfOrganisation: Optional[str] = None
    addressOfOrganisation: Optional[str] = None
    nameOfEnterprise: Optional[str] = None
    addressOfEnterprise: Optional[str] = None
    postalCodeOfEnterprise: Optional[str] = None
    emailEnterprise: Optional[str] = None
    streetEnterprise: Optional[str] = None
    cityEnterprise: Optional[str] = None
    countryEnterprise: Optional[str] = None
    enterpriseTelephoneIsdCode: Optional[str] = None
    enterpriseTelephoneNumber: Optional[str] = None
    surNameOfContactInvitingPerson: Optional[str] = None
    firstNameOfContactInvitingPerson: Optional[str] = None
    addressOfInvitingPerson: Optional[str] = None
    postalCodeOfInvitingPerson: Optional[str] = None
    emailInvitingPerson: Optional[str] = None
    streetInvitingPerson: Optional[str] = None
    cityInvitingPerson: Optional[str] = None
    countryInvitingPerson: Optional[str] = None
    nameOfInvitingHotel: Optional[str] = None
    addressOfInvitingHotel: Optional[str] = None
    postalCodeOfInvitingHotel: Optional[str] = None
    emailInvitingHotel: Optional[str] = None
    streetInvitingHotel: Optional[str] = None
    cityInvitingHotel: Optional[str] = None
    countryInvitingHotel: Optional[str] = None
    invitingHotelTelephoneIsdCode: Optional[str] = None
    invitingHotelTelephoneNumber: Optional[str] = None
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'vafInformation'


class Step6Dto(BaseModel):
    """第六步：附加信息及确认"""
    finalDestination: int
    arrivalDate: DateDto
    departureDate: DateDto
    durationOfStay: str
    costOfTravellingCoveredBy: List[str]
    costOfTravellingCoveredByOthers: Optional[str] = None
    meansSupportId: List[str]
    meansSupportOthers: Optional[str] = None
    isCitizenId: int
    euSurname: Optional[str] = None
    euFirstName: Optional[str] = None
    euNationalityId: Optional[str] = None
    euDateOfBirth: Optional[DateDto] = None
    euPassportNumber: Optional[str] = None
    euRelationshipId: Optional[str] = None
    schengenStateFirstEntry: str
    countryOfDestination: List[str]
    invitingPersonCoveredCosts: int
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'ReceivedAtAC'


class SchengenVisaStepsDto(BaseModel):
    """申根签证所有步骤数据"""
    step1: Optional[Step1Dto] = None
    step2: Optional[Step2Dto] = None
    step3: Optional[Step3Dto] = None
    step4: Optional[Step4Dto] = None
    step5: Optional[Step5Dto] = None
    step6: Optional[Step6Dto] = None


class SchengenVisaApplicationRequest(BaseModel):
    """申根签证申请请求"""
    userId: Optional[int] = None
    step1: Optional[Step1Dto] = None
    step2: Optional[Step2Dto] = None
    step3: Optional[Step3Dto] = None
    step4: Optional[Step4Dto] = None
    step5: Optional[Step5Dto] = None
    step6: Optional[Step6Dto] = None


class SchengenFormData(BaseModel):
    """申根表单数据模型（兼容旧版本）"""
    form_name: str
    country: str
    applicant_name_cn: str
    applicant_name_en: str
    gender: str
    birth_date: str
    nationality: str
    marital_status: str
    passport_number: str
    passport_issue_date: str
    passport_expire_date: str
    phone: str
    email: str
    address: str
    travel_purpose: str
    visa_type: str
    entry_date: str
    exit_date: str
    travel_plan: str
    remarks: str = ""
    status: str = "draft"  # draft, submitted, completed


# ==================== 申根签证表单数据模型 ====================

class DateDto(BaseModel):
    """日期数据传输对象"""
    year: int
    month: int
    day: int


class Step1Dto(BaseModel):
    """第一步：基本资格信息"""
    routeId: Optional[int] = 1
    webRefNo: Optional[str] = None
    firstName: str
    lastName: str
    genderId: str
    dateOfBirth: DateDto
    nationalityId: str
    passportNumber: str
    expiryDate: DateDto
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'eligibilityCriteria'


class Step2Dto(BaseModel):
    """第二步：护照详细信息"""
    surName: str
    surnameAtBirth: Optional[str] = None
    givenName: str
    dateOfBirth: DateDto
    countryOfBirth: str
    placeOfBirth: str
    nationalityId: str
    nationalityAtBirthId: str
    genderId: str
    maritalStatusId: str
    isMinorApplicant: int
    idNumber: str
    passportTypeId: str
    passportNumber: str
    reenternumberOfPassport: str
    issueDate: DateDto
    issuedcountry: str
    expiryDate: DateDto
    issuedBy: str
    # 监护人信息（未成年申请者需要）
    nationalityGuardian: Optional[str] = None
    parentalAuthority: Optional[str] = None
    guardianSurName: Optional[str] = None
    guardianFirstName: Optional[str] = None
    guardianAddress: Optional[str] = None
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'applicantInformation'


class Step3Dto(BaseModel):
    """第三步：申请人信息"""
    applicationDate: Optional[str] = None
    applicantCountry: str
    applicantAddress: str
    occupationId: str
    occupationOthers: Optional[str] = None
    applicantEmail: str
    applicantTelephoneIsdCode: Optional[str] = '+86'
    applicantTelephoneNumber: str
    residenceOtherNationality: int
    residenceCountryPermitNo: Optional[str] = None
    residenceCountryPermitValidUntil: Optional[DateDto] = None
    employerName: Optional[str] = None
    employerAddress: Optional[str] = None
    employerMobile: Optional[str] = None
    employerCity: Optional[str] = None
    employerHomePostalCode: Optional[str] = None
    employerHomeCountry: Optional[str] = None
    fingerprintsCollected: int
    dateOfCollection: Optional[DateDto] = None
    previousApplicationNumber: Optional[str] = None
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'travelInformation'


class Step4Dto(BaseModel):
    """第四步：旅行计划信息"""
    purposeOfTravel: str
    purposeOfTravelOthers: Optional[str] = None
    purposeOfTravelAddInfo: str
    numberOfEntries: str
    isSchengenVisaIssued: int
    validFrom: Optional[DateDto] = None
    validTill: Optional[DateDto] = None
    isAdequateMedicalInsurance: int
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'accommodationInformation'


class Step5Dto(BaseModel):
    """第五步：住宿安排信息"""
    invitingPartyId: int
    nameOfOrganisation: Optional[str] = None
    addressOfOrganisation: Optional[str] = None
    nameOfEnterprise: Optional[str] = None
    addressOfEnterprise: Optional[str] = None
    postalCodeOfEnterprise: Optional[str] = None
    emailEnterprise: Optional[str] = None
    streetEnterprise: Optional[str] = None
    cityEnterprise: Optional[str] = None
    countryEnterprise: Optional[str] = None
    enterpriseTelephoneIsdCode: Optional[str] = None
    enterpriseTelephoneNumber: Optional[str] = None
    surNameOfContactInvitingPerson: Optional[str] = None
    firstNameOfContactInvitingPerson: Optional[str] = None
    addressOfInvitingPerson: Optional[str] = None
    postalCodeOfInvitingPerson: Optional[str] = None
    emailInvitingPerson: Optional[str] = None
    streetInvitingPerson: Optional[str] = None
    cityInvitingPerson: Optional[str] = None
    countryInvitingPerson: Optional[str] = None
    nameOfInvitingHotel: Optional[str] = None
    addressOfInvitingHotel: Optional[str] = None
    postalCodeOfInvitingHotel: Optional[str] = None
    emailInvitingHotel: Optional[str] = None
    streetInvitingHotel: Optional[str] = None
    cityInvitingHotel: Optional[str] = None
    countryInvitingHotel: Optional[str] = None
    invitingHotelTelephoneIsdCode: Optional[str] = None
    invitingHotelTelephoneNumber: Optional[str] = None
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'vafInformation'


class Step6Dto(BaseModel):
    """第六步：附加信息及确认"""
    finalDestination: int
    arrivalDate: DateDto
    departureDate: DateDto
    durationOfStay: str
    costOfTravellingCoveredBy: List[str]
    costOfTravellingCoveredByOthers: Optional[str] = None
    meansSupportId: List[str]
    meansSupportOthers: Optional[str] = None
    isCitizenId: int
    euSurname: Optional[str] = None
    euFirstName: Optional[str] = None
    euNationalityId: Optional[str] = None
    euDateOfBirth: Optional[DateDto] = None
    euPassportNumber: Optional[str] = None
    euRelationshipId: Optional[str] = None
    schengenStateFirstEntry: str
    countryOfDestination: List[str]
    invitingPersonCoveredCosts: int
    isDraft: Optional[bool] = False
    stage: Optional[str] = 'ReceivedAtAC'


class SchengenVisaStepsDto(BaseModel):
    """申根签证所有步骤数据"""
    step1: Optional[Step1Dto] = None
    step2: Optional[Step2Dto] = None
    step3: Optional[Step3Dto] = None
    step4: Optional[Step4Dto] = None
    step5: Optional[Step5Dto] = None
    step6: Optional[Step6Dto] = None


class SchengenVisaApplicationRequest(BaseModel):
    """申根签证申请请求"""
    userId: int
    step1: Optional[Step1Dto] = None
    step2: Optional[Step2Dto] = None
    step3: Optional[Step3Dto] = None
    step4: Optional[Step4Dto] = None
    step5: Optional[Step5Dto] = None
    step6: Optional[Step6Dto] = None


class UserPayload(BaseModel):
    """JWT用户载荷模型"""
    user_id: int
    username: str
    name: str
    permission: str
    exp: float


class UserCreateRequest(BaseModel):
    """创建用户请求模型"""
    username: str = Field(..., min_length=1, max_length=50)
    password: str = Field(..., min_length=6, max_length=100)
    name: str = Field(..., min_length=1, max_length=50)
    role_id: int = Field(..., description="用户角色ID", ge=1)


class UserUpdateRequest(BaseModel):
    """更新用户请求模型"""
    username: Optional[str] = Field(None, min_length=1, max_length=50)
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    permission: Optional[str] = Field(None, pattern="^(admin|kefu|user)$")
    role_id: Optional[int] = Field(None, description="用户角色ID")


class UserListQuery(BaseModel):
    """用户列表查询模型"""
    page: int = Field(1, ge=1)
    page_size: int = Field(10, ge=1, le=100)
    search: Optional[str] = None


class RoleCreateRequest(BaseModel):
    """创建角色请求模型"""
    name: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=200)
    permissions: List[str] = Field(default_factory=list)


class RoleUpdateRequest(BaseModel):
    """更新角色请求模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=200)
    permissions: Optional[List[str]] = None

# ==================== 数据库管理 ====================


class DatabaseManager:
    """数据库连接池管理器"""

    def __init__(self):
        self.pool = SimpleConnectionPool(
            1, 10,  # 最小和最大连接数
            host=settings.DATABASE["host"],
            port=settings.DATABASE["port"],
            dbname=settings.DATABASE["dbname"],
            user=settings.DATABASE["user"],
            password=settings.DATABASE["password"]
        )

    @contextmanager
    def get_db(self):
        """获取数据库连接的上下文管理器"""
        conn = self.pool.getconn()
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            self.pool.putconn(conn)


# ==================== 全局实例 ====================
db_manager = DatabaseManager()
redis_client_local = RedisClient()
ocr_token_manager = OCRTokenManager(redis_client_local)

# VAF表单下载任务状态存储
vaf_download_tasks = {}

# 预约信PDF下载任务状态存储
appointment_download_tasks = {}

# 线程池执行器，用于执行curl_cffi请求
thread_pool = ThreadPoolExecutor(max_workers=10)

# 加载BLS配置
with open("bls_config.json", "r", encoding="utf-8") as f:
    bls_config = json.load(f)

# ==================== FastAPI应用初始化 ====================
app = FastAPI(
    title="签证管理系统 API",
    description="签证订单管理系统后端服务",
    version="2.0.0"
)

# 请求计数器（用于限流）
request_counters = defaultdict(list)

# ==================== 应用启动事件 ====================


@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    try:
        # 初始化OCR Token
        print("正在初始化OCR Token...")
        access_token = ocr_token_manager.get_access_token()
        print(f"OCR Token初始化成功: {access_token[:20]}...")
    except Exception as e:
        print(f"OCR Token初始化失败: {e}")
        print("应用将继续启动，但OCR功能可能不可用")

# ==================== 中间件配置 ====================
# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Disposition"],
)

# 限流中间件


class RateLimitMiddleware(BaseHTTPMiddleware):
    """请求限流中间件"""

    # 不进行限流的路径列表
    RATE_LIMIT_EXEMPT_PATHS = [
        "/api/download_vaf_form_status/",  # VAF表单下载状态查询
        "/api/heartbeat",  # 心跳检测
        "/api/health",     # 健康检查
    ]

    async def dispatch(self, request: Request, call_next):
        client_ip = request.client.host
        path = request.url.path

        # 检查是否是豁免路径
        is_exempt = False
        for exempt_path in self.RATE_LIMIT_EXEMPT_PATHS:
            if path.startswith(exempt_path):
                is_exempt = True
                break

        # 如果是豁免路径，直接通过
        if is_exempt:
            response = await call_next(request)
            return response

        key = f"{client_ip}:{path}"
        now = time.time()
        window_start = now - settings.RATE_LIMIT_WINDOW

        # 清理过期记录
        request_counters[key] = [
            t for t in request_counters[key] if t > window_start
        ]

        # 检查限流
        if len(request_counters[key]) >= settings.RATE_LIMIT_MAX:
            return JSONResponse(
                status_code=429,
                content={"detail": "请求过于频繁，请稍后再试"}
            )

        request_counters[key].append(now)
        response = await call_next(request)
        return response


app.add_middleware(RateLimitMiddleware)

# ==================== 工具函数 ====================


def generate_order_id() -> str:
    """生成唯一订单ID"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    random_suffix = str(random.randint(10000, 99999))
    return f"{timestamp}{random_suffix}"


def create_jwt_token(user_data: dict) -> str:
    """创建JWT令牌"""
    payload = {
        **user_data,
        "exp": time.time() + settings.JWT_EXPIRE_HOURS * 3600
    }
    return jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)


def verify_jwt_token(request: Request) -> UserPayload:
    """验证JWT令牌"""
    token = request.headers.get("authorization")
    if not token:
        raise HTTPException(status_code=401, detail="未提供认证令牌")

    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=[settings.JWT_ALGORITHM]
        )
        return UserPayload(**payload)
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=403, detail="令牌已过期")
    except jwt.PyJWTError:
        raise HTTPException(status_code=403, detail="无效的令牌")


def ensure_dir_exists(directory: str):
    """确保目录存在"""
    os.makedirs(directory, exist_ok=True)


def validate_image_extension(filename: str) -> bool:
    """验证图片文件扩展名"""
    ext = filename.split('.')[-1].lower()
    return ext in settings.ALLOWED_IMAGE_EXTENSIONS


def safe_int_conversion(value: str, default: int = 0) -> int:
    """安全地将字符串转换为整数，处理空值情况"""
    if not value or value == "":
        return default
    try:
        return int(float(value))  # 先转float再转int，处理小数情况
    except (ValueError, TypeError):
        return default


def verify_admin_permission(payload: UserPayload) -> bool:
    """验证管理员权限"""
    if payload.permission != "admin":
        raise HTTPException(status_code=403, detail="需要管理员权限")
    return True


# ==================== 申根签证工具函数 ====================

def generate_application_id() -> str:
    """生成申请ID"""
    timestamp = int(time.time() * 1000)
    random_suffix = str(random.randint(100000, 999999))
    return f"SCHENGEN_{timestamp}_{random_suffix}"


def build_step1_data(row) -> Step1Dto:
    """构建第一步数据"""
    return Step1Dto(
        routeId=row[0],
        webRefNo=row[1],
        firstName=row[2],
        lastName=row[3],
        genderId=row[4],
        dateOfBirth=DateDto(year=row[5], month=row[6], day=row[7]),
        nationalityId=row[8],
        passportNumber=row[9],
        expiryDate=DateDto(year=row[10], month=row[11], day=row[12]),
        isDraft=row[13],
        stage=row[14]
    )


def build_step2_data(row) -> Step2Dto:
    """构建第二步数据"""
    return Step2Dto(
        surName=row[0],
        surnameAtBirth=row[1],
        givenName=row[2],
        dateOfBirth=DateDto(year=row[3], month=row[4], day=row[5]),
        countryOfBirth=row[6],
        placeOfBirth=row[7],
        nationalityId=row[8],
        nationalityAtBirthId=row[9],
        genderId=row[10],
        maritalStatusId=row[11],
        isMinorApplicant=row[12],
        idNumber=row[13],
        passportTypeId=row[14],
        passportNumber=row[15],
        reenternumberOfPassport=row[16],
        issueDate=DateDto(year=row[17], month=row[18], day=row[19]),
        issuedcountry=row[20],
        expiryDate=DateDto(year=row[21], month=row[22], day=row[23]),
        issuedBy=row[24],
        isDraft=row[25],
        stage=row[26]
    )


def build_step3_data(row) -> Step3Dto:
    """构建第三步数据"""
    return Step3Dto(
        applicationDate=row[0].strftime('%Y-%m-%d') if row[0] else None,
        applicantCountry=row[1],
        applicantAddress=row[2],
        occupationId=row[3],
        occupationOthers=row[4],
        applicantEmail=row[5],
        applicantTelephoneIsdCode=row[6],
        applicantTelephoneNumber=row[7],
        residenceOtherNationality=row[8],
        residenceCountryPermitNo=row[9],
        residenceCountryPermitValidUntil=DateDto(year=row[10], month=row[11], day=row[12]) if row[10] and row[11] and row[12] else None,
        employerName=row[13],
        employerAddress=row[14],
        employerMobile=row[15],
        employerCity=row[16],
        employerHomePostalCode=row[17],
        employerHomeCountry=row[18],
        fingerprintsCollected=row[19],
        dateOfCollection=DateDto(year=row[20], month=row[21], day=row[22]) if row[20] and row[21] and row[22] else None,
        previousApplicationNumber=row[23],
        isDraft=row[24],
        stage=row[25]
    )


def build_step4_data(row) -> Step4Dto:
    """构建第四步数据"""
    return Step4Dto(
        purposeOfTravel=row[0],
        purposeOfTravelOthers=row[1],
        purposeOfTravelAddInfo=row[2],
        numberOfEntries=row[3],
        isSchengenVisaIssued=row[4],
        validFrom=DateDto(year=row[5], month=row[6], day=row[7]) if row[5] and row[6] and row[7] else None,
        validTill=DateDto(year=row[8], month=row[9], day=row[10]) if row[8] and row[9] and row[10] else None,
        isAdequateMedicalInsurance=row[11],
        isDraft=row[12],
        stage=row[13]
    )


def build_step5_data(row) -> Step5Dto:
    """构建第五步数据"""
    return Step5Dto(
        invitingPartyId=row[0],
        nameOfOrganisation=row[1],
        addressOfOrganisation=row[2],
        nameOfEnterprise=row[3],
        addressOfEnterprise=row[4],
        postalCodeOfEnterprise=row[5],
        emailEnterprise=row[6],
        streetEnterprise=row[7],
        cityEnterprise=row[8],
        countryEnterprise=row[9],
        enterpriseTelephoneIsdCode=row[10],
        enterpriseTelephoneNumber=row[11],
        surNameOfContactInvitingPerson=row[12],
        firstNameOfContactInvitingPerson=row[13],
        addressOfInvitingPerson=row[14],
        postalCodeOfInvitingPerson=row[15],
        emailInvitingPerson=row[16],
        streetInvitingPerson=row[17],
        cityInvitingPerson=row[18],
        countryInvitingPerson=row[19],
        nameOfInvitingHotel=row[20],
        addressOfInvitingHotel=row[21],
        postalCodeOfInvitingHotel=row[22],
        emailInvitingHotel=row[23],
        streetInvitingHotel=row[24],
        cityInvitingHotel=row[25],
        countryInvitingHotel=row[26],
        invitingHotelTelephoneIsdCode=row[27],
        invitingHotelTelephoneNumber=row[28],
        isDraft=row[29],
        stage=row[30]
    )


def build_step6_data(row) -> Step6Dto:
    """构建第六步数据"""
    return Step6Dto(
        finalDestination=row[0],
        arrivalDate=DateDto(year=row[1], month=row[2], day=row[3]),
        departureDate=DateDto(year=row[4], month=row[5], day=row[6]),
        durationOfStay=row[7],
        costOfTravellingCoveredBy=row[8] if isinstance(row[8], list) else json.loads(row[8]) if row[8] else [],
        costOfTravellingCoveredByOthers=row[9],
        meansSupportId=row[10] if isinstance(row[10], list) else json.loads(row[10]) if row[10] else [],
        meansSupportOthers=row[11],
        isCitizenId=row[12],
        euSurname=row[13],
        euFirstName=row[14],
        euNationalityId=row[15],
        euDateOfBirth=DateDto(year=row[16], month=row[17], day=row[18]) if row[16] and row[17] and row[18] else None,
        euPassportNumber=row[19],
        euRelationshipId=row[20],
        schengenStateFirstEntry=row[21],
        countryOfDestination=row[22] if isinstance(row[22], list) else json.loads(row[22]) if row[22] else [],
        invitingPersonCoveredCosts=row[23],
        isDraft=row[24],
        stage=row[25]
    )


# ==================== 申根签证工具函数 ====================

def generate_application_id() -> str:
    """生成申请ID"""
    timestamp = int(time.time() * 1000)
    random_suffix = str(random.randint(100000, 999999))
    return f"SCHENGEN_{timestamp}_{random_suffix}"


def build_step1_data(row) -> Step1Dto:
    """构建第一步数据"""
    return Step1Dto(
        routeId=row[0],
        webRefNo=row[1],
        firstName=row[2],
        lastName=row[3],
        genderId=row[4],
        dateOfBirth=DateDto(year=row[5], month=row[6], day=row[7]),
        nationalityId=row[8],
        passportNumber=row[9],
        expiryDate=DateDto(year=row[10], month=row[11], day=row[12]),
        isDraft=row[13],
        stage=row[14]
    )


def build_step2_data(row) -> Step2Dto:
    """构建第二步数据"""
    return Step2Dto(
        surName=row[0],
        surnameAtBirth=row[1],
        givenName=row[2],
        dateOfBirth=DateDto(year=row[3], month=row[4], day=row[5]),
        countryOfBirth=row[6],
        placeOfBirth=row[7],
        nationalityId=row[8],
        nationalityAtBirthId=row[9],
        genderId=row[10],
        maritalStatusId=row[11],
        isMinorApplicant=row[12],
        idNumber=row[13],
        passportTypeId=row[14],
        passportNumber=row[15],
        reenternumberOfPassport=row[16],
        issueDate=DateDto(year=row[17], month=row[18], day=row[19]),
        issuedcountry=row[20],
        expiryDate=DateDto(year=row[21], month=row[22], day=row[23]),
        issuedBy=row[24],
        isDraft=row[25],
        stage=row[26]
    )


def build_step3_data(row) -> Step3Dto:
    """构建第三步数据"""
    return Step3Dto(
        applicationDate=row[0].strftime('%Y-%m-%d') if row[0] else None,
        applicantCountry=row[1],
        applicantAddress=row[2],
        occupationId=row[3],
        occupationOthers=row[4],
        applicantEmail=row[5],
        applicantTelephoneIsdCode=row[6],
        applicantTelephoneNumber=row[7],
        residenceOtherNationality=row[8],
        residenceCountryPermitNo=row[9],
        residenceCountryPermitValidUntil=DateDto(year=row[10], month=row[11], day=row[12]) if row[10] and row[11] and row[12] else None,
        employerName=row[13],
        employerAddress=row[14],
        employerMobile=row[15],
        employerCity=row[16],
        employerHomePostalCode=row[17],
        employerHomeCountry=row[18],
        fingerprintsCollected=row[19],
        dateOfCollection=DateDto(year=row[20], month=row[21], day=row[22]) if row[20] and row[21] and row[22] else None,
        previousApplicationNumber=row[23],
        isDraft=row[24],
        stage=row[25]
    )


def build_step4_data(row) -> Step4Dto:
    """构建第四步数据"""
    return Step4Dto(
        purposeOfTravel=row[0],
        purposeOfTravelOthers=row[1],
        purposeOfTravelAddInfo=row[2],
        numberOfEntries=row[3],
        isSchengenVisaIssued=row[4],
        validFrom=DateDto(year=row[5], month=row[6], day=row[7]) if row[5] and row[6] and row[7] else None,
        validTill=DateDto(year=row[8], month=row[9], day=row[10]) if row[8] and row[9] and row[10] else None,
        isAdequateMedicalInsurance=row[11],
        isDraft=row[12],
        stage=row[13]
    )


def build_step5_data(row) -> Step5Dto:
    """构建第五步数据"""
    return Step5Dto(
        invitingPartyId=row[0],
        nameOfOrganisation=row[1],
        addressOfOrganisation=row[2],
        nameOfEnterprise=row[3],
        addressOfEnterprise=row[4],
        postalCodeOfEnterprise=row[5],
        emailEnterprise=row[6],
        streetEnterprise=row[7],
        cityEnterprise=row[8],
        countryEnterprise=row[9],
        enterpriseTelephoneIsdCode=row[10],
        enterpriseTelephoneNumber=row[11],
        surNameOfContactInvitingPerson=row[12],
        firstNameOfContactInvitingPerson=row[13],
        addressOfInvitingPerson=row[14],
        postalCodeOfInvitingPerson=row[15],
        emailInvitingPerson=row[16],
        streetInvitingPerson=row[17],
        cityInvitingPerson=row[18],
        countryInvitingPerson=row[19],
        nameOfInvitingHotel=row[20],
        addressOfInvitingHotel=row[21],
        postalCodeOfInvitingHotel=row[22],
        emailInvitingHotel=row[23],
        streetInvitingHotel=row[24],
        cityInvitingHotel=row[25],
        countryInvitingHotel=row[26],
        invitingHotelTelephoneIsdCode=row[27],
        invitingHotelTelephoneNumber=row[28],
        isDraft=row[29],
        stage=row[30]
    )


def build_step6_data(row) -> Step6Dto:
    """构建第六步数据"""
    return Step6Dto(
        finalDestination=row[0],
        arrivalDate=DateDto(year=row[1], month=row[2], day=row[3]),
        departureDate=DateDto(year=row[4], month=row[5], day=row[6]),
        durationOfStay=row[7],
        costOfTravellingCoveredBy=row[8] if isinstance(row[8], list) else json.loads(row[8]) if row[8] else [],
        costOfTravellingCoveredByOthers=row[9],
        meansSupportId=row[10] if isinstance(row[10], list) else json.loads(row[10]) if row[10] else [],
        meansSupportOthers=row[11],
        isCitizenId=row[12],
        euSurname=row[13],
        euFirstName=row[14],
        euNationalityId=row[15],
        euDateOfBirth=DateDto(year=row[16], month=row[17], day=row[18]) if row[16] and row[17] and row[18] else None,
        euPassportNumber=row[19],
        euRelationshipId=row[20],
        schengenStateFirstEntry=row[21],
        countryOfDestination=row[22] if isinstance(row[22], list) else json.loads(row[22]) if row[22] else [],
        invitingPersonCoveredCosts=row[23],
        isDraft=row[24],
        stage=row[25]
    )

# ==================== 用户认证路由 ====================


@app.post("/user/login", summary="用户登录", tags=["认证"])
def user_login(request: LoginRequest):
    """用户登录接口"""
    request_id = str(uuid.uuid4())[:8]

    log_info("用户登录请求", request_id=request_id, username=request.username)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """SELECT id, username, permission, name
                       FROM users
                       WHERE username = %s AND password = %s""",
                    (request.username, request.password)
                )
                user = cur.fetchone()

                if not user:
                    log_warning("用户登录失败 - 用户名或密码错误", request_id=request_id, username=request.username)
                    raise HTTPException(status_code=401, detail="用户名或密码错误")

                # 更新最后登录时间
                cur.execute(
                    "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = %s",
                    (user[0],)
                )
                conn.commit()

                user_data = {
                    "user_id": user[0],
                    "username": user[1],
                    "permission": user[2],
                    "name": user[3]
                }

                token = create_jwt_token(user_data)

                log_info("用户登录成功", request_id=request_id, username=user[1], user_id=user[0], permission=user[2])

                return {
                    "code": 1,
                    "token": token,
                    "username": user[1],
                    "permission": user[2]
                }
    except HTTPException:
        raise
    except Exception as e:
        log_error("用户登录异常", error=e, request_id=request_id, username=request.username)
        raise HTTPException(status_code=500, detail=f"登录失败: {str(e)}")


@app.get("/user/info", summary="获取当前用户信息", tags=["用户"])
def get_current_user(payload: UserPayload = Depends(verify_jwt_token)):
    """获取当前登录用户信息"""
    with db_manager.get_db() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT u.id, u.username, u.name, u.permission, u.role_id, r.name as role_name, r.permissions
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE u.id = %s
            """, (payload.user_id,))
            user = cur.fetchone()

            if not user:
                raise HTTPException(status_code=404, detail="用户不存在")

            return {
                "code": 1,
                "data": {
                    "id": user[0],
                    "username": user[1],
                    "name": user[2],
                    "permission": user[3],
                    "role_id": user[4],
                    "role_name": user[5],
                    "permissions": user[6] if user[6] else []
                }
            }


# ==================== 用户管理路由 ====================


@app.get("/api/users", summary="获取用户列表", tags=["用户管理"])
def get_users(
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """获取用户列表（分页）"""
    verify_admin_permission(payload)

    # 验证分页参数
    if page < 1:
        page = 1
    if page_size < 1 or page_size > 100:
        page_size = 10

    offset = (page - 1) * page_size

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 构建查询条件
                where_clause = ""
                params = []

                if search:
                    where_clause = "WHERE u.username ILIKE %s OR u.name ILIKE %s"
                    search_pattern = f"%{search}%"
                    params.extend([search_pattern, search_pattern])

                # 查询总数
                count_sql = f"SELECT COUNT(*) FROM users u {where_clause}"
                cur.execute(count_sql, params)
                total_count = cur.fetchone()[0]

                # 查询用户列表
                sql = f"""
                    SELECT u.id, u.username, u.name, u.permission, u.created_at, u.updated_at,
                           u.role_id, r.name as role_name, u.last_login
                    FROM users u
                    LEFT JOIN roles r ON u.role_id = r.id
                    {where_clause}
                    ORDER BY u.created_at DESC
                    LIMIT %s OFFSET %s
                """
                cur.execute(sql, params + [page_size, offset])
                users = cur.fetchall()

                # 格式化返回数据
                user_list = []
                for user in users:
                    user_list.append({
                        "id": user[0],
                        "username": user[1],
                        "name": user[2],
                        "permission": user[3],
                        "created_at": user[4].strftime('%Y-%m-%d %H:%M:%S') if user[4] else None,
                        "updated_at": user[5].strftime('%Y-%m-%d %H:%M:%S') if user[5] else None,
                        "role_id": user[6],
                        "role_name": user[7],
                        "last_login": user[8].strftime('%Y-%m-%d %H:%M:%S') if user[8] else None
                    })

                return {
                    "code": 1,
                    "data": {
                        "users": user_list,
                        "pagination": {
                            "page": page,
                            "page_size": page_size,
                            "total": total_count,
                            "total_pages": (total_count + page_size - 1) // page_size
                        }
                    }
                }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")


@app.post("/api/users", summary="创建用户", tags=["用户管理"])
def create_user(
    user_data: UserCreateRequest,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """创建新用户"""
    verify_admin_permission(payload)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查用户名是否已存在
                cur.execute(
                    "SELECT id FROM users WHERE username = %s",
                    (user_data.username,)
                )
                if cur.fetchone():
                    raise HTTPException(status_code=400, detail="用户名已存在")

                # 插入新用户
                cur.execute("""
                    INSERT INTO users (username, password, name, role_id, permission, created_at)
                    VALUES (%s, %s, %s, %s,'vip', NOW())
                    RETURNING id
                """, (
                    user_data.username,
                    user_data.password,
                    user_data.name,
                    user_data.role_id
                ))

                user_id = cur.fetchone()[0]

                return {
                    "code": 1,
                    "message": "用户创建成功",
                    "data": {"user_id": user_id}
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建用户失败: {str(e)}")


@app.put("/api/users/{user_id}", summary="更新用户信息", tags=["用户管理"])
def update_user(
    user_id: int,
    user_data: UserUpdateRequest,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """更新用户信息"""
    verify_admin_permission(payload)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查用户是否存在
                cur.execute("SELECT id FROM users WHERE id = %s", (user_id,))
                if not cur.fetchone():
                    raise HTTPException(status_code=404, detail="用户不存在")

                # 构建更新语句
                update_fields = []
                update_values = []

                if user_data.username is not None:
                    # 检查用户名是否已被其他用户使用
                    cur.execute(
                        "SELECT id FROM users WHERE username = %s AND id != %s",
                        (user_data.username, user_id)
                    )
                    if cur.fetchone():
                        raise HTTPException(status_code=400, detail="用户名已被其他用户使用")

                    update_fields.append("username = %s")
                    update_values.append(user_data.username)

                if user_data.name is not None:
                    update_fields.append("name = %s")
                    update_values.append(user_data.name)

                if user_data.permission is not None:
                    update_fields.append("permission = %s")
                    update_values.append(user_data.permission)

                if user_data.role_id is not None:
                    # 验证角色是否存在
                    cur.execute("SELECT id FROM roles WHERE id = %s", (user_data.role_id,))
                    if not cur.fetchone():
                        raise HTTPException(status_code=400, detail="指定的角色不存在")

                    update_fields.append("role_id = %s")
                    update_values.append(user_data.role_id)

                if not update_fields:
                    raise HTTPException(status_code=400, detail="没有提供要更新的字段")

                # 执行更新
                update_fields.append("updated_at = NOW()")
                update_values.append(user_id)

                sql = f"""
                    UPDATE users
                    SET {', '.join(update_fields)}
                    WHERE id = %s
                """
                cur.execute(sql, update_values)

                return {
                    "code": 1,
                    "message": "用户信息更新成功"
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新用户信息失败: {str(e)}")


@app.post("/api/mark_order_paid", summary="标记订单已支付", tags=["订单"])
# ==================== VFS 预约信下载（本地文件） ====================
@app.get("/api/vfs/orders/{order_id}/appointment-pdf")
def download_vfs_appointment_pdf(order_id: str, payload: UserPayload = Depends(verify_jwt_token)):
    """下载已保存到服务器本地的 VFS 预约信PDF。
    前提：payed_pdf_worker 已将文件保存至 vfs/appointment_pdfs，并写入 appointment_pdfs 表。
    """
    try:
        # 授权检查：管理员/客服放行；其他用户需是该订单的创建者或负责人
        if payload.permission not in ("admin", "kefu"):
            with db_manager.get_db() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        "SELECT 1 FROM orders WHERE order_id = %s AND operator = %s LIMIT 1",
                        (order_id, payload.user_id),
                    )
                    if not cur.fetchone():
                        raise HTTPException(status_code=403, detail="无权限下载该订单的预约信")

        base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__),  'appointment_pdfs'))
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT pdf_filename FROM appointment_pdfs WHERE order_id = %s", (order_id,))
                row = cur.fetchone()
                if not row or not row[0]:
                    raise HTTPException(status_code=404, detail="未找到预约信文件记录")
                filename = row[0]
        # 组装绝对路径并做路径安全校验
        file_path = os.path.abspath(os.path.join(base_dir, filename))
        if not file_path.startswith(base_dir) or not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="服务器上未找到预约信文件")
        # 增加下载次数
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "UPDATE appointment_pdfs SET download_count = COALESCE(download_count, 0) + 1, updated_at = NOW() WHERE order_id = %s",
                    (order_id,)
                )
        # 返回文件
        return FileResponse(file_path, media_type="application/pdf", filename=filename)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")


def mark_order_paid(
    request: dict,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """标记订单为已支付状态"""
    # 验证管理员权限
    if payload.permission not in ("admin", "kefu"):
        raise HTTPException(status_code=403, detail="权限不足")

    order_id = request.get("order_id")
    if not order_id:
        raise HTTPException(status_code=400, detail="订单ID不能为空")

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查订单是否存在且状态为待支付
                cur.execute(
                    "SELECT order_status FROM orders WHERE order_id = %s",
                    (order_id,)
                )
                result = cur.fetchone()

                if not result:
                    raise HTTPException(status_code=404, detail="订单不存在")

                if result[0] != 'wait_pay':
                    raise HTTPException(status_code=400, detail="订单状态不是待支付")

                # 更新订单状态为pending（待预约）
                cur.execute(
                    "UPDATE orders SET order_status = 'pending', updated_at = CURRENT_TIMESTAMP WHERE order_id = %s",
                    (order_id,)
                )

                conn.commit()

                return {
                    "code": 1,
                    "message": "订单已标记为已支付"
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"标记支付失败: {str(e)}")


@app.delete("/api/users/{user_id}", summary="删除用户", tags=["用户管理"])
def delete_user(
    user_id: int,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """删除用户"""
    verify_admin_permission(payload)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查用户是否存在
                cur.execute("SELECT id, username FROM users WHERE id = %s", (user_id,))
                user = cur.fetchone()
                if not user:
                    raise HTTPException(status_code=404, detail="用户不存在")

                # 不允许删除自己
                if user_id == payload.user_id:
                    raise HTTPException(status_code=400, detail="不能删除自己的账户")

                # 删除用户
                cur.execute("DELETE FROM users WHERE id = %s", (user_id,))

                return {
                    "code": 1,
                    "message": f"用户 {user[1]} 删除成功"
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")


@app.post("/api/users/{user_id}/reset-password", summary="重置用户密码", tags=["用户管理"])
def reset_user_password(
    user_id: int,
    data: dict,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """重置用户密码"""
    verify_admin_permission(payload)

    new_password = data.get("new_password")
    if not new_password:
        raise HTTPException(status_code=400, detail="缺少新密码")

    if len(new_password) < 6:
        raise HTTPException(status_code=400, detail="密码长度不能少于6位")

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查用户是否存在
                cur.execute("SELECT id, username FROM users WHERE id = %s", (user_id,))
                user = cur.fetchone()
                if not user:
                    raise HTTPException(status_code=404, detail="用户不存在")

                # 更新密码
                cur.execute("""
                    UPDATE users
                    SET password = %s, updated_at = NOW()
                    WHERE id = %s
                """, (new_password, user_id))

                return {
                    "code": 1,
                    "message": f"用户 {user[1]} 的密码重置成功"
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置密码失败: {str(e)}")

# ==================== 角色管理路由 ====================


@app.get("/api/roles", summary="获取角色列表", tags=["角色管理"])
def get_roles(
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """获取角色列表（分页）"""
    verify_admin_permission(payload)

    # 验证分页参数
    if page < 1:
        page = 1
    if page_size < 1 or page_size > 100:
        page_size = 10

    offset = (page - 1) * page_size

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 构建查询条件
                where_clause = ""
                params = []

                if search:
                    where_clause = "WHERE name ILIKE %s OR description ILIKE %s"
                    search_pattern = f"%{search}%"
                    params.extend([search_pattern, search_pattern])

                # 查询总数
                count_sql = f"SELECT COUNT(*) FROM roles {where_clause}"
                cur.execute(count_sql, params)
                total_count = cur.fetchone()[0]

                # 查询角色列表，并统计每个角色的用户数量
                sql = f"""
                    SELECT r.id, r.name, r.description, r.permissions, r.created_at, r.updated_at,
                           COUNT(u.id) as user_count
                    FROM roles r
                    LEFT JOIN users u ON r.id = u.role_id
                    {where_clause}
                    GROUP BY r.id, r.name, r.description, r.permissions, r.created_at, r.updated_at
                    ORDER BY r.created_at DESC
                    LIMIT %s OFFSET %s
                """
                cur.execute(sql, params + [page_size, offset])
                roles = cur.fetchall()

                # 格式化返回数据
                role_list = []
                for role in roles:
                    role_list.append({
                        "id": role[0],
                        "name": role[1],
                        "description": role[2],
                        "permissions": role[3] if role[3] else [],
                        "created_at": role[4].strftime('%Y-%m-%d %H:%M:%S') if role[4] else None,
                        "updated_at": role[5].strftime('%Y-%m-%d %H:%M:%S') if role[5] else None,
                        "user_count": role[6]
                    })

                return {
                    "code": 1,
                    "data": {
                        "roles": role_list,
                        "pagination": {
                            "page": page,
                            "page_size": page_size,
                            "total": total_count,
                            "total_pages": (total_count + page_size - 1) // page_size
                        }
                    }
                }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色列表失败: {str(e)}")


@app.post("/api/roles", summary="创建角色", tags=["角色管理"])
def create_role(
    role_data: RoleCreateRequest,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """创建新角色"""
    verify_admin_permission(payload)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查角色名是否已存在
                cur.execute(
                    "SELECT id FROM roles WHERE name = %s",
                    (role_data.name,)
                )
                if cur.fetchone():
                    raise HTTPException(status_code=400, detail="角色名已存在")

                # 插入新角色
                cur.execute("""
                    INSERT INTO roles (name, description, permissions, created_at)
                    VALUES (%s, %s, %s, NOW())
                    RETURNING id
                """, (
                    role_data.name,
                    role_data.description,
                    json.dumps(role_data.permissions)
                ))

                role_id = cur.fetchone()[0]

                return {
                    "code": 1,
                    "message": "角色创建成功",
                    "data": {"role_id": role_id}
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建角色失败: {str(e)}")


@app.put("/api/roles/{role_id}", summary="更新角色", tags=["角色管理"])
def update_role(
    role_id: int,
    role_data: RoleUpdateRequest,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """更新角色信息"""
    verify_admin_permission(payload)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查角色是否存在
                cur.execute("SELECT id FROM roles WHERE id = %s", (role_id,))
                if not cur.fetchone():
                    raise HTTPException(status_code=404, detail="角色不存在")

                # 构建更新语句
                update_fields = []
                update_values = []

                if role_data.name is not None:
                    # 检查角色名是否已被其他角色使用
                    cur.execute(
                        "SELECT id FROM roles WHERE name = %s AND id != %s",
                        (role_data.name, role_id)
                    )
                    if cur.fetchone():
                        raise HTTPException(status_code=400, detail="角色名已被其他角色使用")

                    update_fields.append("name = %s")
                    update_values.append(role_data.name)

                if role_data.description is not None:
                    update_fields.append("description = %s")
                    update_values.append(role_data.description)

                if role_data.permissions is not None:
                    update_fields.append("permissions = %s")
                    update_values.append(json.dumps(role_data.permissions))

                if not update_fields:
                    raise HTTPException(status_code=400, detail="没有提供要更新的字段")

                # 执行更新
                update_fields.append("updated_at = NOW()")
                update_values.append(role_id)

                sql = f"""
                    UPDATE roles
                    SET {', '.join(update_fields)}
                    WHERE id = %s
                """
                cur.execute(sql, update_values)

                return {
                    "code": 1,
                    "message": "角色信息更新成功"
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新角色信息失败: {str(e)}")


@app.delete("/api/roles/{role_id}", summary="删除角色", tags=["角色管理"])
def delete_role(
    role_id: int,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """删除角色"""
    verify_admin_permission(payload)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查角色是否存在
                cur.execute("SELECT id, name FROM roles WHERE id = %s", (role_id,))
                role = cur.fetchone()
                if not role:
                    raise HTTPException(status_code=404, detail="角色不存在")

                # 检查是否有用户正在使用此角色
                cur.execute("SELECT COUNT(*) FROM users WHERE role_id = %s", (role_id,))
                user_count = cur.fetchone()[0]
                if user_count > 0:
                    raise HTTPException(
                        status_code=400,
                        detail=f"无法删除角色 '{role[1]}'，仍有 {user_count} 个用户正在使用此角色"
                    )

                # 删除角色
                cur.execute("DELETE FROM roles WHERE id = %s", (role_id,))

                return {
                    "code": 1,
                    "message": f"角色 '{role[1]}' 删除成功"
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除角色失败: {str(e)}")

# ==================== 订单管理路由 ====================


@app.get("/api/get_orders", summary="获取订单列表", tags=["订单"])
def get_order_list(payload: UserPayload = Depends(verify_jwt_token)):
    """获取订单列表"""
    with db_manager.get_db() as conn:
        with conn.cursor() as cur:
            # 根据权限查询订单（排除已删除的订单）
            if payload.permission in ("admin", "kefu"):
                cur.execute("""
                    SELECT o.order_id, o.created_at, o.status, o.updated_at, o.accept_vip,
                           o.accept_next_day, o.travel_date, o.customer, o.price, o.remark,
                           o.operator, u.name as operator_name, o.order_status
                    FROM orders o
                    LEFT JOIN users u ON o.operator = u.id
                    WHERE o.order_status != 'deleted'
                    ORDER BY o.created_at DESC
                """)
            else:
                cur.execute("""
                    SELECT order_id, created_at, status, updated_at, accept_vip,
                           accept_next_day, travel_date, customer, price, remark,
                           operator, NULL as operator_name, order_status
                    FROM orders
                    WHERE operator = %s AND order_status != 'deleted'
                    ORDER BY created_at DESC
                """, (payload.user_id,))

            orders = cur.fetchall()
            result = []

            for order in orders:
                order_id = order[0]

                # 获取客户信息
                cur.execute("""
                    SELECT name, surname_pinyin, firstname_pinyin, dob, passport,
                           passport_expire, passport_image, avatar_image, gender,
                           nationality, passport_date, sign_location, bornplace,
                           marital_status, country, region
                    FROM clients WHERE order_id = %s
                """, (order_id,))
                clients = [
                    dict(zip([desc.name for desc in cur.description], row))
                    for row in cur.fetchall()
                ]

                # 获取签证类型
                cur.execute("""
                    SELECT mission_code, center_code, visa_type, visa_code
                    FROM visa_types WHERE order_id = %s
                """, (order_id,))
                visa_types = cur.fetchall()

                # 获取日期范围
                cur.execute("""
                    SELECT start_date, end_date
                    FROM date_ranges WHERE order_id = %s
                """, (order_id,))
                date_ranges = cur.fetchall()

                order_result = {
                    "order_id": order_id,
                    "created_at": order[1].strftime('%Y-%m-%d %H:%M:%S') if order[1] else None,
                    "updated_at": order[3].strftime('%Y-%m-%d %H:%M:%S') if order[3] else None,
                    "status": order[2],
                    "accept_vip": order[4],
                    "accept_next_day": order[5],
                    "travel_date": order[6],
                    "customer": order[7],
                    "price": str(order[8]) if order[8] is not None else "",
                    "remark": order[9],
                    "clients": clients,
                    "visaType": visa_types,
                    "dateRangeList": date_ranges
                }

                # 如果是管理员或客服，添加操作员信息
                if payload.permission in ("admin", "kefu"):
                    order_result["operator"] = order[10]  # operator user_id
                    order_result["operator_name"] = order[11]  # operator name
                    order_result["order_status"] = order[12]  # order_status
                else:
                    order_result["order_status"] = order[12]  # order_status

                result.append(order_result)

            return {"code": 1, "data": result}


def _get_order_details(cur, order_id):
    """获取订单详细信息的通用函数"""
    # 获取客户信息
    cur.execute("""
        SELECT name, surname_pinyin, firstname_pinyin, dob, passport,
               passport_expire, passport_image, avatar_image, gender,
               nationality, passport_date, sign_location, bornplace,
               marital_status, country, region
        FROM clients WHERE order_id = %s
    """, (order_id,))
    clients = [
        dict(zip([desc.name for desc in cur.description], row))
        for row in cur.fetchall()
    ]

    # 获取签证类型
    cur.execute("""
        SELECT mission_code, center_code, visa_type, visa_code
        FROM visa_types WHERE order_id = %s
    """, (order_id,))
    visa_types = cur.fetchall()

    # 获取日期范围
    cur.execute("""
        SELECT start_date, end_date
        FROM date_ranges WHERE order_id = %s
    """, (order_id,))
    date_ranges = cur.fetchall()

    return clients, visa_types, date_ranges


@app.get("/api/get_pending_orders", summary="获取待预约订单列表", tags=["订单"])
def get_pending_orders(payload: UserPayload = Depends(verify_jwt_token)):
    """获取待预约的订单：registe_error, schedule_error, registe_success, pending"""
    request_id = str(uuid.uuid4())[:8]

    log_info("获取待预约订单列表请求",
             request_id=request_id,
             user_id=payload.user_id,
             username=payload.username,
             permission=payload.permission)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                pending_statuses = ['wait_registe', 'registe_error', 'schedule_error', 'registe_success', 'avatar_not_available', 'pending', 'pause', 'wait_active', 'urn_create_error', 'waitlisted', 'waitlist_available']

                if payload.permission in ("admin", "kefu"):
                    cur.execute("""
                        SELECT o.order_id, o.created_at, o.status, o.updated_at, o.accept_vip,
                               o.accept_next_day, o.travel_date, o.customer, o.price, o.remark, o.order_status,
                               u.name as operator_name , o.is_vfs_order, o.auto_schedule
                        FROM orders o
                        LEFT JOIN users u ON o.operator = u.id
                        WHERE o.order_status = ANY(%s)
                        ORDER BY o.created_at DESC
                    """, (pending_statuses,))
                else:
                    cur.execute("""
                        SELECT o.order_id, o.created_at, o.status, o.updated_at, o.accept_vip,
                               o.accept_next_day, o.travel_date, o.customer, o.price, o.remark, o.order_status,
                               u.name as operator_name , o.is_vfs_order, o.auto_schedule
                        FROM orders o
                        LEFT JOIN users u ON o.operator = u.id
                        WHERE o.operator = %s AND o.order_status = ANY(%s)
                        ORDER BY o.created_at DESC
                    """, (payload.user_id, pending_statuses))

                orders = cur.fetchall()
                result = []

                for order in orders:
                    order_id = order[0]
                    clients, visa_types, date_ranges = _get_order_details(cur, order_id)

                    result.append({
                        "order_id": order_id,
                        "created_at": order[1].strftime('%Y-%m-%d %H:%M:%S') if order[1] else None,
                        "updated_at": order[3].strftime('%Y-%m-%d %H:%M:%S') if order[3] else None,
                        "status": order[2],
                        "order_status": order[10],
                        "accept_vip": order[4],
                        "accept_next_day": order[5],
                        "travel_date": order[6],
                        "customer": order[7],
                        "price": str(order[8]) if order[8] is not None else "",
                        "remark": order[9],
                        "operator_name": order[11] or "未知",
                        "clients": clients,
                        "visaType": visa_types,
                        "dateRangeList": date_ranges,
                        "is_vfs_order": order[12],
                        "auto_schedule": order[13]
                    })

                log_info("获取待预约订单列表成功",
                         request_id=request_id,
                         user_id=payload.user_id,
                         orders_count=len(result))

                return {"code": 1, "data": result}

    except Exception as e:
        log_error("获取待预约订单列表失败",
                  error=e,
                  request_id=request_id,
                  user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=f"获取订单列表失败: {str(e)}")


@app.get("/api/get_payment_orders", summary="获取待支付订单列表", tags=["订单"])
def get_payment_orders(payload: UserPayload = Depends(verify_jwt_token)):
    """获取待支付的订单：waiting_for_payment"""
    request_id = str(uuid.uuid4())[:8]

    log_info("获取待支付订单列表请求",
             request_id=request_id,
             user_id=payload.user_id,
             username=payload.username,
             permission=payload.permission)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                if payload.permission in ("admin", "kefu"):
                    cur.execute("""
                        SELECT o.order_id, o.created_at, o.status, o.updated_at, o.accept_vip,
                            o.accept_next_day, o.travel_date, o.customer, o.price, o.remark, o.order_status,
                            u.name as operator_name,
                            -- 如果 ap.appointment_date 是 NULL，则使用 o.travel_date
                            COALESCE(ap.appointment_date, o.appointment_date) AS appointment_date,
                            -- 如果 ap.appointment_time 是 NULL，则使用 o.appointment_date (请替换为实际的备用时间字段)
                            COALESCE(ap.appointment_time, o.appointment_time) AS appointment_time -- 假设一个默认时间
                        FROM orders o
                        LEFT JOIN users u ON o.operator = u.id
                        LEFT JOIN appointment_pdfs ap ON o.order_id = ap.order_id
                        WHERE o.order_status = 'waiting_for_payment'
                        ORDER BY o.created_at DESC;
                    """)
                else:
                    cur.execute("""
                        SELECT o.order_id, o.created_at, o.status, o.updated_at, o.accept_vip,
                            o.accept_next_day, o.travel_date, o.customer, o.price, o.remark, o.order_status,
                            u.name as operator_name,
                            -- If ap.appointment_date is NULL, use o.travel_date as a fallback
                            COALESCE(ap.appointment_date, o.appointment_date) AS appointment_date,
                            -- If ap.appointment_time is NULL, use a default value (replace if you have a fallback column)
                            COALESCE(ap.appointment_time, o.appointment_time) AS appointment_time
                        FROM orders o
                        LEFT JOIN users u ON o.operator = u.id
                        LEFT JOIN appointment_pdfs ap ON o.order_id = ap.order_id
                        WHERE o.operator = %s AND o.order_status = 'waiting_for_payment'
                        ORDER BY o.created_at DESC;
                    """, (payload.user_id,))

                orders = cur.fetchall()
                result = []

                for order in orders:
                    order_id = order[0]
                    clients, visa_types, date_ranges = _get_order_details(cur, order_id)

                    # 获取支付二维码
                    cur.execute("""
                        SELECT payment_qrcode FROM payment_qrcodes WHERE order_id = %s
                    """, (order_id,))
                    qrcode_result = cur.fetchone()
                    payment_qrcode = qrcode_result[0] if qrcode_result else ""

                    result.append({
                        "order_id": order_id,
                        "created_at": order[1].strftime('%Y-%m-%d %H:%M:%S') if order[1] else None,
                        "updated_at": order[3].strftime('%Y-%m-%d %H:%M:%S') if order[3] else None,
                        "status": order[2],
                        "order_status": order[10],
                        "accept_vip": order[4],
                        "accept_next_day": order[5],
                        "travel_date": order[6],
                        "customer": order[7],
                        "price": str(order[8]) if order[8] is not None else "",
                        "remark": order[9],
                        "operator_name": order[11] or "未知",
                        "appointment_date": order[12] if order[12] else "",
                        "appointment_time": order[13] if order[13] else "",
                        "clients": clients,
                        "visaType": visa_types,
                        "dateRangeList": date_ranges,
                        "payment_qrcode": payment_qrcode
                    })

                log_info("获取待支付订单列表成功",
                         request_id=request_id,
                         user_id=payload.user_id,
                         orders_count=len(result))

                return {"code": 1, "data": result}

    except Exception as e:
        log_error("获取待支付订单列表失败",
                  error=e,
                  request_id=request_id,
                  user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=f"获取订单列表失败: {str(e)}")


@app.get("/api/get_completed_orders", summary="获取已完成订单列表（分页）", tags=["订单"])
def get_completed_orders(
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    country: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    operators: Optional[str] = None,
    statuses: Optional[str] = None,
    sort_by: Optional[str] = None,
    payload: UserPayload = Depends(verify_jwt_token),
):
    """获取已完成的订单（服务端分页）"""
    request_id = str(uuid.uuid4())[:8]

    log_info(
        "获取已完成订单列表请求",
        request_id=request_id,
        user_id=payload.user_id,
        username=payload.username,
        permission=payload.permission,
        page=page,
        page_size=page_size,
    )

    # 参数规范化
    if page < 1:
        page = 1
    if page_size < 10:
        page_size = 10
    if page_size > 100:
        page_size = 100
    offset = (page - 1) * page_size

    completed_statuses = [
        'appointment_downloaded', 'appointment_canceled', 'account_deleted', 'payment_failed', 'payed'
    ]

    # 解析前端传入的状态，限定在完成态范围内
    if statuses:
        req_status_list = [s.strip() for s in statuses.split(',') if s.strip()]
        filter_statuses = [s for s in req_status_list if s in completed_statuses]
        if not filter_statuses:
            filter_statuses = completed_statuses
    else:
        filter_statuses = completed_statuses

    # 排序映射
    order_map = {
        "updated_at_desc": "o.updated_at DESC",
        "updated_at_asc": "o.updated_at ASC",
        "appointment_date_desc": "o.appointment_date DESC",
        "appointment_date_asc": "o.appointment_date ASC",
    }
    order_by = order_map.get(sort_by or "", "o.updated_at DESC")

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                where_clauses = ["o.order_status = ANY(%s)"]
                params: List[Any] = [filter_statuses]

                # 权限过滤
                if payload.permission not in ("admin", "kefu"):
                    where_clauses.append("o.operator = %s")
                    params.append(payload.user_id)

                # 国家过滤
                if country:
                    where_clauses.append("o.mission_code = %s")
                    params.append(country)

                # 操作员过滤（operator_name 列表，逗号分隔）
                if operators:
                    op_list = [op.strip() for op in operators.split(',') if op.strip()]
                    if op_list:
                        where_clauses.append("o.operator_name = ANY(%s)")
                        params.append(op_list)

                # 日期过滤（完成时间 updated_at）
                if start_date and end_date:
                    where_clauses.append("DATE(o.updated_at) BETWEEN %s AND %s")
                    params.extend([start_date, end_date])
                elif start_date:
                    where_clauses.append("DATE(o.updated_at) >= %s")
                    params.append(start_date)
                elif end_date:
                    where_clauses.append("DATE(o.updated_at) <= %s")
                    params.append(end_date)

                # 关键词搜索：订单号/客户/操作员
                if search:
                    like = f"%{search}%"
                    where_clauses.append("(o.order_id ILIKE %s OR o.customer ILIKE %s OR o.operator_name ILIKE %s)")
                    params.extend([like, like, like])

                where_sql = " AND ".join(where_clauses)

                # 统计总数
                count_sql = f"""
                    SELECT COUNT(*)
                    FROM orders o
                    LEFT JOIN appointment_pdfs ap ON o.order_id = ap.order_id
                    WHERE {where_sql}
                """
                cur.execute(count_sql, params)
                total_count = cur.fetchone()[0]

                # 查询列表
                list_sql = f"""
                    SELECT o.order_id, o.created_at, o.status, o.updated_at, o.accept_vip,
                           o.accept_next_day, o.travel_date, o.customer, o.price, o.remark, o.order_status,
                           o.operator_name, o.appointment_date, o.appointment_time, ap.appointment_date, ap.appointment_time
                    FROM orders o
                    LEFT JOIN appointment_pdfs ap ON o.order_id = ap.order_id
                    WHERE {where_sql}
                    ORDER BY {order_by}
                    LIMIT %s OFFSET %s
                """
                cur.execute(list_sql, params + [page_size, offset])
                rows = cur.fetchall()

                result = []
                for row in rows:
                    order_id = row[0]
                    clients, visa_types, date_ranges = _get_order_details(cur, order_id)
                    result.append({
                        "order_id": order_id,
                        "created_at": row[1].strftime('%Y-%m-%d %H:%M:%S') if row[1] else None,
                        "updated_at": row[3].strftime('%Y-%m-%d %H:%M:%S') if row[3] else None,
                        "status": row[2],
                        "order_status": row[10],
                        "accept_vip": row[4],
                        "accept_next_day": row[5],
                        "travel_date": row[6],
                        "customer": row[7],
                        "price": str(row[8]) if row[8] is not None else "",
                        "remark": row[9],
                        "operator_name": row[11] or "未知",
                        "appointment_date": row[12] if row[12] else row[14],
                        "appointment_time": row[13] if row[13] else row[15],
                        "clients": clients,
                        "visaType": visa_types,
                        "dateRangeList": date_ranges,
                    })

                # 获取操作员列表（不受 operators 参数限制，但遵循其余过滤条件）
                op_params = params.copy()
                op_clauses = [c for c in where_clauses if not c.startswith("o.operator_name = ANY")]
                op_where_sql = " AND ".join(op_clauses) if op_clauses else "TRUE"
                op_sql = f"""
                    SELECT DISTINCT o.operator_name
                    FROM orders o LEFT JOIN appointment_pdfs ap ON o.order_id = ap.order_id
                    WHERE {op_where_sql}
                """
                cur.execute(op_sql, op_params)
                operator_names = [r[0] for r in cur.fetchall() if r[0]]

                log_info(
                    "获取已完成订单列表成功",
                    request_id=request_id,
                    user_id=payload.user_id,
                    orders_count=len(result),
                    total=total_count,
                )

                return {
                    "code": 1,
                    "data": {
                        "items": result,
                        "total": total_count,
                        "page": page,
                        "page_size": page_size,
                        "extra": {"operators": operator_names},
                    },
                }

    except Exception as e:
        log_error(
            "获取已完成订单列表失败",
            error=e,
            request_id=request_id,
            user_id=payload.user_id,
        )
        raise HTTPException(status_code=500, detail=f"获取订单列表失败: {str(e)}")


@app.post("/api/export_completed_orders", summary="导出已完成订单（xlsx）", tags=["订单"])
async def export_completed_orders(request: Request, payload: UserPayload = Depends(verify_jwt_token)):
    """按与列表相同的筛选/排序条件导出已完成订单为 xlsx 文件"""
    try:
        data = await request.json()
        if not isinstance(data, dict):
            data = {}
    except Exception:
        data = {}

    page = int(data.get("page", 1) or 1)
    page_size = int(data.get("page_size", 20) or 20)
    search = data.get("search")
    country = data.get("country")
    start_date = data.get("start_date")
    end_date = data.get("end_date")
    operators = data.get("operators")
    statuses = data.get("statuses")
    sort_by = data.get("sort_by")

    completed_statuses = [
        'appointment_downloaded', 'appointment_canceled', 'account_deleted', 'payment_failed', 'payed'
    ]
    if statuses:
        req_status_list = [s.strip() for s in statuses.split(',') if s.strip()]
        filter_statuses = [s for s in req_status_list if s in completed_statuses]
        if not filter_statuses:
            filter_statuses = completed_statuses
    else:
        filter_statuses = completed_statuses

    order_map = {
        "updated_at_desc": "o.updated_at DESC",
        "updated_at_asc": "o.updated_at ASC",
        "appointment_date_desc": "o.appointment_date DESC",
        "appointment_date_asc": "o.appointment_date ASC",
    }
    order_by = order_map.get(sort_by or "", "o.updated_at DESC")

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                where_clauses = ["o.order_status = ANY(%s)"]
                params: List[Any] = [filter_statuses]

                if payload.permission not in ("admin", "kefu"):
                    where_clauses.append("o.operator = %s")
                    params.append(payload.user_id)

                if country:
                    where_clauses.append("o.mission_code = %s")
                    params.append(country)

                if operators:
                    op_list = [op.strip() for op in operators.split(',') if op.strip()]
                    if op_list:
                        where_clauses.append("o.operator_name = ANY(%s)")
                        params.append(op_list)

                if start_date and end_date:
                    where_clauses.append("DATE(o.updated_at) BETWEEN %s AND %s")
                    params.extend([start_date, end_date])
                elif start_date:
                    where_clauses.append("DATE(o.updated_at) >= %s")
                    params.append(start_date)
                elif end_date:
                    where_cla_clause = "DATE(o.updated_at) <= %s"
                    where_clauses.append(where_cla_clause)
                    params.append(end_date)

                if search:
                    like = f"%{search}%"
                    where_clauses.append("(o.order_id ILIKE %s OR o.customer ILIKE %s OR o.operator_name ILIKE %s)")
                    params.extend([like, like, like])

                where_sql = " AND ".join(where_clauses)

                sql = f"""
                    SELECT o.order_id, o.created_at, o.status, o.updated_at, o.accept_vip,
                           o.accept_next_day, o.travel_date, o.customer, o.price, o.remark, o.order_status,
                           o.operator_name, o.appointment_date, o.appointment_time, ap.appointment_date, ap.appointment_time
                    FROM orders o
                    LEFT JOIN appointment_pdfs ap ON o.order_id = ap.order_id
                    WHERE {where_sql}
                    ORDER BY {order_by}
                """
                cur.execute(sql, params)
                rows = cur.fetchall()

                # 生成工作簿
                wb = Workbook()
                ws = wb.active
                ws.title = "已完成订单"

                headers = ["完成时间", "抢号日期", "姓名", "领区", "对接", "来源", "单价", "人数"]
                ws.append(headers)

                for row in rows:
                    order_id = row[0]
                    clients, visa_types, _date_ranges = _get_order_details(cur, order_id)
                    # 客户姓名
                    names = "、".join([c.get("name") or "未知" for c in clients]) or "未知"
                    # 领区（先简单拼接 visa_types 数组每项）
                    visa_display = "、".join(["-".join([str(v) for v in vt if v]) for vt in visa_types]) or "未知"
                    # 人数
                    person_count = len(clients) or 1

                    finish_time = row[3].strftime('%Y-%m-%d %H:%M:%S') if row[3] else None
                    appoint_date = row[12] or row[14]

                    ws.append([
                        finish_time or "",
                        appoint_date or "",
                        names,
                        visa_display,
                        row[11] or "未知",  # operator_name
                        row[7] or "",      # customer 来源
                        str(row[8]) if row[8] is not None else "",  # price 单价
                        person_count,
                    ])

                # 写入到内存
                stream = io.BytesIO()
                wb.save(stream)
                stream.seek(0)

                filename_cn = f"已完成订单_{datetime.datetime.now().strftime('%Y%m%d')}.xlsx"
                filename_ascii = f"orders_{datetime.datetime.now().strftime('%Y%m%d')}.xlsx"
                content_disposition = (
                    f"attachment; filename={filename_ascii}; filename*=UTF-8''{quote(filename_cn)}"
                )
                return StreamingResponse(
                    stream,
                    media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    headers={
                        "Content-Disposition": content_disposition
                    },
                )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


@app.post("/api/add_order", summary="创建订单", tags=["订单"])
def create_order(order_data: OrderData, payload: UserPayload = Depends(verify_jwt_token)):
    """创建新订单"""
    request_id = str(uuid.uuid4())[:8]

    # 记录订单创建请求
    client_names = [client.name for client in order_data.clients]
    visa_types = [f"{visa[0]}-{visa[2]}" for visa in order_data.visaType]

    log_info("创建订单请求",
             request_id=request_id,
             operator=payload.username,
             operator_id=payload.user_id,
             clients=",".join(client_names),
             visa_types=",".join(visa_types),
             travel_date=order_data.travel_date)

    # 检查是否包含西班牙签证，如果有则检查Redis中是否已存在该客户
    spain_visa = next(
        (visa for visa in order_data.visaType if "spain" in visa),
        None
    )

    if spain_visa:
        client = order_data.clients[0]
        center_code = spain_visa[1]
        redis_key = f"{center_code}-{client.passport}"

        log_info("检查西班牙签证重复申请",
                 request_id=request_id,
                 passport=client.passport,
                 center_code=center_code)

        # 检查Redis中是否已存在该客户
        existing_data = redis_client_local.hget('spainUserDatas', redis_key)
        if existing_data:
            log_warning("西班牙签证重复申请",
                        request_id=request_id,
                        passport=client.passport,
                        center_code=center_code)
            return {"code": 0, "message": "该客户的西班牙签证申请已存在，请勿重复提交"}

    order_id = generate_order_id()
    log_info("生成订单号", request_id=request_id, order_id=order_id)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 插入订单主表

                visatype = order_data.visaType[0]

                is_vfs_order = True if visatype[0] and "spain" not in visatype[0].lower() else False

                cur.execute("""
                    INSERT INTO orders (
                        order_id, operator, created_at, status, accept_vip,
                        accept_next_day, travel_date, operator_name, customer,
                        price, remark, order_status, is_vfs_order, mission_code, center_code, visa_type, visa_code, auto_schedule
                    ) VALUES (
                        %s, %s, NOW(), 'pending', %s, %s, %s, %s, %s, %s, %s, 'wait_registe', %s, %s, %s, %s, %s, %s
                    )
                """, (
                    order_id, payload.user_id, order_data.accept_vip,
                    order_data.accept_next_day, order_data.travel_date,
                    payload.name, order_data.customer, safe_int_conversion(order_data.price),
                    order_data.remark, is_vfs_order, *visatype, order_data.auto_schedule
                ))

                # 插入客户信息
                for client in order_data.clients:
                    cur.execute("""
                        INSERT INTO clients (
                            order_id, name, surname_pinyin, firstname_pinyin, dob,
                            passport, passport_expire, passport_image, avatar_image,
                            gender, nationality, passport_date, sign_location,
                            bornplace, marital_status, country, region
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                    """, (
                        order_id, client.name, client.surname_pinyin,
                        client.firstname_pinyin, client.dob, client.passport,
                        client.passport_expire, client.passport_image,
                        client.avatar_image, client.gender, client.nationality,
                        client.passport_date, client.sign_location,
                        client.bornplace, client.marital_status,
                        client.country, client.region
                    ))

                # 插入签证类型
                for visa in order_data.visaType:
                    cur.execute("""
                        INSERT INTO visa_types (
                            order_id, mission_code, center_code, visa_type, visa_code
                        ) VALUES (%s, %s, %s, %s, %s)
                    """, (order_id, *visa))

                # 插入日期范围
                for start_date, end_date in order_data.dateRangeList:
                    cur.execute("""
                        INSERT INTO date_ranges (order_id, start_date, end_date)
                        VALUES (%s, %s, %s)
                    """, (
                        order_id,
                        start_date.replace("/", "-"),
                        end_date.replace("/", "-")
                    ))

        # 处理所有签证写入Redis（包括西班牙和其他国家）
        log_info("处理签证订单写入Redis", request_id=request_id, order_id=order_id)
        _handle_visa_to_redis(order_data, order_id, payload.username)

        log_info("订单创建成功",
                 request_id=request_id,
                 order_id=order_id,
                 operator=payload.username,
                 clients_count=len(order_data.clients),
                 visa_types_count=len(order_data.visaType))

        return {"code": 1, "order_id": order_id}

    except Exception as e:
        log_error("订单创建失败",
                  error=e,
                  request_id=request_id,
                  operator=payload.username,
                  clients=",".join(client_names))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/submit_us_visa_info", summary="提交美国签证用户信息", tags=["美国签证"])
def submit_us_visa_info(request_data: USVisaUserInfoRequest, payload: UserPayload = Depends(verify_jwt_token)):
    """提交美国签证用户信息到Redis"""
    request_id = str(uuid.uuid4())[:8]

    try:
        # 记录请求信息
        client_names = [client.name for client in request_data.clients]
        log_info("提交美国签证用户信息",
                 request_id=request_id,
                 operator=payload.username,
                 clients=",".join(client_names),
                 clients_count=len(request_data.clients))

        # 生成唯一的提交ID
        submission_id = f"us_visa_{int(time.time())}_{request_id}"

        # 准备Redis数据
        redis_data = {
            "submission_id": submission_id,
            "user_id": payload.user_id,
            "username": payload.username,
            "visa_type": request_data.visaType,
            "date_ranges": request_data.dateRangeList,
            "travel_date": request_data.travel_date,
            "customer": request_data.customer,
            "remark": request_data.remark,
            "clients": [],
            "created_at": datetime.now().isoformat(),
            "status": "submitted"
        }

        # 处理客户信息
        for client in request_data.clients:
            client_data = {
                "name": client.name,
                "surname_pinyin": client.surname_pinyin,
                "firstname_pinyin": client.firstname_pinyin,
                "dob": client.dob,
                "passport": client.passport,
                "passport_expire": client.passport_expire,
                "passport_image": client.passport_image,
                "gender": client.gender,
                "nationality": client.nationality,
                "passport_date": client.passport_date,
                # 美国签证特殊字段
                "us_username": client.us_username,
                "us_password": client.us_password,
                "us_qa": [{"DISP": qa.DISP, "VAL": qa.VAL} for qa in client.us_qa],
            }
            redis_data["clients"].append(client_data)

        # 保存到Redis
        redis_key = f"us_visa_submissions:{submission_id}"
        redis_client_local.setex(redis_key, 86400 * 30, json.dumps(redis_data))  # 保存30天

        # 同时保存到用户的提交列表中
        user_submissions_key = f"user_us_visa_submissions:{payload.user_id}"
        redis_client_local.lpush(user_submissions_key, submission_id)
        redis_client_local.expire(user_submissions_key, 86400 * 30)  # 30天过期

        log_info("美国签证用户信息提交成功",
                 request_id=request_id,
                 submission_id=submission_id,
                 operator=payload.username,
                 clients_count=len(request_data.clients))

        return {
            "code": 1,
            "message": "美国签证用户信息提交成功",
            "submission_id": submission_id
        }

    except Exception as e:
        log_error("美国签证用户信息提交失败",
                  error=e,
                  request_id=request_id,
                  operator=payload.username,
                  clients=",".join(client_names))
        raise HTTPException(status_code=500, detail=f"提交失败: {str(e)}")


@app.get("/api/get_us_visa_submissions", summary="获取用户的美国签证提交记录", tags=["美国签证"])
def get_us_visa_submissions(payload: UserPayload = Depends(verify_jwt_token)):
    """获取用户的美国签证提交记录"""
    try:
        user_submissions_key = f"user_us_visa_submissions:{payload.user_id}"
        submission_ids = redis_client_local.lrange(user_submissions_key, 0, -1)

        submissions = []
        for submission_id in submission_ids:
            redis_key = f"us_visa_submissions:{submission_id.decode()}"
            data = redis_client_local.get(redis_key)
            if data:
                submission_data = json.loads(data)
                # 只返回基本信息，不包含敏感数据
                submissions.append({
                    "submission_id": submission_data["submission_id"],
                    "created_at": submission_data["created_at"],
                    "status": submission_data["status"],
                    "clients_count": len(submission_data["clients"]),
                    "client_names": [c["name"] for c in submission_data["clients"]],
                    "travel_date": submission_data["travel_date"],
                    "customer": submission_data["customer"]
                })

        return {"code": 1, "data": submissions}

    except Exception as e:
        log_error("获取美国签证提交记录失败", error=e, user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=f"获取记录失败: {str(e)}")


@app.get("/api/get_order_detail", summary="获取订单详情", tags=["订单"])
def get_order_detail(order_id: str, payload: UserPayload = Depends(verify_jwt_token)):
    """获取订单详情"""
    with db_manager.get_db() as conn:
        with conn.cursor() as cur:
            # 查询订单基本信息
            if payload.permission in ("admin", "kefu"):
                cur.execute("""
                    SELECT o.created_at, o.updated_at, o.status, o.accept_vip, o.accept_next_day,
                           o.travel_date, o.customer, o.price, o.remark, o.operator, o.order_status,
                           u.name as operator_name
                    FROM orders o
                    LEFT JOIN users u ON o.operator = u.id
                    WHERE o.order_id = %s
                """, (order_id,))
            else:
                cur.execute("""
                    SELECT created_at, updated_at, status, accept_vip, accept_next_day,
                           travel_date, customer, price, remark, operator, order_status,
                           NULL as operator_name
                    FROM orders WHERE order_id = %s
                """, (order_id,))
            order = cur.fetchone()

            if not order:
                return {"code": 0, "message": "订单不存在"}

            # 检查订单状态
            if order[10] == 'deleted':  # order_status 字段
                return {"code": 0, "message": "订单已删除"}

            # 权限检查
            if payload.permission not in ("admin", "kefu") and order[9] != payload.user_id:
                return {"code": 0, "message": "无权限访问该订单"}

            # 获取客户信息
            cur.execute("""
                SELECT name, surname_pinyin, firstname_pinyin, dob, passport,
                       passport_expire, passport_image, avatar_image, gender,
                       nationality, passport_date, sign_location, bornplace,
                       marital_status, country, region
                FROM clients WHERE order_id = %s
            """, (order_id,))
            clients = [
                dict(zip([desc.name for desc in cur.description], row))
                for row in cur.fetchall()
            ]

            # 获取签证类型
            cur.execute("""
                SELECT mission_code, center_code, visa_type, visa_code
                FROM visa_types WHERE order_id = %s
            """, (order_id,))
            visa_types = cur.fetchall()

            # 获取日期范围
            cur.execute("""
                SELECT start_date, end_date
                FROM date_ranges WHERE order_id = %s
            """, (order_id,))
            date_ranges = cur.fetchall()

            # 获取更新日志
            cur.execute("""
                SELECT old_data, new_data, updated_at
                FROM order_update_log
                WHERE order_id = %s
                ORDER BY updated_at DESC
                LIMIT 1
            """, (order_id,))
            log = cur.fetchone()

            order_detail = {
                "order_id": order_id,
                "created_at": order[0].strftime('%Y-%m-%d %H:%M:%S') if order[0] else None,
                "updated_at": order[1].strftime('%Y-%m-%d %H:%M:%S') if order[1] else None,
                "status": order[2],
                "accept_vip": order[3],
                "accept_next_day": order[4],
                "travel_date": order[5],
                "customer": order[6],
                "price": str(order[7]) if order[7] is not None else "",
                "remark": order[8],
                "clients": clients,
                "visaType": visa_types,
                "dateRangeList": date_ranges,
                "update_log": {
                    "old": log[0],
                    "new": log[1],
                    "updated_at": log[2]
                } if log else None
            }

            # 如果是管理员或客服，添加操作员信息
            if payload.permission in ("admin", "kefu"):
                order_detail["operator"] = order[9]  # operator user_id
                order_detail["operator_name"] = order[11]  # operator name

            return {
                "code": 1,
                "data": order_detail
            }


@app.post("/api/edit_order", summary="编辑订单", tags=["订单"])
def update_order(order_data: EditOrderData, payload: UserPayload = Depends(verify_jwt_token)):
    """编辑订单"""
    order_id = order_data.order_id

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 权限检查
                print(order_id)
                cur.execute(
                    "SELECT operator, order_status FROM orders WHERE order_id = %s",
                    (order_id,)
                )
                order = cur.fetchone()
                if not order:
                    return {"code": 0, "message": "订单不存在"}

                if order[1] == 'deleted':
                    return {"code": 0, "message": "订单已删除，无法编辑"}

                if order[0] != payload.user_id and payload.permission not in ("admin", "kefu"):
                    return {"code": 0, "message": "无权限编辑该订单"}

                # 获取旧数据用于日志
                old_data = _get_order_old_data(cur, order_id)

                # 检查西班牙签证状态
                if not _check_spain_visa_status(order_data, old_data['visaType']):
                    return {"code": 0, "message": "请等待订单状态更新才允许编辑订单"}

                # 删除旧数据
                cur.execute("DELETE FROM clients WHERE order_id = %s", (order_id,))
                cur.execute("DELETE FROM visa_types WHERE order_id = %s", (order_id,))
                cur.execute("DELETE FROM date_ranges WHERE order_id = %s", (order_id,))

                # 插入新数据
                _insert_order_details(cur, order_id, order_data)

                visatype = order_data.visaType[0]

                is_vfs_order = True if visatype[0] and "spain" not in visatype[0].lower() else False

                # 更新订单主表
                cur.execute("""
                    UPDATE orders
                    SET updated_at = NOW(), accept_vip = %s, accept_next_day = %s,
                        travel_date = %s, customer = %s, price = %s, remark = %s,
                        is_vfs_order = %s, mission_code = %s, center_code = %s, visa_type = %s, visa_code = %s,
                        auto_schedule = %s
                    WHERE order_id = %s
                """, (
                    order_data.accept_vip, order_data.accept_next_day,
                    order_data.travel_date, order_data.customer,
                    safe_int_conversion(order_data.price), order_data.remark,
                    is_vfs_order, *visatype, order_data.auto_schedule, order_id
                ))

                # 记录更新日志
                new_data = {
                    "clients": [client.dict() for client in order_data.clients],
                    "visaType": order_data.visaType,
                    "dateRangeList": order_data.dateRangeList,
                    "accept_vip": order_data.accept_vip,
                    "accept_next_day": order_data.accept_next_day,
                    "travel_date": order_data.travel_date,
                    "customer": order_data.customer,
                    "price": safe_int_conversion(order_data.price),
                    "remark": order_data.remark
                }

                cur.execute("""
                    INSERT INTO order_update_log (order_id, operator, old_data, new_data)
                    VALUES (%s, %s, %s, %s)
                """, (order_id, payload.user_id, json.dumps(old_data), json.dumps(new_data)))

        # 更新签证Redis数据（包括西班牙和其他国家）
        _update_visa_redis(order_data, order_id, payload.username, old_data['visaType'])

        return {"code": 1, "message": "订单更新成功"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/order-history/{order_id}", summary="获取订单历史记录", tags=["订单"])
def get_order_history(order_id: str, payload: UserPayload = Depends(verify_jwt_token)):
    """
    获取指定订单的完整历史更新记录

    Args:
        order_id: 订单ID
        payload: 用户认证信息

    Returns:
        {
            "code": 1,
            "data": [
                {
                    "id": 1,
                    "order_id": "order_123",
                    "action": "update",
                    "operator": "admin",
                    "updated_at": "2024-01-01 10:00:00",
                    "remark": "更新订单信息",
                    "changes": [
                        {
                            "field": "status",
                            "old_value": "pending",
                            "new_value": "confirmed"
                        }
                    ]
                }
            ]
        }
    """
    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 权限检查 - 确保用户有权限查看该订单
                cur.execute(
                    "SELECT operator, order_status FROM orders WHERE order_id = %s",
                    (order_id,)
                )
                order = cur.fetchone()
                if not order:
                    return {"code": 0, "message": "订单不存在"}

                if order[1] == 'deleted':
                    return {"code": 0, "message": "订单已删除"}

                if order[0] != payload.user_id and payload.permission not in ("admin", "kefu"):
                    return {"code": 0, "message": "无权限查看该订单历史"}

                # 查询订单历史记录
                cur.execute("""
                    SELECT
                        order_id,
                        operator,
                        old_data,
                        new_data,
                        updated_at
                    FROM order_update_log
                    WHERE order_id = %s
                    ORDER BY updated_at DESC
                """, (order_id,))

                records = cur.fetchall()
                print(order_id)

                # 获取操作人姓名映射
                operator_ids = [str(record[1]) for record in records if record[1]]
                operator_names = {}
                if operator_ids:
                    # 使用 ANY 操作符查询多个用户
                    placeholders = ','.join(['%s'] * len(operator_ids))
                    cur.execute(f"""
                        SELECT id, name FROM users WHERE id IN ({placeholders})
                    """, operator_ids)
                    for id, name in cur.fetchall():
                        operator_names[str(id)] = name

                # 处理返回数据
                history_data = []
                for i, record in enumerate(records):
                    print(record)
                    old_data = {}
                    new_data = {}

                    # 解析JSON数据
                    try:
                        if record[2]:  # old_data
                            old_data = record[2]
                    except json.JSONDecodeError:
                        old_data = {}

                    try:
                        if record[3]:  # new_data
                            new_data = record[3]
                    except json.JSONDecodeError:
                        new_data = {}

                    # 生成变更详情
                    changes = _generate_changes(old_data, new_data)

                    # 获取操作人姓名
                    operator_name = operator_names.get(str(record[1]), f"用户{record[1]}")

                    # 如果仅备注发生变化，则将操作类型标记为 remark_update
                    action_type = "update"
                    if len(changes) == 1:
                        only_field = changes[0].get("field")
                        if only_field in ("备注", "remark"):
                            action_type = "remark_update"

                    history_data.append({
                        "id": i + 1,
                        "order_id": record[0],
                        "action": action_type,
                        "operator": operator_name,
                        "updated_at": record[4].strftime('%Y-%m-%d %H:%M:%S') if record[4] else None,
                        "remark": "备注变更" if action_type == "remark_update" else "更新订单信息",
                        "changes": changes
                    })

                return {"code": 1, "data": history_data}

    except Exception as e:
        print(f"获取订单历史记录失败: {str(e)}")
        return {"code": 0, "message": f"获取历史记录失败: {str(e)}"}


@app.post("/api/get_account_password", summary="获取订单账号密码", tags=["订单"])
def get_account_password(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    try:
        order_id = data.get("order_id")
        if not order_id:
            return {"code": 0, "message": "缺少订单号"}

        # 验证订单是否存在且属于当前用户
        with db_manager.get_db() as conn:
            cur = conn.cursor()

            # 检查订单是否存在且属于当前用户
            if payload.permission == 'admin' or payload.permission == 'kefu':
                # 管理员可以操作所有订单
                cur.execute("""
                    SELECT o.order_id, o.vfs_account, o.vfs_password
                    FROM orders o
                    WHERE o.order_id = %s
                    ORDER BY o.order_id LIMIT 1
                """, (order_id,))
            else:
                # 非管理员只能操作自己的订单
                cur.execute("""
                    SELECT o.order_id, o.vfs_account, o.vfs_password
                    FROM orders o
                    WHERE o.order_id = %s AND o.operator = %s
                    ORDER BY o.order_id LIMIT 1
                """, (order_id, payload.user_id))
            order_info = cur.fetchone()
            if not order_info:
                return {"code": 0, "message": "订单不存在"}

            order_id, vfs_account, vfs_password = order_info

            return {"code": 1, "data": {"vfs_account": vfs_account, "vfs_password": vfs_password}}

        return {"code": 0, "message": "未知错误"}

    except Exception as e:
        print(f"提取失败: {str(e)}")
        return {"code": 0, "message": f"提取失败: {str(e)}"}


@app.post("/api/reschedule_payment_order", summary="重新预约待支付订单", tags=["订单"])
def reschedule_payment_order(
    request: dict,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """重新预约待支付订单：将订单状态从waiting_for_payment恢复到registe_success，并将数据从successUserDatas移回spainUserDatas"""
    request_id = generate_request_id()

    order_id = request.get("order_id")
    if not order_id:
        raise HTTPException(status_code=400, detail="订单ID不能为空")

    log_info("重新预约订单请求",
             request_id=request_id,
             order_id=order_id,
             user_id=payload.user_id,
             username=payload.username)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查订单是否存在且状态为待支付
                cur.execute("""
                    SELECT order_status, operator
                    FROM orders
                    WHERE order_id = %s AND order_status != 'deleted'
                """, (order_id,))
                result = cur.fetchone()

                if not result:
                    raise HTTPException(status_code=404, detail="订单不存在")

                order_status, operator_id = result

                # 权限检查：只有订单创建者或管理员/客服可以重新预约
                if operator_id != payload.user_id and payload.permission not in ("admin", "kefu"):
                    raise HTTPException(status_code=403, detail="无权限操作该订单")

                if order_status != 'waiting_for_payment':
                    raise HTTPException(status_code=400, detail="只有待支付状态的订单才能重新预约")

                # 更新订单状态为registe_success
                cur.execute("""
                    UPDATE orders
                    SET order_status = 'registe_success', updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (order_id,))

                # 记录操作日志
                old_data = {"order_status": order_status, "action": "重新预约前"}
                new_data = {"order_status": "registe_success", "action": "重新预约", "operator": payload.username}
                cur.execute("""
                    INSERT INTO order_update_log (order_id, operator, old_data, new_data)
                    VALUES (%s, %s, %s, %s)
                """, (order_id, payload.user_id, json.dumps(old_data), json.dumps(new_data)))

                conn.commit()

                # Redis操作：将数据从successUserDatas移回spainUserDatas
                try:
                    redis_client = RedisClient()

                    # 获取successUserDatas中的所有数据 已经是数组了
                    all_success_data = redis_client_local.hgetall("spainSuccessUsers")

                    found_data = None
                    found_key = None

                    # 遍历所有数据，查找匹配的order_id
                    for value in all_success_data:
                        try:
                            data = json.loads(value) if isinstance(value, str) else value
                            if isinstance(data, dict) and data.get("order_id") == order_id:
                                found_data = data
                                found_key = f"{data.get('centerCode')}-{data.get('passportNO')}"
                                break
                        except (json.JSONDecodeError, AttributeError):
                            continue

                    if found_data and found_key:
                        # 构建spainUserDatas的key: centerCode-passportNO
                        center_code = found_data.get("centerCode", "")
                        passport_no = found_data.get("passportNO", "")
                        spain_key = f"{center_code}-{passport_no}"
                        found_data['status'] = 'update_appointment'
                        found_data['queue_name'] = 'spainUserDatas'
                        # 将数据移回spainUserDatas
                        redis_client_local.hset("spainUserDatas", spain_key, json.dumps(found_data))

                        # # 从successUserDatas中删除
                        # redis_client_local.hdel("spainSuccessUsers", found_key)

                        log_info("Redis数据迁移成功",
                                 request_id=request_id,
                                 order_id=order_id,
                                 original_key=found_key,
                                 new_key=spain_key,
                                 action="spainSuccessUsers -> spainUserDatas")
                    else:
                        log_info("Redis中未找到订单数据",
                                 request_id=request_id,
                                 order_id=order_id,
                                 note="在successUserDatas中未找到匹配的订单数据")

                except Exception as redis_error:
                    log_error("Redis操作失败",
                              error=redis_error,
                              request_id=request_id,
                              order_id=order_id)
                    # Redis操作失败不影响数据库操作的成功

                log_info("重新预约订单成功",
                         request_id=request_id,
                         order_id=order_id,
                         user_id=payload.user_id)

                return {
                    "code": 1,
                    "message": "重新预约成功，订单已重新进入预约队列"
                }

    except HTTPException:
        raise
    except Exception as e:
        log_error("重新预约订单失败",
                  error=e,
                  request_id=request_id,
                  order_id=order_id,
                  user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=f"重新预约失败: {str(e)}")


@app.post("/api/delete_order", summary="删除订单", tags=["订单"])
def delete_order(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """删除订单"""
    order_id = data.get("order_id")
    if not order_id:
        raise HTTPException(status_code=400, detail="缺少订单ID")

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 权限检查
                cur.execute(
                    "SELECT operator, order_status FROM orders WHERE order_id = %s",
                    (order_id,)
                )
                order = cur.fetchone()
                if not order:
                    return {"code": 0, "message": "订单不存在"}

                if order[1] == 'deleted':
                    return {"code": 0, "message": "订单已删除"}

                if order[0] != payload.user_id and payload.permission not in ("admin", "kefu"):
                    return {"code": 0, "message": "无权限删除该订单"}

                # 软删除订单（将状态设置为deleted）
                cur.execute("""
                    UPDATE orders
                    SET order_status = 'deleted', updated_at = NOW()
                    WHERE order_id = %s
                """, (order_id,))

        # 处理Redis中的签证数据（包括西班牙和其他国家）
        _delete_visa_redis(order_id)

        return {"code": 1, "message": "订单删除成功"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/toggle_vip_accept", summary="切换VIP接受状态", tags=["订单"])
def toggle_vip_accept(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """切换订单的VIP接受状态"""
    try:
        order_id = data.get("order_id")
        accept_vip = data.get("accept_vip")

        if not order_id:
            raise HTTPException(status_code=400, detail="缺少订单ID")

        if accept_vip is None:
            raise HTTPException(status_code=400, detail="缺少VIP接受状态")

        request_id = generate_request_id()
        log_info("切换VIP接受状态请求",
                 request_id=request_id,
                 user_id=payload.user_id,
                 order_id=order_id,
                 accept_vip=accept_vip)

        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查订单是否存在且用户有权限，同时获取护照号
                cur.execute("""
                    SELECT o.operator, c.passport
                    FROM orders o
                    JOIN clients c ON o.order_id = c.order_id
                    WHERE o.order_id = %s AND o.order_status != 'deleted'
                    LIMIT 1
                """, (order_id,))
                order_info = cur.fetchone()

                if not order_info:
                    return {"code": 0, "message": "订单不存在"}

                operator_id, passport = order_info
                if operator_id != payload.user_id and payload.permission not in ("admin", "kefu"):
                    return {"code": 0, "message": "无权限修改该订单"}

                # 获取旧数据用于日志记录
                old_data = _get_order_old_data(cur, order_id)

                # 更新数据库中的VIP接受状态
                cur.execute("""
                    UPDATE orders
                    SET accept_vip = %s, updated_at = NOW()
                    WHERE order_id = %s
                """, (accept_vip, order_id))

                # 记录更新日志
                new_data = old_data.copy()
                new_data['accept_vip'] = accept_vip

                cur.execute("""
                    INSERT INTO order_update_log (order_id, operator, old_data, new_data)
                    VALUES (%s, %s, %s, %s)
                """, (order_id, payload.user_id, json.dumps(old_data), json.dumps(new_data)))

                # 检查是否有西班牙签证，如果有则同步更新Redis数据
                cur.execute("""
                    SELECT center_code
                    FROM visa_types
                    WHERE order_id = %s AND mission_code = 'spain'
                    LIMIT 1
                """, (order_id,))
                spain_visa = cur.fetchone()

                if spain_visa:
                    center_code = spain_visa[0]
                    redis_key = f"{center_code}-{passport}"

                    # 从Redis获取现有数据
                    spain_data_str = redis_client_local.hget('spainUserDatas', redis_key)
                    if spain_data_str:
                        spain_data = json.loads(spain_data_str)
                        # 仅修改accept_vip字段

                        spain_data['acceptVIP'] = 1 if accept_vip else 2
                        # 原样回写到Redis
                        redis_client_local.hset(
                            'spainUserDatas',
                            redis_key,
                            json.dumps(spain_data)
                        )
                        log_info("Redis中VIP接受状态已同步",
                                 request_id=request_id,
                                 redis_key=redis_key,
                                 accept_vip=accept_vip)

                # 同步更新vfs_user中的数据
                vfs_data_str = redis_client_local.hget('vfs_user', order_id)
                if vfs_data_str:
                    vfs_data = json.loads(vfs_data_str)
                    vfs_data['order_info']['accept_vip'] = accept_vip
                    vfs_data['updated_at'] = int(time.time())
                    redis_client_local.hset(
                        'vfs_user',
                        order_id,
                        json.dumps(vfs_data, ensure_ascii=False)
                    )
                    log_info("vfs_user中VIP接受状态已同步",
                             request_id=request_id,
                             order_id=order_id,
                             accept_vip=accept_vip)

        log_info("切换VIP接受状态成功",
                 request_id=request_id,
                 user_id=payload.user_id,
                 order_id=order_id,
                 accept_vip=accept_vip)

        return {"code": 1, "message": "VIP接受状态更新成功"}

    except Exception as e:
        log_error("切换VIP接受状态失败", error=e, request_id=request_id, user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/toggle_next_day_accept", summary="切换隔天接受状态", tags=["订单"])
def toggle_next_day_accept(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """切换订单的隔天接受状态"""
    try:
        order_id = data.get("order_id")
        accept_next_day = data.get("accept_next_day")

        if not order_id:
            raise HTTPException(status_code=400, detail="缺少订单ID")

        if accept_next_day is None:
            raise HTTPException(status_code=400, detail="缺少隔天接受状态")

        request_id = generate_request_id()
        log_info("切换隔天接受状态请求",
                 request_id=request_id,
                 user_id=payload.user_id,
                 order_id=order_id,
                 accept_next_day=accept_next_day)

        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查订单是否存在且用户有权限，同时获取护照号
                cur.execute("""
                    SELECT o.operator, c.passport
                    FROM orders o
                    JOIN clients c ON o.order_id = c.order_id
                    WHERE o.order_id = %s AND o.order_status != 'deleted'
                    LIMIT 1
                """, (order_id,))
                order_info = cur.fetchone()

                if not order_info:
                    return {"code": 0, "message": "订单不存在"}

                operator_id, passport = order_info
                if operator_id != payload.user_id and payload.permission not in ("admin", "kefu"):
                    return {"code": 0, "message": "无权限修改该订单"}

                # 获取旧数据用于日志记录
                old_data = _get_order_old_data(cur, order_id)

                # 更新数据库中的隔天接受状态
                cur.execute("""
                    UPDATE orders
                    SET accept_next_day = %s, updated_at = NOW()
                    WHERE order_id = %s
                """, (accept_next_day, order_id))

                # 记录更新日志
                new_data = old_data.copy()
                new_data['accept_next_day'] = accept_next_day

                cur.execute("""
                    INSERT INTO order_update_log (order_id, operator, old_data, new_data)
                    VALUES (%s, %s, %s, %s)
                """, (order_id, payload.user_id, json.dumps(old_data), json.dumps(new_data)))

                # 检查是否有西班牙签证，如果有则同步更新Redis数据
                cur.execute("""
                    SELECT center_code
                    FROM visa_types
                    WHERE order_id = %s AND mission_code = 'spain'
                    LIMIT 1
                """, (order_id,))
                spain_visa = cur.fetchone()

                if spain_visa:
                    center_code = spain_visa[0]
                    redis_key = f"{center_code}-{passport}"

                    # 从Redis获取现有数据
                    spain_data_str = redis_client_local.hget('spainUserDatas', redis_key)
                    if spain_data_str:
                        spain_data = json.loads(spain_data_str)
                        # 仅修改accept_next_day字段
                        spain_data['acceptND'] = 1 if accept_next_day else 2
                        # 原样回写到Redis
                        redis_client_local.hset(
                            'spainUserDatas',
                            redis_key,
                            json.dumps(spain_data)
                        )
                        log_info("Redis中隔天接受状态已同步",
                                 request_id=request_id,
                                 redis_key=redis_key,
                                 accept_next_day=accept_next_day)

                # 同步更新vfs_user中的数据
                vfs_data_str = redis_client_local.hget('vfs_user', order_id)
                if vfs_data_str:
                    vfs_data = json.loads(vfs_data_str)
                    vfs_data['order_info']['accept_next_day'] = accept_next_day
                    vfs_data['updated_at'] = int(time.time())
                    redis_client_local.hset(
                        'vfs_user',
                        order_id,
                        json.dumps(vfs_data, ensure_ascii=False)
                    )
                    log_info("vfs_user中隔天接受状态已同步",
                             request_id=request_id,
                             order_id=order_id,
                             accept_next_day=accept_next_day)

        log_info("切换隔天接受状态成功",
                 request_id=request_id,
                 user_id=payload.user_id,
                 order_id=order_id,
                 accept_next_day=accept_next_day)

        return {"code": 1, "message": "隔天接受状态更新成功"}

    except Exception as e:
        log_error("切换隔天接受状态失败", error=e, request_id=request_id, user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/toggle_auto_schedule", summary="切换自动预约状态", tags=["订单"])
def toggle_auto_schedule(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """切换订单的自动预约状态"""
    try:
        order_id = data.get("order_id")
        auto_schedule = data.get("auto_schedule")

        if not order_id:
            raise HTTPException(status_code=400, detail="缺少订单ID")

        if auto_schedule is None:
            raise HTTPException(status_code=400, detail="缺少自动预约状态")

        request_id = generate_request_id()
        log_info("切换自动预约状态请求",
                 request_id=request_id,
                 user_id=payload.user_id,
                 order_id=order_id,
                 auto_schedule=auto_schedule)

        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查订单是否存在且用户有权限，同时获取护照号
                cur.execute("""
                    SELECT o.operator, c.passport
                    FROM orders o
                    JOIN clients c ON o.order_id = c.order_id
                    WHERE o.order_id = %s AND o.order_status != 'deleted'
                    LIMIT 1
                """, (order_id,))
                order_info = cur.fetchone()

                if not order_info:
                    return {"code": 0, "message": "订单不存在"}

                operator_id, passport = order_info
                if operator_id != payload.user_id and payload.permission not in ("admin", "kefu"):
                    return {"code": 0, "message": "无权限修改该订单"}

                # 获取旧数据用于日志记录
                old_data = _get_order_old_data(cur, order_id)

                # 更新数据库中自动预约状态
                cur.execute("""
                    UPDATE orders
                    SET auto_schedule = %s, updated_at = NOW()
                    WHERE order_id = %s
                """, (auto_schedule, order_id))

                # 记录更新日志
                new_data = old_data.copy()
                new_data['auto_schedule'] = auto_schedule

                cur.execute("""
                    INSERT INTO order_update_log (order_id, operator, old_data, new_data)
                    VALUES (%s, %s, %s, %s)
                """, (order_id, payload.user_id, json.dumps(old_data), json.dumps(new_data)))

                # # 检查是否有西班牙签证，如果有则同步更新Redis数据
                # cur.execute("""
                #     SELECT center_code
                #     FROM visa_types
                #     WHERE order_id = %s AND mission_code = 'spain'
                #     LIMIT 1
                # """, (order_id,))
                # spain_visa = cur.fetchone()

                # if spain_visa:
                #     center_code = spain_visa[0]
                #     redis_key = f"{center_code}-{passport}"

                #     # 从Redis获取现有数据
                #     spain_data_str = redis_client_local.hget('spainUserDatas', redis_key)
                #     if spain_data_str:
                #         spain_data = json.loads(spain_data_str)
                #         # 仅修改accept_next_day字段
                #         spain_data['acceptND'] = 1 if accept_next_day else 2
                #         # 原样回写到Redis
                #         redis_client_local.hset(
                #             'spainUserDatas',
                #             redis_key,
                #             json.dumps(spain_data)
                #         )
                #         log_info("Redis中隔天接受状态已同步",
                #                  request_id=request_id,
                #                  redis_key=redis_key,
                #                  accept_next_day=accept_next_day)

                # # 同步更新vfs_user中的数据
                # vfs_data_str = redis_client_local.hget('vfs_user', order_id)
                # if vfs_data_str:
                #     vfs_data = json.loads(vfs_data_str)
                #     vfs_data['order_info']['accept_next_day'] = accept_next_day
                #     vfs_data['updated_at'] = int(time.time())
                #     redis_client_local.hset(
                #         'vfs_user',
                #         order_id,
                #         json.dumps(vfs_data, ensure_ascii=False)
                #     )
                #     log_info("vfs_user中隔天接受状态已同步",
                #              request_id=request_id,
                #              order_id=order_id,
                #              accept_next_day=accept_next_day)

        log_info("切换自动预约状态成功",
                 request_id=request_id,
                 user_id=payload.user_id,
                 order_id=order_id,
                 auto_schedule=auto_schedule)

        return {"code": 1, "message": "自动预约状态更新成功"}

    except Exception as e:
        log_error("切换自动预约状态失败", error=e, request_id=request_id, user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/update_order_remark", summary="更新订单备注", tags=["订单"])
def update_order_remark(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """仅更新订单备注并记录日志"""
    try:
        order_id = data.get("order_id")
        remark = data.get("remark", "")

        if not order_id:
            return {"code": 0, "message": "缺少订单ID"}

        # 备注长度兜底校验（前端也会校验）
        if remark is not None and len(str(remark)) > 500:
            return {"code": 0, "message": "备注长度不能超过500字符"}

        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查订单是否存在及权限
                cur.execute(
                    """
                    SELECT operator, order_status, remark
                    FROM orders WHERE order_id = %s
                    """,
                    (order_id,)
                )
                row = cur.fetchone()
                if not row:
                    return {"code": 0, "message": "订单不存在"}

                operator_id, order_status, old_remark = row
                if order_status == 'deleted':
                    return {"code": 0, "message": "订单已删除"}

                if operator_id != payload.user_id and payload.permission not in ("admin", "kefu"):
                    return {"code": 0, "message": "无权限修改该订单"}

                # 获取旧数据用于日志
                old_data = _get_order_old_data(cur, order_id)

                # 执行更新
                cur.execute(
                    """
                    UPDATE orders
                    SET remark = %s, updated_at = NOW()
                    WHERE order_id = %s
                    """,
                    (remark, order_id)
                )

                # 记录更新日志（只包含 remark 字段变化）
                new_data = {**old_data}
                new_data["remark"] = remark

                cur.execute(
                    """
                    INSERT INTO order_update_log (order_id, operator, old_data, new_data)
                    VALUES (%s, %s, %s, %s)
                    """,
                    (order_id, payload.user_id, json.dumps(old_data), json.dumps(new_data))
                )

        return {"code": 1, "message": "备注更新成功"}
    except Exception as e:
        print(f"更新订单备注失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/update_expected_dates", summary="更新期望日期", tags=["订单"])
def update_expected_dates(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """更新订单的期望日期"""
    try:
        order_id = data.get("order_id")
        date_range_list = data.get("dateRangeList")

        if not order_id:
            raise HTTPException(status_code=400, detail="缺少订单ID")

        if not date_range_list or not isinstance(date_range_list, list):
            raise HTTPException(status_code=400, detail="缺少期望日期列表")

        # 验证日期格式
        for date_range in date_range_list:
            if not isinstance(date_range, list) or len(date_range) != 2:
                raise HTTPException(status_code=400, detail="日期范围格式错误")

        request_id = generate_request_id()
        log_info("更新期望日期请求",
                 request_id=request_id,
                 user_id=payload.user_id,
                 order_id=order_id,
                 date_range_list=date_range_list)

        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查订单是否存在且用户有权限，同时获取护照号
                cur.execute("""
                    SELECT o.operator, c.passport
                    FROM orders o
                    JOIN clients c ON o.order_id = c.order_id
                    WHERE o.order_id = %s AND o.order_status != 'deleted'
                    LIMIT 1
                """, (order_id,))
                order_info = cur.fetchone()

                if not order_info:
                    return {"code": 0, "message": "订单不存在"}

                operator_id, passport = order_info
                if operator_id != payload.user_id and payload.permission not in ("admin", "kefu"):
                    return {"code": 0, "message": "无权限修改该订单"}

                # 获取旧数据用于日志记录
                old_data = _get_order_old_data(cur, order_id)

                # 获取原有的日期范围数据用于日志记录
                cur.execute("""
                    SELECT start_date, end_date
                    FROM date_ranges WHERE order_id = %s
                """, (order_id,))
                old_date_ranges = cur.fetchall()

                # 删除旧的日期范围记录
                cur.execute("DELETE FROM date_ranges WHERE order_id = %s", (order_id,))

                # 插入新的日期范围记录
                for start_date, end_date in date_range_list:
                    cur.execute("""
                        INSERT INTO date_ranges (order_id, start_date, end_date)
                        VALUES (%s, %s, %s)
                    """, (order_id, start_date, end_date))

                # 更新订单的更新时间
                cur.execute("""
                    UPDATE orders
                    SET updated_at = NOW()
                    WHERE order_id = %s
                """, (order_id,))

                # 记录更新日志
                new_data = old_data.copy()
                new_data['dateRangeList'] = date_range_list

                cur.execute("""
                    INSERT INTO order_update_log (order_id, operator, old_data, new_data)
                    VALUES (%s, %s, %s, %s)
                """, (order_id, payload.user_id, json.dumps(old_data), json.dumps(new_data)))

                # 检查是否有西班牙签证，如果有则同步更新Redis数据
                cur.execute("""
                    SELECT center_code
                    FROM visa_types
                    WHERE order_id = %s AND mission_code = 'spain'
                    LIMIT 1
                """, (order_id,))
                spain_visa = cur.fetchone()

                if spain_visa:
                    center_code = spain_visa[0]
                    redis_key = f"{center_code}-{passport}"

                    # 更新 spainUserDatas 中的期望日期
                    spain_data_str = redis_client_local.hget('spainUserDatas', redis_key)
                    if spain_data_str:
                        spain_data = json.loads(spain_data_str.decode('utf-8') if isinstance(spain_data_str, bytes) else spain_data_str)

                        # 更新 startDate 和 endDate 字段
                        # 使用第一个日期范围作为西班牙签证的期望日期
                        if date_range_list and len(date_range_list) > 0 and len(date_range_list[0]) == 2:
                            spain_data['startDate'] = date_range_list[0][0]  # yyyy-mm-dd 格式
                            spain_data['endDate'] = date_range_list[0][1]    # yyyy-mm-dd 格式

                        # 回写到Redis
                        redis_client_local.hset(
                            'spainUserDatas',
                            redis_key,
                            json.dumps(spain_data, ensure_ascii=False)
                        )
                        log_info("spainUserDatas中期望日期已同步",
                                 request_id=request_id,
                                 order_id=order_id,
                                 redis_key=redis_key,
                                 start_date=spain_data.get('startDate'),
                                 end_date=spain_data.get('endDate'))

                    # 更新Redis中的vfs_user期望日期数据
                    vfs_data_str = redis_client_local.hget('vfs_user', order_id)
                    if vfs_data_str:
                        vfs_data = json.loads(vfs_data_str.decode('utf-8') if isinstance(vfs_data_str, bytes) else vfs_data_str)
                        vfs_data['order_info']['dateRangeList'] = date_range_list
                        vfs_data['updated_at'] = int(time.time())
                        redis_client_local.hset(
                            'vfs_user',
                            order_id,
                            json.dumps(vfs_data, ensure_ascii=False)
                        )
                        log_info("vfs_user中期望日期已同步",
                                 request_id=request_id,
                                 order_id=order_id,
                                 date_range_list=date_range_list)

        log_info("更新期望日期成功",
                 request_id=request_id,
                 user_id=payload.user_id,
                 order_id=order_id,
                 date_range_list=date_range_list)

        return {"code": 1, "message": "期望日期更新成功"}

    except Exception as e:
        log_error("更新期望日期失败", error=e, request_id=request_id, user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/update_client_avatar", summary="更新客户头像", tags=["订单"])
def update_client_avatar(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """更新客户头像"""
    request_id = generate_request_id()
    try:
        order_id = data.get("order_id")
        passport = data.get("passport")
        avatar_image_base64 = data.get("avatar_image")

        if not order_id:
            raise HTTPException(status_code=400, detail="缺少订单ID")

        if not passport:
            raise HTTPException(status_code=400, detail="缺少护照号")

        if not avatar_image_base64:
            raise HTTPException(status_code=400, detail="缺少头像图片数据")
        log_info("更新客户头像请求",
                 request_id=request_id,
                 user_id=payload.user_id,
                 order_id=order_id,
                 passport=passport)

        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 检查订单是否存在且用户有权限
                cur.execute("""
                    SELECT o.operator
                    FROM orders o
                    WHERE o.order_id = %s AND o.order_status != 'deleted'
                    LIMIT 1
                """, (order_id,))
                order_info = cur.fetchone()

                if not order_info:
                    return {"code": 0, "message": "订单不存在"}

                operator_id = order_info[0]
                if operator_id != payload.user_id and payload.permission not in ("admin", "kefu"):
                    return {"code": 0, "message": "无权限修改该订单"}

                # 检查客户是否存在
                cur.execute("""
                    SELECT avatar_image
                    FROM clients
                    WHERE order_id = %s AND passport = %s
                    LIMIT 1
                """, (order_id, passport))
                client_info = cur.fetchone()

                if not client_info:
                    return {"code": 0, "message": "客户不存在"}

                old_avatar_image = client_info[0]

                # 保存头像图片文件
                try:
                    import base64
                    import uuid
                    from datetime import datetime
                    from PIL import Image
                    import io

                    # 决定文件名：保留原有头像名，没有则生成新的
                    if old_avatar_image and old_avatar_image.strip():
                        # 保留原有文件名，但确保扩展名为jpg
                        base_name = os.path.splitext(old_avatar_image)[0]
                        filename = f"{base_name}.jpg"
                    else:
                        # 生成新的唯一文件名
                        filename = f"{uuid.uuid4().hex}.jpg"

                    # 解码base64图片数据
                    image_data = base64.b64decode(avatar_image_base64)

                    # 使用PIL处理图片：转换为JPG格式并压缩到100KB以下
                    with Image.open(io.BytesIO(image_data)) as img:
                        # 转换为RGB模式（JPG不支持透明度）
                        if img.mode in ('RGBA', 'LA', 'P'):
                            # 创建白色背景
                            background = Image.new('RGB', img.size, (255, 255, 255))
                            if img.mode == 'P':
                                img = img.convert('RGBA')
                            background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                            img = background
                        elif img.mode != 'RGB':
                            img = img.convert('RGB')

                        # 压缩图片到100KB以下
                        quality = 95
                        while quality > 10:
                            output_buffer = io.BytesIO()
                            img.save(output_buffer, format='JPEG', quality=quality, optimize=True)
                            compressed_data = output_buffer.getvalue()

                            # 检查文件大小
                            if len(compressed_data) <= 100 * 1024:  # 100KB
                                break

                            # 如果还是太大，降低质量或缩小尺寸
                            if quality > 60:
                                quality -= 10
                            else:
                                # 缩小图片尺寸
                                width, height = img.size
                                new_width = int(width * 0.9)
                                new_height = int(height * 0.9)
                                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                                quality = 85

                        # 确保目录存在
                        os.makedirs(settings.AVATAR_IMAGE_DIR, exist_ok=True)

                        # 保存文件
                        filepath = os.path.join(settings.AVATAR_IMAGE_DIR, filename)
                        with open(filepath, "wb") as f:
                            f.write(compressed_data)

                        log_info("头像文件处理并保存成功",
                                 request_id=request_id,
                                 filename=filename,
                                 filepath=filepath,
                                 original_size=len(image_data),
                                 compressed_size=len(compressed_data),
                                 quality=quality)

                except Exception as file_error:
                    log_error("保存头像文件失败", error=file_error, request_id=request_id)
                    raise HTTPException(status_code=500, detail="保存头像文件失败")

                # 更新数据库
                cur.execute("""
                    UPDATE clients
                    SET avatar_image = %s
                    WHERE order_id = %s AND passport = %s
                """, (filename, order_id, passport))

                # 记录更新日志
                cur.execute("""
                    INSERT INTO order_update_log (order_id, operator, old_data, new_data)
                    VALUES (%s, %s, %s, %s)
                """, (
                    order_id,
                    payload.user_id,
                    json.dumps({"avatar_image": old_avatar_image}),
                    json.dumps({"avatar_image": filename})
                ))

                # 检查是否有西班牙签证，如果有则同步更新Redis数据
                cur.execute("""
                    SELECT center_code
                    FROM visa_types
                    WHERE order_id = %s AND mission_code = 'spain'
                    LIMIT 1
                """, (order_id,))
                spain_visa = cur.fetchone()

                if spain_visa:
                    center_code = spain_visa[0]
                    redis_key = f"{center_code}-{passport}"

                    # 从Redis获取现有数据
                    spain_data_str = redis_client_local.hget('spainUserDatas', redis_key)
                    if spain_data_str:
                        spain_data = json.loads(spain_data_str)
                        # 更新avatar_image字段
                        spain_data['avatar_image'] = filename
                        # 原样回写到Redis
                        redis_client_local.hset(
                            'spainUserDatas',
                            redis_key,
                            json.dumps(spain_data)
                        )
                        log_info("Redis中头像信息已同步",
                                 request_id=request_id,
                                 redis_key=redis_key,
                                 avatar_image=filename)

                # 删除旧头像文件（仅当文件名发生变化时）
                if old_avatar_image and old_avatar_image != filename:
                    try:
                        old_filepath = os.path.join(settings.AVATAR_IMAGE_DIR, old_avatar_image)
                        if os.path.exists(old_filepath):
                            os.remove(old_filepath)
                            log_info("旧头像文件已删除", request_id=request_id, old_file=old_avatar_image)
                    except Exception as delete_error:
                        log_warning("删除旧头像文件失败", error=delete_error, request_id=request_id)

        log_info("更新客户头像成功",
                 request_id=request_id,
                 user_id=payload.user_id,
                 order_id=order_id,
                 passport=passport,
                 new_avatar=filename)

        return {"code": 1, "message": "头像更新成功", "data": {"avatar_image": filename}}

    except Exception as e:
        log_error("更新客户头像失败", error=e, request_id=request_id, user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=str(e))


# ==================== OCR和文件上传路由 ====================


@app.post("/api/ocr-passport", summary="护照OCR识别", tags=["OCR"])
async def ocr_passport(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """护照OCR识别"""
    try:
        image_base64 = data.get("image")
        if not image_base64:
            raise HTTPException(status_code=400, detail="未提供图片")

        # 处理base64数据
        if ',' in image_base64:
            image_base64_data = image_base64.split(",")[1]
        else:
            image_base64_data = image_base64

        # 保存图片
        image_data = base64.b64decode(image_base64_data)
        filename = f"{uuid.uuid4().hex}.jpg"
        filepath = os.path.join(settings.PASSPORT_IMAGE_DIR, filename)
        ensure_dir_exists(settings.PASSPORT_IMAGE_DIR)

        with open(filepath, "wb") as f:
            f.write(image_data)

        # 调用OCR API - 使用动态获取的token
        access_token = ocr_token_manager.get_access_token()
        ocr_url = f"{settings.OCR_API_URL}?access_token={access_token}"
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        response = requests.post(
            ocr_url,
            headers=headers,
            data={'image': image_base64_data}
        )
        result = response.json()

        if 'words_result' in result:
            return {"code": 1, "result": result['words_result'], "filename": filename}
        else:
            return {"code": 0, "message": str(result)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/refresh-ocr-token", summary="刷新OCR Token", tags=["OCR"])
def refresh_ocr_token(payload: UserPayload = Depends(verify_jwt_token)):
    """手动刷新百度OCR Token"""
    try:
        # 只有管理员可以刷新token
        if payload.permission not in ("admin",):
            raise HTTPException(status_code=403, detail="权限不足")

        access_token = ocr_token_manager._refresh_token()
        return {
            "code": 1,
            "message": "OCR Token刷新成功",
            "token": access_token[:20] + "..." if access_token else None
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刷新失败: {str(e)}")


@app.get("/api/ocr-token-status", summary="查看OCR Token状态", tags=["OCR"])
def get_ocr_token_status(payload: UserPayload = Depends(verify_jwt_token)):
    """查看OCR Token状态"""
    try:
        # 只有管理员可以查看token状态
        if payload.permission not in ("admin",):
            raise HTTPException(status_code=403, detail="权限不足")

        token = redis_client_local.get(ocr_token_manager.token_key)
        expire_time = redis_client_local.get(ocr_token_manager.token_expire_key)

        if token and expire_time:
            expire_datetime = datetime.datetime.fromtimestamp(int(expire_time))
            current_time = datetime.datetime.now()
            is_valid = expire_datetime > current_time

            return {
                "code": 1,
                "data": {
                    "has_token": True,
                    "token_preview": token[:20] + "..." if token else None,
                    "expire_time": expire_datetime.strftime("%Y-%m-%d %H:%M:%S"),
                    "is_valid": is_valid,
                    "days_remaining": (expire_datetime - current_time).days if is_valid else 0
                }
            }
        else:
            return {
                "code": 1,
                "data": {
                    "has_token": False,
                    "message": "未找到有效的OCR Token"
                }
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/upload-avatar", summary="上传头像", tags=["文件"])
async def upload_avatar(file: UploadFile = File(...), payload: UserPayload = Depends(verify_jwt_token)):
    """上传头像"""
    try:
        # 验证文件格式
        if not validate_image_extension(file.filename):
            raise HTTPException(status_code=400, detail="只支持 JPG/PNG 格式")

        # 保存文件
        content = await file.read()
        ext = file.filename.split('.')[-1].lower()
        filename = f"avatar_{uuid.uuid4().hex}.{ext}"
        filepath = os.path.join(settings.AVATAR_IMAGE_DIR, filename)
        ensure_dir_exists(settings.AVATAR_IMAGE_DIR)

        with open(filepath, "wb") as f:
            f.write(content)

        return {"code": 1, "filename": filename}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/passport_image/{filename}", summary="获取护照图片", tags=["文件"])
def get_passport_image(filename: str):
    """获取护照图片"""
    filepath = os.path.join(settings.PASSPORT_IMAGE_DIR, filename)
    if os.path.exists(filepath):
        return FileResponse(filepath, media_type="image/jpeg")
    raise HTTPException(status_code=404, detail="图片不存在")


@app.get("/api/avatar_image/{filename}", summary="获取头像图片", tags=["文件"])
def get_avatar_image(filename: str):
    """获取头像图片"""
    filepath = os.path.join(settings.AVATAR_IMAGE_DIR, filename)
    if os.path.exists(filepath):
        return FileResponse(filepath, media_type="image/jpeg")
    raise HTTPException(status_code=404, detail="头像不存在")

# ==================== 日期查询路由 ====================


@app.post("/get_vfs_date", summary="获取VFS日期", tags=["日期查询"])
def get_vfs_dates(payload: UserPayload = Depends(verify_jwt_token)):
    """获取VFS可用日期"""
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{now}--{payload.username}--查询VFS时间")

    vfs_data = redis_client_local.hgetall("vfs_calendar")
    # parsed_data = [json.loads(val) for val in vfs_data.values()]
    return {"code": 1, "data": vfs_data}


@app.post("/get_bls_date", summary="获取BLS日期", tags=["日期查询"])
def get_bls_dates(payload: UserPayload = Depends(verify_jwt_token)):
    """获取BLS可用日期"""
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{now}--{payload.username}--查询BLS时间")

    bls_data = redis_client_local.hgetall("visa_logs_hash")
    # parsed_data = [json.loads(val) for val in bls_data.values()]
    return {"code": 1, "data": bls_data}


@app.post("/get_vfs_config", summary="获取VFS配置", tags=["配置"])
def get_vfs_configuration(payload: UserPayload = Depends(verify_jwt_token)):
    """获取VFS配置信息"""
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{now}--{payload.username}--查询VFS配置")

    vfs_data = redis_client_local.hgetall("vfs_center_data")
    # parsed_data = [json.loads(val) for val in vfs_data.values()]

    # 提取叶子节点
    def extract_leaf_nodes(node):
        if isinstance(node, dict):
            children = node.get("data") or node.get("sub")
            cleaned_node = {
                k: node[k]
                for k in ("name", "missionCode", "centerCode", "code",
                          "missionCodeName", "isoCode", "centerName")
                if k in node
            }
            if isinstance(children, list) and children:
                cleaned_node["children"] = [extract_leaf_nodes(child) for child in children]
            return cleaned_node
        return {}

    leaf_nodes = [extract_leaf_nodes(entry) for entry in vfs_data]
    leaf_nodes.extend(bls_config)

    return {"code": 1, "data": leaf_nodes}

# ==================== Webhook管理路由 ====================


@app.post("/api/hook", summary="添加Webhook", tags=["Webhook"])
def add_webhook(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """添加Webhook URL"""
    hook_url = data.get("hook_url")
    if not hook_url:
        raise HTTPException(status_code=400, detail="缺少 hook_url")

    with db_manager.get_db() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO user_hooks (user_id, hook_url)
                VALUES (%s, %s)
            """, (payload.user_id, hook_url))

            # 获取所有hooks
            cur.execute(
                "SELECT hook_url FROM user_hooks WHERE user_id = %s",
                (payload.user_id,)
            )
            urls = [row[0] for row in cur.fetchall()]

    # 更新Redis缓存
    redis_client_local.set(
        f"{payload.user_id}_notify_url",
        json.dumps({"hook_urls": urls})
    )

    return {"code": 1, "message": "Webhook添加成功"}


@app.get("/api/hooks", summary="获取Webhook列表", tags=["Webhook"])
def get_webhooks(payload: UserPayload = Depends(verify_jwt_token)):
    """获取用户的Webhook列表"""
    with db_manager.get_db() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id, hook_url, created_at, updated_at
                FROM user_hooks
                WHERE user_id = %s
            """, (payload.user_id,))
            hooks = cur.fetchall()

    return {
        "code": 1,
        "data": [
            {
                "id": hook[0],
                "hook_url": hook[1],
                "created_at": hook[2],
                "updated_at": hook[3]
            }
            for hook in hooks
        ]
    }


@app.post("/api/hook/update", summary="更新Webhook", tags=["Webhook"])
def update_webhook(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """更新Webhook URL"""
    hook_id = data.get("id")
    hook_url = data.get("hook_url")

    if not hook_id or not hook_url:
        raise HTTPException(status_code=400, detail="缺少必要参数")

    with db_manager.get_db() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                UPDATE user_hooks
                SET hook_url = %s, updated_at = NOW()
                WHERE id = %s AND user_id = %s
            """, (hook_url, hook_id, payload.user_id))

            if cur.rowcount == 0:
                raise HTTPException(status_code=404, detail="记录不存在或无权限")

            # 获取所有hooks
            cur.execute(
                "SELECT hook_url FROM user_hooks WHERE user_id = %s",
                (payload.user_id,)
            )
            urls = [row[0] for row in cur.fetchall()]

    # 更新Redis缓存
    redis_client_local.set(
        f"{payload.user_id}_notify_url",
        json.dumps({"hook_urls": urls})
    )

    return {"code": 1, "message": "Webhook更新成功"}


@app.post("/api/hook/delete", summary="删除Webhook", tags=["Webhook"])
def delete_webhook(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """删除Webhook URL"""
    hook_id = data.get("id")
    if not hook_id:
        raise HTTPException(status_code=400, detail="缺少参数 id")

    with db_manager.get_db() as conn:
        with conn.cursor() as cur:
            cur.execute(
                "DELETE FROM user_hooks WHERE id = %s AND user_id = %s",
                (hook_id, payload.user_id)
            )

            if cur.rowcount == 0:
                raise HTTPException(status_code=404, detail="记录不存在或无权限")

            # 获取剩余hooks
            cur.execute(
                "SELECT hook_url FROM user_hooks WHERE user_id = %s",
                (payload.user_id,)
            )
            urls = [row[0] for row in cur.fetchall()]

    # 更新或删除Redis缓存
    if urls:
        redis_client_local.set(
            f"{payload.user_id}_notify_url",
            json.dumps({"hook_urls": urls})
        )
    else:
        redis_client_local.delete(f"{payload.user_id}_notify_url")

    return {"code": 1, "message": "Webhook删除成功"}

# ==================== 系统状态路由 ====================


@app.get("/health", summary="健康检查", tags=["系统"])
def health_check():
    """系统健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "version": "2.0.0"
    }

# ==================== 辅助函数 ====================


def _handle_spain_visa(order_data: OrderData, order_id: str, username: str):
    """处理西班牙签证特殊逻辑"""
    # 检查是否有西班牙签证
    spain_visa = next(
        (visa for visa in order_data.visaType if "spain" in visa),
        None
    )

    if not spain_visa:
        return

    client = order_data.clients[0]
    center_code = spain_visa[1]
    visa_type = spain_visa[3]
    date_range = order_data.dateRangeList[0]

    spain_data = {
        "birthday": client.dob.replace('-', '/'),
        "chnname": client.name,
        "endDate": date_range[1],
        "expiredDT": client.passport_expire.replace('-', '/'),
        "gender": client.gender,
        "name": client.firstname_pinyin,
        "passportNO": client.passport,
        "phone": "",
        "startDate": date_range[0],
        "visaTypeCode": visa_type,
        "xing": client.surname_pinyin,
        "remark": order_data.remark,
        "vip": 4,
        "status": "pending",
        "acceptND": 1 if order_data.accept_next_day else 2,
        "acceptVIP": 1 if order_data.accept_vip else 2,
        "countryCode": client.nationality,
        "from": username,
        "signLocation": client.sign_location,
        "bornplace": client.bornplace,
        "passportDate": client.passport_date.replace('-', '/'),
        "maritalStatus": client.marital_status,
        "travelDate": order_data.travel_date.replace('-', '/'),
        "missionCode": "spain",
        "centerCode": center_code,
        "createTime": int(time.time()),
        "order_id": order_id,
        "passport_image": client.passport_image or "",
        "avatar_image": client.avatar_image or ""
    }

    redis_client_local.hset(
        'spainUserDatas',
        f"{center_code}-{client.passport}",
        json.dumps(spain_data)
    )


def _handle_visa_to_redis(order_data: OrderData, order_id: str, username: str):
    """处理所有签证订单写入Redis - 包括西班牙和其他国家"""
    # 首先处理西班牙签证的特殊逻辑
    _handle_spain_visa(order_data, order_id, username)

    # 处理所有签证（包括非西班牙）写入vfs_user
    if not order_data.visaType:
        return

    # 获取唯一的签证类型（单选模式）
    selected_visa = order_data.visaType[0]
    is_spain = "spain" in selected_visa[0].lower() if selected_visa[0] else False

    # 转换clients为字典数组（原样存入）
    clients_data = []
    for client in order_data.clients:
        client_dict = {
            "name": client.name,
            "surname_pinyin": client.surname_pinyin,
            "firstname_pinyin": client.firstname_pinyin,
            "passport": client.passport,
            "dob": client.dob,
            "passport_expire": client.passport_expire,
            "gender": client.gender,
            "nationality": client.nationality,
            "passport_date": client.passport_date,
            "sign_location": client.sign_location,
            "bornplace": client.bornplace,
            "marital_status": client.marital_status,
            "passport_image": client.passport_image or "",
            "avatar_image": client.avatar_image or "",
            "country": client.country or "",
            "region": client.region or ""
        }
        clients_data.append(client_dict)

    # 转换dateRangeList为start_date和end_date的字典数组
    date_ranges_data = []
    for date_range in order_data.dateRangeList:
        if len(date_range) >= 2:
            date_ranges_data.append({
                "start_date": date_range[0],
                "end_date": date_range[1]
            })

    # 构建基础订单信息
    base_order_info = {
        "accept_vip": order_data.accept_vip,
        "accept_next_day": order_data.accept_next_day,
        "travel_date": order_data.travel_date,
        "customer": order_data.customer,
        "price": order_data.price,
        "remark": order_data.remark,
        "date_ranges": date_ranges_data
    }

    if is_spain:
        # 西班牙订单不需要写入vfs_user，只处理西班牙专用的Redis逻辑
        log_info(f"西班牙订单跳过vfs_user写入",
                 order_id=order_id,
                 mission_code=selected_visa[0])
    else:
        # 如果选择的不是西班牙签证，使用新逻辑处理
        visa_data = {
            "order_id": order_id,
            "visa_info": {
                "mission_code": selected_visa[0],  # 国家代码
                "center_code": selected_visa[1],   # 中心代码
                "visa_type": selected_visa[2],     # 签证类型
                "visa_code": selected_visa[3]      # 签证代码
            },
            "clients": clients_data,
            "order_info": base_order_info,
            "status": "pending",
            "created_by": username,
            "created_at": int(time.time()),
            "updated_at": int(time.time())
        }

        # 直接使用order_id作为key写入Redis
        redis_client_local.hset(
            'vfs_user',
            order_id,
            json.dumps(visa_data, ensure_ascii=False)
        )

        log_info(f"非西班牙订单写入Redis vfs_user",
                 order_id=order_id,
                 mission_code=selected_visa[0],
                 center_code=selected_visa[1])


def _generate_changes(old_data: dict, new_data: dict) -> list:
    """
    生成变更详情列表

    Args:
        old_data: 旧数据
        new_data: 新数据

    Returns:
        变更详情列表
    """
    changes = []

    # 字段名称映射
    field_names = {
        'accept_vip': '接受VIP',
        'accept_next_day': '接受隔天',
        'travel_date': '出行日期',
        'customer': '客户姓名',
        'price': '价格',
        'remark': '备注',
        'clients': '申请人信息',
        'visaType': '签证类型',
        'dateRangeList': '日期范围',
        'order_status': '订单状态',
        'action': '操作类型',
        'redis_key': 'Redis键名',
        'redis_status': 'Redis状态',
        'redis_exists': 'Redis数据存在',
        'redis_updated': 'Redis已更新',
        'redis_create_time': 'Redis创建时间'
    }

    # 比较基本字段
    basic_fields = ['accept_vip', 'accept_next_day', 'travel_date', 'customer', 'price', 'remark',
                    'order_status', 'action', 'redis_status', 'redis_exists', 'redis_updated']

    for field in basic_fields:
        old_value = old_data.get(field)
        new_value = new_data.get(field)

        # 特殊处理价格
        if field == 'price':
            old_value = int(old_value) if old_value is not None and str(old_value).isdigit() else old_value
            new_value = int(new_value) if new_value is not None and str(new_value).isdigit() else new_value

        if old_value != new_value:
            # 特殊处理布尔值
            if isinstance(old_value, bool) or isinstance(new_value, bool):
                old_display = "是" if old_value else "否"
                new_display = "是" if new_value else "否"
            # 特殊处理日期格式
            elif field == 'travel_date':
                old_display = _format_travel_date(old_value)
                new_display = _format_travel_date(new_value)
            # 特殊处理操作类型
            elif field == 'action':
                action_map = {
                    'reschedule_request': '重新安排请求',
                    'reschedule_completed': '重新安排完成',
                    'status_update_request': '状态更新请求',
                    'status_update_completed': '状态更新完成',
                    'create': '创建订单',
                    'update': '更新信息',
                    'delete': '删除订单',
                    'status_change': '状态变更',
                    'payment_confirmed': '支付确认',
                    'appointment_booked': '预约成功',
                    'appointment_cancelled': '预约取消',
                    '重新预约': '重新预约',
                    '重新预约前': '重新预约前'
                }
                old_display = action_map.get(old_value, str(old_value)) if old_value else "空"
                new_display = action_map.get(new_value, str(new_value)) if new_value else "空"
            # 特殊处理订单状态
            elif field == 'order_status':
                status_map = {
                    'pending': '待处理',
                    'wait_registe': '等待注册',
                    'registe_error': '注册失败',
                    'registe_success': '注册成功',
                    'schedule_error': '预约失败',
                    'appointment_downloaded': '预约信已下载',
                    'appointment_canceled': '预约已取消',
                    'account_deleted': '账号已删除',
                    'waiting_for_payment': '待支付',
                    'payed': '已支付',
                    'completed': '已完成',
                    'deleted': '已删除',
                    'pause': '已暂停'
                }
                old_display = status_map.get(old_value, str(old_value)) if old_value else "空"
                new_display = status_map.get(new_value, str(new_value)) if new_value else "空"
            else:
                old_display = str(old_value) if old_value is not None else "空"
                new_display = str(new_value) if new_value is not None else "空"

            changes.append({
                "field": field_names.get(field, field),
                "old_value": old_display,
                "new_value": new_display
            })

    # 特殊处理Redis创建时间字段
    old_redis_time = old_data.get('redis_create_time')
    new_redis_time = new_data.get('redis_create_time')
    if old_redis_time != new_redis_time:
        old_display = "空"
        new_display = "空"

        if old_redis_time is not None:
            try:
                import datetime
                old_display = datetime.datetime.fromtimestamp(int(old_redis_time)).strftime('%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                old_display = str(old_redis_time)

        if new_redis_time is not None:
            try:
                import datetime
                new_display = datetime.datetime.fromtimestamp(int(new_redis_time)).strftime('%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                new_display = str(new_redis_time)

        changes.append({
            "field": field_names.get('redis_create_time', 'Redis创建时间'),
            "old_value": old_display,
            "new_value": new_display
        })

    # 详细比较客户信息
    old_clients = old_data.get('clients', [])
    new_clients = new_data.get('clients', [])

    if old_clients != new_clients:
        # 比较客户数量
        old_count = len(old_clients) if isinstance(old_clients, list) else 0
        new_count = len(new_clients) if isinstance(new_clients, list) else 0

        if old_count != new_count:
            changes.append({
                "field": "申请人数量",
                "old_value": f"{old_count}人",
                "new_value": f"{new_count}人"
            })

        # 详细比较每个客户的信息
        if isinstance(old_clients, list) and isinstance(new_clients, list):
            max_len = max(len(old_clients), len(new_clients))
            for i in range(max_len):
                old_client = old_clients[i] if i < len(old_clients) else {}
                new_client = new_clients[i] if i < len(new_clients) else {}

                if old_client != new_client:
                    client_changes = _compare_client_details(old_client, new_client, i + 1)
                    changes.extend(client_changes)

    # 比较签证类型
    old_visa = old_data.get('visaType', [])
    new_visa = new_data.get('visaType', [])

    if old_visa != new_visa:
        old_visa_str = _format_visa_types(old_visa)
        new_visa_str = _format_visa_types(new_visa)

        changes.append({
            "field": "签证类型",
            "old_value": old_visa_str,
            "new_value": new_visa_str
        })

        # 详细比较签证类型变化
        if isinstance(old_visa, list) and isinstance(new_visa, list):
            visa_changes = _compare_visa_details(old_visa, new_visa)
            changes.extend(visa_changes)

    # 比较日期范围
    old_dates = old_data.get('dateRangeList', [])
    new_dates = new_data.get('dateRangeList', [])
    if old_dates != new_dates:
        old_dates_str = _format_date_ranges(old_dates)
        new_dates_str = _format_date_ranges(new_dates)

        changes.append({
            "field": "日期范围",
            "old_value": old_dates_str,
            "new_value": new_dates_str
        })

    return changes


def _format_visa_types(visa_types: list) -> str:
    """格式化签证类型显示"""
    if not visa_types or not isinstance(visa_types, list):
        return "无"

    formatted = []
    for visa in visa_types:
        if isinstance(visa, list) and len(visa) >= 4:
            # [mission_code, center_code, visa_type, visa_code]
            formatted.append(f"{visa[0]}/{visa[2]}/{visa[3]}")

    return ", ".join(formatted) if formatted else "无"


def _format_date_ranges(date_ranges: list) -> str:
    """格式化日期范围显示"""
    if not date_ranges or not isinstance(date_ranges, list):
        return "无"

    formatted = []
    for date_range in date_ranges:
        if isinstance(date_range, list) and len(date_range) >= 2:
            formatted.append(f"{date_range[0]} ~ {date_range[1]}")

    return ", ".join(formatted) if formatted else "无"


def _format_travel_date(date_str) -> str:
    """格式化出行日期显示"""
    if not date_str:
        return "空"

    # 处理ISO格式日期
    if isinstance(date_str, str) and 'T' in date_str:
        try:
            from datetime import datetime
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d')
        except:
            return str(date_str)

    return str(date_str)


def _compare_client_details(old_client: dict, new_client: dict, client_index: int) -> list:
    """详细比较客户信息变化"""
    changes = []

    # 客户信息字段映射
    client_field_names = {
        'name': '姓名',
        'surname_pinyin': '姓拼音',
        'firstname_pinyin': '名拼音',
        'dob': '出生日期',
        'passport': '护照号码',
        'passport_expire': '护照有效期',
        'gender': '性别',
        'nationality': '国籍',
        'passport_date': '护照签发日期',
        'sign_location': '签发地点',
        'bornplace': '出生地点',
        'marital_status': '婚姻状况',
        'passport_image': '护照图片',
        'avatar_image': '头像图片'
    }

    # 比较重要字段
    important_fields = ['name', 'surname_pinyin', 'firstname_pinyin', 'dob', 'passport',
                        'passport_expire', 'gender', 'nationality']

    for field in important_fields:
        old_value = old_client.get(field)
        new_value = new_client.get(field)

        if old_value != new_value:
            # 性别特殊处理
            if field == 'gender':
                old_display = "男" if old_value == 'M' else "女" if old_value == 'F' else str(old_value) if old_value else "空"
                new_display = "男" if new_value == 'M' else "女" if new_value == 'F' else str(new_value) if new_value else "空"
            else:
                old_display = str(old_value) if old_value else "空"
                new_display = str(new_value) if new_value else "空"

            changes.append({
                "field": f"申请人{client_index} - {client_field_names.get(field, field)}",
                "old_value": old_display,
                "new_value": new_display
            })

    return changes


def _compare_visa_details(old_visa: list, new_visa: list) -> list:
    """详细比较签证类型变化"""
    changes = []

    # 比较签证数量
    if len(old_visa) != len(new_visa):
        changes.append({
            "field": "签证类型数量",
            "old_value": f"{len(old_visa)}个",
            "new_value": f"{len(new_visa)}个"
        })

    # 详细比较每个签证类型
    max_len = max(len(old_visa), len(new_visa))
    for i in range(max_len):
        old_v = old_visa[i] if i < len(old_visa) else []
        new_v = new_visa[i] if i < len(new_visa) else []

        if old_v != new_v:
            old_display = _format_single_visa_type(old_v)
            new_display = _format_single_visa_type(new_v)

            changes.append({
                "field": f"签证类型{i + 1}",
                "old_value": old_display,
                "new_value": new_display
            })

    return changes


def _format_single_visa_type(visa_type) -> str:
    """格式化单个签证类型"""
    if not visa_type or not isinstance(visa_type, list):
        return "空"

    if len(visa_type) >= 4:
        # [mission_code, center_code, visa_type, visa_code]
        return f"{visa_type[0]} - {visa_type[2]} - {visa_type[3]}"

    return str(visa_type)


def _get_order_old_data(cursor, order_id: str) -> dict:
    """获取订单旧数据"""
    # 获取订单基本信息
    cursor.execute("""
        SELECT accept_vip, accept_next_day, travel_date, customer, price, remark, order_status
        FROM orders WHERE order_id = %s
    """, (order_id,))
    order_info = cursor.fetchone()

    # 获取客户信息
    cursor.execute("""
        SELECT name, surname_pinyin, firstname_pinyin, dob, passport,
               passport_expire, passport_image, avatar_image, gender,
               nationality, passport_date, sign_location, bornplace,
               marital_status, country, region
        FROM clients WHERE order_id = %s
    """, (order_id,))
    clients = []
    for row in cursor.fetchall():
        client = dict(zip([desc.name for desc in cursor.description], row))
        # 处理日期格式
        for date_field in ['dob', 'passport_expire', 'passport_date']:
            if isinstance(client.get(date_field), (datetime.date, datetime.datetime)):
                client[date_field] = client[date_field].strftime("%Y-%m-%d")
        clients.append(client)

    # 获取签证类型
    cursor.execute("""
        SELECT mission_code, center_code, visa_type, visa_code
        FROM visa_types WHERE order_id = %s
    """, (order_id,))
    visa_types = [list(row) for row in cursor.fetchall()]

    # 获取日期范围
    cursor.execute("""
        SELECT start_date, end_date
        FROM date_ranges WHERE order_id = %s
    """, (order_id,))
    date_ranges = [
        [start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')]
        for start, end in cursor.fetchall()
    ]

    return {
        "clients": clients,
        "visaType": visa_types,
        "dateRangeList": date_ranges,
        "accept_vip": order_info[0],
        "accept_next_day": order_info[1],
        "travel_date": order_info[2],
        "customer": order_info[3],
        "price": str(order_info[4]) if order_info[4] is not None else "",
        "remark": order_info[5],
        "order_status": order_info[6]
    }


def _check_spain_visa_status(order_data: EditOrderData, old_visa_types: list) -> bool:
    """检查西班牙签证状态是否允许编辑"""
    # 检查新订单是否有西班牙签证
    new_spain_visa = next(
        (visa for visa in order_data.visaType if "spain" in visa),
        None
    )

    if not new_spain_visa:
        return True

    # 检查旧订单是否有西班牙签证
    old_spain_visa = next(
        (visa for visa in old_visa_types if "spain" in visa),
        None
    )

    if not old_spain_visa:
        return True

    # 检查Redis中的状态
    client = order_data.clients[0]
    old_center_code = old_spain_visa[1]
    redis_key = f"{old_center_code}-{client.passport}"

    spain_data_str = redis_client_local.hget('spainUserDatas', redis_key)
    if spain_data_str:
        spain_data = json.loads(spain_data_str)
        current_status = spain_data.get('status', '')
        return current_status == 'update_appointment'

    return True


def _insert_order_details(cursor, order_id: str, order_data: OrderData):
    """插入订单详细信息"""
    # 插入客户信息
    for client in order_data.clients:
        cursor.execute("""
            INSERT INTO clients (
                order_id, name, surname_pinyin, firstname_pinyin, dob,
                passport, passport_expire, passport_image, avatar_image,
                gender, nationality, passport_date, sign_location,
                bornplace, marital_status, country, region
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
        """, (
            order_id, client.name, client.surname_pinyin,
            client.firstname_pinyin, client.dob, client.passport,
            client.passport_expire, client.passport_image,
            client.avatar_image, client.gender, client.nationality,
            client.passport_date, client.sign_location,
            client.bornplace, client.marital_status,
            client.country, client.region
        ))

    # 插入签证类型
    for visa in order_data.visaType:
        cursor.execute("""
            INSERT INTO visa_types (
                order_id, mission_code, center_code, visa_type, visa_code
            ) VALUES (%s, %s, %s, %s, %s)
        """, (order_id, *visa))

    # 插入日期范围
    for start_date, end_date in order_data.dateRangeList:
        cursor.execute("""
            INSERT INTO date_ranges (order_id, start_date, end_date)
            VALUES (%s, %s, %s)
        """, (
            order_id,
            start_date.replace("/", "-"),
            end_date.replace("/", "-")
        ))


def _update_spain_visa_redis(order_data: EditOrderData, order_id: str, username: str, old_visa_types: list):
    """更新西班牙签证Redis数据"""
    # 检查是否有西班牙签证
    new_spain_visa = next(
        (visa for visa in order_data.visaType if "spain" in visa),
        None
    )

    if not new_spain_visa:
        return

    old_spain_visa = next(
        (visa for visa in old_visa_types if "spain" in visa),
        None
    )

    if not old_spain_visa:
        return

    client = order_data.clients[0]
    new_center_code = new_spain_visa[1]
    old_center_code = old_spain_visa[1]
    visa_type = new_spain_visa[3]
    date_range = order_data.dateRangeList[0]

    # 获取原有数据
    old_redis_key = f"{old_center_code}-{client.passport}"
    spain_data_str = redis_client_local.hget('spainUserDatas', old_redis_key)

    if spain_data_str:
        spain_data = json.loads(spain_data_str)

        # 如果中心代码变了，删除旧的
        if old_center_code != new_center_code:
            redis_client_local.hdel('spainUserDatas', old_redis_key)

        # 更新数据
        spain_data.update({
            "birthday": client.dob.replace('-', '/'),
            "chnname": client.name,
            "endDate": date_range[1],
            "expiredDT": client.passport_expire.replace('-', '/'),
            "gender": client.gender,
            "name": client.firstname_pinyin,
            "passportNO": client.passport,
            "startDate": date_range[0],
            "visaTypeCode": visa_type,
            "xing": client.surname_pinyin,
            "remark": order_data.remark,
            "status": "update_info",
            "acceptND": 1 if order_data.accept_next_day else 2,
            "acceptVIP": 1 if order_data.accept_vip else 2,
            "countryCode": client.nationality,
            "from": username,
            "signLocation": client.sign_location,
            "bornplace": client.bornplace,
            "passportDate": client.passport_date.replace('-', '/'),
            "maritalStatus": client.marital_status,
            "travelDate": order_data.travel_date.replace('-', '/'),
            "centerCode": new_center_code,
            "order_id": order_id,
            "passport_image": client.passport_image or "",
            "avatar_image": client.avatar_image or ""
        })

        # 保存更新后的数据
        new_redis_key = f"{new_center_code}-{client.passport}"
        redis_client_local.hset(
            'spainUserDatas',
            new_redis_key,
            json.dumps(spain_data)
        )


def _update_visa_redis(order_data: EditOrderData, order_id: str, username: str, old_visa_types: list):
    """更新所有签证订单的Redis数据 - 包括西班牙和其他国家"""
    # 首先处理西班牙签证的特殊逻辑
    _update_spain_visa_redis(order_data, order_id, username, old_visa_types)

    # 更新vfs_user中的数据
    if not order_data.visaType:
        return

    # 获取唯一的签证类型（单选模式）
    selected_visa = order_data.visaType[0]
    is_spain = "spain" in selected_visa[0].lower() if selected_visa[0] else False

    # 转换clients为字典数组（原样存入）
    clients_data = []
    for client in order_data.clients:
        client_dict = {
            "name": client.name,
            "surname_pinyin": client.surname_pinyin,
            "firstname_pinyin": client.firstname_pinyin,
            "passport": client.passport,
            "dob": client.dob,
            "passport_expire": client.passport_expire,
            "gender": client.gender,
            "nationality": client.nationality,
            "passport_date": client.passport_date,
            "sign_location": client.sign_location,
            "bornplace": client.bornplace,
            "marital_status": client.marital_status,
            "passport_image": client.passport_image or "",
            "avatar_image": client.avatar_image or "",
            "country": client.country or "",
            "region": client.region or ""
        }
        clients_data.append(client_dict)

    # 转换dateRangeList为start_date和end_date的字典数组
    date_ranges_data = []
    for date_range in order_data.dateRangeList:
        if len(date_range) >= 2:
            date_ranges_data.append({
                "start_date": date_range[0],
                "end_date": date_range[1]
            })

    # 构建基础订单信息
    base_order_info = {
        "accept_vip": order_data.accept_vip,
        "accept_next_day": order_data.accept_next_day,
        "travel_date": order_data.travel_date,
        "customer": order_data.customer,
        "price": order_data.price,
        "remark": order_data.remark,
        "date_ranges": date_ranges_data
    }

    # 先删除旧的Redis记录
    _delete_old_visa_redis_records(order_id, old_visa_types)

    if is_spain:
        # 西班牙订单不需要写入vfs_user，只处理西班牙专用的Redis逻辑
        log_info(f"西班牙订单跳过vfs_user更新",
                 order_id=order_id,
                 mission_code=selected_visa[0])
    else:
        # 如果选择的不是西班牙签证，使用新逻辑处理
        visa_data = {
            "order_id": order_id,
            "visa_info": {
                "mission_code": selected_visa[0],  # 国家代码
                "center_code": selected_visa[1],   # 中心代码
                "visa_type": selected_visa[2],     # 签证类型
                "visa_code": selected_visa[3]      # 签证代码
            },
            "clients": clients_data,
            "order_info": base_order_info,
            "status": "pending",
            "updated_by": username,
            "updated_at": int(time.time())
        }

        # 获取旧数据的created_at
        existing_data_str = redis_client_local.hget('vfs_user', order_id)
        if existing_data_str:
            existing_data = json.loads(existing_data_str)
            visa_data["created_at"] = existing_data.get("created_at", int(time.time()))
            visa_data["created_by"] = existing_data.get("created_by", username)
        else:
            visa_data["created_at"] = int(time.time())
            visa_data["created_by"] = username

        # 直接使用order_id作为key写入Redis
        redis_client_local.hset(
            'vfs_user',
            order_id,
            json.dumps(visa_data, ensure_ascii=False)
        )

        log_info(f"非西班牙订单更新Redis vfs_user",
                 order_id=order_id,
                 mission_code=selected_visa[0],
                 center_code=selected_visa[1])


def _delete_old_visa_redis_records(order_id: str, old_visa_types: list):
    """删除旧的签证Redis记录"""
    # 检查旧订单是否为西班牙签证
    is_old_spain = False
    if old_visa_types and len(old_visa_types) > 0:
        first_old_visa = old_visa_types[0]
        if isinstance(first_old_visa, list) and len(first_old_visa) > 0:
            is_old_spain = "spain" in first_old_visa[0].lower()

    # 如果旧订单不是西班牙签证，才删除vfs_user记录
    if not is_old_spain:
        # 删除主记录
        redis_client_local.hdel('vfs_user', order_id)

        # 删除可能的分拆记录（基于旧的签证类型数量）
        if old_visa_types:
            for idx in range(len(old_visa_types)):
                visa_key = f"{order_id}_{idx}"
                redis_client_local.hdel('vfs_user', visa_key)


def _delete_spain_visa_redis(order_id: str):
    """删除西班牙签证Redis数据"""
    # 获取所有西班牙签证数据
    spain_data_all = redis_client_local.client.hgetall("spainUserDatas")

    for key, value in spain_data_all.items():
        data = json.loads(value)
        if data.get('order_id') == order_id:
            # 从活动数据中删除
            redis_client_local.hdel('spainUserDatas', key)
            # 添加到已删除数据
            data['status'] = 'deleted'
            redis_client_local.hset(
                'spainDeletedUserDatas',
                key,
                json.dumps(data)
            )


def _delete_visa_redis(order_id: str):
    """删除所有签证Redis数据 - 包括西班牙和其他国家"""
    # 首先处理西班牙签证的特殊逻辑
    _delete_spain_visa_redis(order_id)

    # 检查订单是否为西班牙签证（从数据库获取）
    is_spain_order = False
    try:
        with db_manager.get_db() as conn:
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cursor.execute(
                "SELECT visa_type FROM visa_types WHERE order_id = %s",
                (order_id,)
            )
            order_data = cursor.fetchone()
            if order_data and order_data['visa_type']:
                visa_types = order_data['visa_type']
                if isinstance(visa_types, list) and len(visa_types) > 0:
                    first_visa = visa_types[0]
                    if isinstance(first_visa, list) and len(first_visa) > 0:
                        is_spain_order = "spain" in first_visa[0].lower()
    except Exception as e:
        log_error(f"检查订单签证类型失败", error=e, order_id=order_id)

    # 如果是西班牙订单，不删除vfs_user记录（因为它本来就不应该在那里）
    if is_spain_order:
        log_info(f"西班牙订单跳过vfs_user删除", order_id=order_id)
        return

    # 获取所有可能的vfs_user相关记录并删除（仅非西班牙订单）
    deleted_count = 0

    # 删除主记录（非西班牙签证）
    vfs_data_str = redis_client_local.hget('vfs_user', order_id)
    if vfs_data_str:
        # 保存到已删除数据（可选）
        vfs_data = json.loads(vfs_data_str)
        vfs_data['status'] = 'deleted'
        vfs_data['deleted_at'] = int(time.time())

        # 保存到已删除的hash表中（如果需要）
        redis_client_local.hset(
            'vfs_user_deleted',
            order_id,
            json.dumps(vfs_data, ensure_ascii=False)
        )

        # 从活动数据中删除
        redis_client_local.hdel('vfs_user', order_id)
        deleted_count += 1

    # 删除可能的分拆记录（非西班牙签证的多个记录）
    # 尝试删除最多20个可能的分拆记录（假设一个订单不会有超过20个签证类型）
    for idx in range(20):
        visa_key = f"{order_id}_{idx}"
        vfs_data_str = redis_client_local.hget('vfs_user', visa_key)
        if vfs_data_str:
            # 保存到已删除数据
            vfs_data = json.loads(vfs_data_str)
            vfs_data['status'] = 'deleted'
            vfs_data['deleted_at'] = int(time.time())

            # 保存到已删除的hash表中
            redis_client_local.hset(
                'vfs_user_deleted',
                visa_key,
                json.dumps(vfs_data, ensure_ascii=False)
            )

            # 从活动数据中删除
            redis_client_local.hdel('vfs_user', visa_key)
            deleted_count += 1
        else:
            # 如果连续几个索引都不存在，就停止尝试
            if idx > 5:  # 允许一些索引不连续
                break

    if deleted_count > 0:
        log_info(f"非西班牙订单从Redis vfs_user删除",
                 order_id=order_id,
                 deleted_records=deleted_count)

# ==================== Hook 消息发送函数 ====================


async def send_hook_message(hook_urls: list, message: str):
    """
    发送hook消息到指定的URL列表
    随机选择一个URL发送消息
    """
    if not hook_urls or not message:
        return

    try:
        # 随机选择一个hook URL
        import random
        selected_url = random.choice(hook_urls)

        # 构造消息数据
        post_data = {
            "msgtype": "text",
            "text": {
                "content": message
            }
        }

        # 发送POST请求
        response = requests.post(selected_url, json=post_data, timeout=10)

        print(f"Hook消息发送成功: {selected_url}, 状态码: {response.status_code}")

    except Exception as e:
        print(f"Hook消息发送失败: {str(e)}")


# ==================== 西班牙预约系统回调接口 ====================


@app.post("/api/spain-booking-callback", summary="西班牙预约系统回调", tags=["回调"])
async def spain_booking_callback(data: dict):
    """
    西班牙预约系统回调接口
    用于接收预约状态变更通知
    """
    request_id = str(uuid.uuid4())[:8]

    try:
        # 获取回调数据
        order_id = data.get("order_id")
        status = data.get("status")  # appointment_canceled, account_deleted, registe_success, registe_error, schedule_error, waiting_for_payment, appointment_downloaded
        message = data.get("message", "")
        date_info = data.get("date_info", "")
        payment_qrcode = data.get("payment_qrcode", "")  # 付款二维码
        email = data.get("email", "")
        appointment_pdf_url = data.get("appointment_pdf_url", "")
        appointment_date = data.get("appointment_date", "")
        appointment_time = data.get("appointment_time", "")
        # 记录回调请求
        log_info("西班牙预约系统回调请求",
                 request_id=request_id,
                 order_id=order_id,
                 status=status,
                 has_message=bool(message),
                 has_qrcode=bool(payment_qrcode),
                 has_email=bool(email),
                 has_pdf_url=bool(appointment_pdf_url))

        # 验证必要参数
        if not order_id or not status:
            log_warning("回调参数不完整", request_id=request_id, order_id=order_id, status=status)
            return {"code": 0, "message": "缺少必要参数 order_id 或 status"}

        # 验证订单是否存在
        with db_manager.get_db() as conn:
            cur = conn.cursor()

            # 检查订单是否存在且未删除
            cur.execute("""
                SELECT order_id, order_status as current_status, operator, remark
                FROM orders
                WHERE order_id = %s AND order_status != 'deleted'
            """, (order_id,))

            order = cur.fetchone()
            if not order:
                log_warning("订单不存在", request_id=request_id, order_id=order_id)
                return {"code": 0, "message": f"订单 {order_id} 不存在或已删除"}

            current_status = order[1]
            operator_id = order[2]

            log_info("订单验证成功",
                     request_id=request_id,
                     order_id=order_id,
                     current_status=current_status,
                     operator_id=operator_id)

            # 获取客户信息用于丰富消息内容
            cur.execute("""
                SELECT name, passport
                FROM clients
                WHERE order_id = %s
                ORDER BY id LIMIT 1
            """, (order_id,))

            client = cur.fetchone()
            client_name = client[0] if client else "未知客户"
            client_passport = client[1] if client else "未知护照"

            log_info("获取客户信息",
                     request_id=request_id,
                     order_id=order_id,
                     client_name=client_name,
                     client_passport=client_passport)

            # 根据回调状态更新订单
            new_status = None
            update_message = ""

            # 构建基础信息
            base_info = f"【西班牙订单状态更新】\n订单号：{order_id}\n客户姓名：{client_name}\n护照号：{client_passport}\n预约类型：{date_info}\n"

            log_info("开始处理状态更新",
                     request_id=request_id,
                     order_id=order_id,
                     old_status=current_status,
                     new_status_request=status)

            if status == "registe_error":
                # 注册错误
                new_status = "registe_error"
                update_message = f"{base_info}状态：注册失败\n原因：{message}"

            elif status == "schedule_error":
                # 预约错误
                new_status = "schedule_error"
                update_message = f"{base_info}状态：预约失败\n原因：{message}"

            elif status == "waiting_for_payment":
                # 等待付款
                new_status = "waiting_for_payment"
                update_message = f"{base_info}状态：等待付款\n说明：{message}\n备注：{order[3]}"

            elif status == "appointment_downloaded":
                # 预约信已下载
                new_status = "appointment_downloaded"
                update_message = f"{base_info}状态：预约信已下载\n说明：{message}"

            elif status == "appointment_canceled":
                # 预约已取消
                new_status = "appointment_canceled"
                update_message = f"{base_info}状态：预约已取消\n原因：{message}"

            elif status == "account_deleted":
                # 账号已删除
                new_status = "account_deleted"
                update_message = f"{base_info}状态：账号已删除\n原因：{message}"

            elif status == "registe_success":
                # 账号已注册
                new_status = "registe_success"
                update_message = f"{base_info}状态：注册成功\n说明：{message}"
            elif status == "avatar_not_available":
                # 头像图片不可用
                new_status = "avatar_not_available"
                update_message = f"{base_info}状态：头像图片不可用\n说明：{message}"
            else:
                log_warning("未知状态", request_id=request_id, order_id=order_id, status=status)
                return {"code": 0, "message": f"未知状态：{status}"}

            log_info("状态映射完成",
                     request_id=request_id,
                     order_id=order_id,
                     old_status=current_status,
                     new_status=new_status)

            # 获取操作员ID用于后续发送hook消息
            operator_id = order[2]

            # 更新订单状态
            log_info("更新订单状态",
                     request_id=request_id,
                     order_id=order_id,
                     old_status=current_status,
                     new_status=new_status)

            cur.execute("""
                UPDATE orders
                SET order_status = %s, updated_at = CURRENT_TIMESTAMP
                WHERE order_id = %s
            """, (new_status, order_id))

            # 如果有付款二维码，存储到付款二维码表
            if payment_qrcode:
                log_info("存储付款二维码",
                         request_id=request_id,
                         order_id=order_id,
                         qrcode_length=len(payment_qrcode))

                # 使用 UPSERT 语法，如果订单已存在则更新，否则插入
                cur.execute("""
                    INSERT INTO payment_qrcodes (order_id, payment_qrcode, updated_at)
                    VALUES (%s, %s, CURRENT_TIMESTAMP)
                    ON CONFLICT (order_id)
                    DO UPDATE SET
                        payment_qrcode = EXCLUDED.payment_qrcode,
                        updated_at = CURRENT_TIMESTAMP
                """, (order_id, payment_qrcode))

            # 如果有邮箱，存储到订单邮箱表
            if email:
                log_info("存储订单邮箱",
                         request_id=request_id,
                         order_id=order_id,
                         email=email)

                cur.execute("""
                    INSERT INTO order_emails (order_id, email, updated_at)
                    VALUES (%s, %s, CURRENT_TIMESTAMP)
                    ON CONFLICT (order_id)
                    DO UPDATE SET
                        email = EXCLUDED.email,
                        updated_at = CURRENT_TIMESTAMP
                """, (order_id, email))

            # 如果有预约PDF URL，存储到预约PDF表
            if appointment_pdf_url or appointment_date:
                # 从URL中提取文件名
                import os
                pdf_filename = os.path.basename(appointment_pdf_url.split('?')[0])  # 去除查询参数

                log_info("存储预约PDF",
                         request_id=request_id,
                         order_id=order_id,
                         pdf_url=appointment_pdf_url,
                         pdf_filename=pdf_filename)

                cur.execute("""
                    INSERT INTO appointment_pdfs (order_id, pdf_url, pdf_filename, appointment_date, appointment_time, updated_at)
                    VALUES (%s, %s, %s,%s, %s, CURRENT_TIMESTAMP)
                    ON CONFLICT (order_id)
                    DO UPDATE SET
                        pdf_url = EXCLUDED.pdf_url,
                        pdf_filename = EXCLUDED.pdf_filename,
                        appointment_date = EXCLUDED.appointment_date,
                        appointment_time = EXCLUDED.appointment_time,
                        updated_at = CURRENT_TIMESTAMP
                """, (order_id, appointment_pdf_url,  pdf_filename, appointment_date, appointment_time))

            # 如果有消息，存储到订单消息表
            if message:
                log_info("存储订单消息",
                         request_id=request_id,
                         order_id=order_id,
                         message_length=len(message),
                         message_type=status)

                cur.execute("""
                    INSERT INTO order_messages (order_id, message, message_type, status, updated_at)
                    VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
                    ON CONFLICT (order_id)
                    DO UPDATE SET
                        message = EXCLUDED.message,
                        message_type = EXCLUDED.message_type,
                        status = EXCLUDED.status,
                        updated_at = CURRENT_TIMESTAMP
                """, (order_id, message, status, new_status))

            # 获取该操作员的所有hook URL
            cur.execute("""
                SELECT hook_url FROM user_hooks
                WHERE user_id = %s AND hook_url IS NOT NULL AND hook_url != ''
            """, (operator_id,))

            hook_urls = [row[0] for row in cur.fetchall()]

            log_info("获取Hook URLs",
                     request_id=request_id,
                     order_id=order_id,
                     operator_id=operator_id,
                     hook_count=len(hook_urls))

            conn.commit()

            log_info("数据库事务提交成功",
                     request_id=request_id,
                     order_id=order_id,
                     new_status=new_status)

            # 发送hook消息（在数据库事务提交后）
            if hook_urls:
                log_info("开始发送Hook消息",
                         request_id=request_id,
                         order_id=order_id,
                         hook_count=len(hook_urls))
                if 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7b816326-5e5c-441d-aced-fc3d186addfe' in hook_urls and status != "waiting_for_payment":
                    hook_urls = ['https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=935fdfcd-5020-41b6-8232-accc74498b77']
                await send_hook_message(hook_urls, update_message)
                log_info("Hook消息发送完成",
                         request_id=request_id,
                         order_id=order_id)
            else:
                log_warning("无Hook URL可发送",
                            request_id=request_id,
                            order_id=order_id,
                            operator_id=operator_id)

            log_info("西班牙预约回调处理成功",
                     request_id=request_id,
                     order_id=order_id,
                     old_status=current_status,
                     new_status=new_status)

            return {
                "code": 1,
                "message": "回调处理成功",
                "data": {
                    "order_id": order_id,
                    "old_status": current_status,
                    "new_status": new_status,
                    "update_message": update_message
                }
            }

    except Exception as e:
        log_error("西班牙预约系统回调处理错误",
                  error=e,
                  request_id=request_id,
                  order_id=order_id if 'order_id' in locals() else 'unknown')
        return {"code": 0, "message": f"回调处理失败: {str(e)}"}


@app.post("/api/get-payment-qrcode", summary="获取付款二维码", tags=["西班牙预约"])
async def get_payment_qrcode(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """
    获取订单的付款二维码
    """
    try:
        order_id = data.get("order_id")
        if not order_id:
            return {"code": 0, "message": "缺少订单号"}

        with db_manager.get_db() as conn:
            cur = conn.cursor()

            # 检查订单权限
            if payload.permission not in ("admin", "kefu"):
                cur.execute("""
                    SELECT operator FROM orders
                    WHERE order_id = %s AND order_status != 'deleted'
                """, (order_id,))
                order = cur.fetchone()
                if not order or order[0] != payload.user_id:
                    return {"code": 0, "message": "无权限访问该订单"}

            # 获取付款二维码
            cur.execute("""
                SELECT payment_qrcode, created_at, updated_at
                FROM payment_qrcodes
                WHERE order_id = %s
            """, (order_id,))

            qrcode_data = cur.fetchone()
            if not qrcode_data:
                return {"code": 0, "message": "该订单暂无付款二维码"}

            return {
                "code": 1,
                "data": {
                    "order_id": order_id,
                    "payment_qrcode": qrcode_data[0],
                    "created_at": qrcode_data[1].strftime('%Y-%m-%d %H:%M:%S') if qrcode_data[1] else None,
                    "updated_at": qrcode_data[2].strftime('%Y-%m-%d %H:%M:%S') if qrcode_data[2] else None
                }
            }

    except Exception as e:
        print(f"获取付款二维码错误: {str(e)}")
        return {"code": 0, "message": f"获取失败: {str(e)}"}


@app.post("/api/get-order-email", summary="获取订单邮箱", tags=["西班牙预约"])
async def get_order_email(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """
    获取订单的邮箱地址
    """
    try:
        order_id = data.get("order_id")
        if not order_id:
            return {"code": 0, "message": "缺少订单号"}

        with db_manager.get_db() as conn:
            cur = conn.cursor()

            # 检查订单权限
            if payload.permission not in ("admin", "kefu"):
                cur.execute("""
                    SELECT operator FROM orders
                    WHERE order_id = %s AND order_status != 'deleted'
                """, (order_id,))
                order = cur.fetchone()
                if not order or order[0] != payload.user_id:
                    return {"code": 0, "message": "无权限访问该订单"}

            # 获取订单邮箱
            cur.execute("""
                SELECT email, created_at, updated_at
                FROM order_emails
                WHERE order_id = %s
            """, (order_id,))

            email_data = cur.fetchone()
            if not email_data:
                return {"code": 0, "message": "该订单暂无邮箱信息"}

            return {
                "code": 1,
                "data": {
                    "order_id": order_id,
                    "email": email_data[0],
                    "created_at": email_data[1].strftime('%Y-%m-%d %H:%M:%S') if email_data[1] else None,
                    "updated_at": email_data[2].strftime('%Y-%m-%d %H:%M:%S') if email_data[2] else None
                }
            }

    except Exception as e:
        print(f"获取订单邮箱错误: {str(e)}")
        return {"code": 0, "message": f"获取失败: {str(e)}"}


@app.post("/api/get-appointment-pdf", summary="获取预约PDF", tags=["西班牙预约"])
async def get_appointment_pdf(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """
    获取订单的预约PDF信息
    """
    try:
        order_id = data.get("order_id")
        if not order_id:
            return {"code": 0, "message": "缺少订单号"}

        with db_manager.get_db() as conn:
            cur = conn.cursor()

            # 检查订单权限
            if payload.permission not in ("admin", "kefu"):
                cur.execute("""
                    SELECT operator FROM orders
                    WHERE order_id = %s AND order_status != 'deleted'
                """, (order_id,))
                order = cur.fetchone()
                if not order or order[0] != payload.user_id:
                    return {"code": 0, "message": "无权限访问该订单"}

            # 获取预约PDF信息
            cur.execute("""
                SELECT pdf_url, pdf_filename, file_size, download_count, created_at, updated_at
                FROM appointment_pdfs
                WHERE order_id = %s
            """, (order_id,))

            pdf_data = cur.fetchone()
            if not pdf_data:
                return {"code": 0, "message": "该订单暂无预约PDF"}

            # 更新下载次数
            cur.execute("""
                UPDATE appointment_pdfs
                SET download_count = download_count + 1
                WHERE order_id = %s
            """, (order_id,))
            conn.commit()

            return {
                "code": 1,
                "data": {
                    "order_id": order_id,
                    "pdf_url": pdf_data[0],
                    "pdf_filename": pdf_data[1],
                    "file_size": pdf_data[2],
                    "download_count": pdf_data[3] + 1,  # 返回更新后的下载次数
                    "created_at": pdf_data[4].strftime('%Y-%m-%d %H:%M:%S') if pdf_data[4] else None,
                    "updated_at": pdf_data[5].strftime('%Y-%m-%d %H:%M:%S') if pdf_data[5] else None
                }
            }

    except Exception as e:
        print(f"获取预约PDF错误: {str(e)}")
        return {"code": 0, "message": f"获取失败: {str(e)}"}


@app.post("/api/get-order-message", summary="获取订单消息", tags=["西班牙预约"])
async def get_order_message(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """
    获取订单的消息内容
    """
    try:
        order_id = data.get("order_id")
        if not order_id:
            return {"code": 0, "message": "缺少订单号"}

        with db_manager.get_db() as conn:
            cur = conn.cursor()

            # 检查订单权限
            if payload.permission not in ("admin", "kefu"):
                cur.execute(
                    """
                    SELECT operator FROM orders
                    WHERE order_id = %s AND order_status != 'deleted'
                    """,
                    (order_id,)
                )
                order = cur.fetchone()
                if not order or order[0] != payload.user_id:
                    return {"code": 0, "message": "无权限访问该订单"}

            # 获取订单消息
            cur.execute(
                """
                SELECT message, message_type, status, created_at, updated_at
                FROM order_messages
                WHERE order_id = %s
                """,
                (order_id,)
            )

            message_data = cur.fetchone()
            if not message_data:
                return {"code": 0, "message": "该订单暂无消息记录"}

            return {
                "code": 1,
                "data": {
                    "order_id": order_id,
                    "message": message_data[0],
                    "message_type": message_data[1],
                    "status": message_data[2],
                    "created_at": message_data[3].strftime('%Y-%m-%d %H:%M:%S') if message_data[3] else None,
                    "updated_at": message_data[4].strftime('%Y-%m-%d %H:%M:%S') if message_data[4] else None,
                },
            }
    except Exception as e:
        return {"code": 0, "message": f"该订单暂无消息记录: {str(e)}"}
# ==================== VFS 即时预约 API ====================


def build_cf_params_from_login_user(login_user: dict) -> dict:
    """从登录用户对象构建 cf 参数（ITA 优先使用）。"""
    try:
        if not isinstance(login_user, dict):
            return {}
        cookie = (login_user.get("cf_clearance") or "").strip()
        proxy = (login_user.get("login_proxy") or "").strip()
        ua = (login_user.get("cf_user_agent") or "").strip()
        cf = {}
        if cookie:
            cf["cookie"] = cookie
        if proxy:
            cf["proxy"] = proxy
        if ua:
            cf["user_agent"] = ua
        return cf
    except Exception:
        return {}


@app.get("/api/vfs/orders/{order_id}/available-dates", summary="获取可预约日期并建立会话", tags=["VFS预约"])
async def vfs_get_available_dates(order_id: str, month: str | None = None, session_id: str | None = None, payload: UserPayload = Depends(verify_jwt_token)):
    request_id = generate_request_id()
    try:
        # 1) 校验订单权限与基础信息
        with db_manager.get_db() as conn:
            cur = conn.cursor()
            if payload.permission in ("admin", "kefu"):
                cur.execute(
                    """
                    SELECT order_id, operator, is_vfs_order, order_status,
                           mission_code, center_code, visa_code, urn, vfs_account
                    FROM orders WHERE order_id = %s
                    """,
                    (order_id,)
                )
            else:
                cur.execute(
                    """
                    SELECT order_id, operator, is_vfs_order, order_status,
                           mission_code, center_code, visa_code, urn, vfs_account
                    FROM orders WHERE order_id = %s AND operator = %s
                    """,
                    (order_id, payload.user_id)
                )
            row = cur.fetchone()
        if not row:
            return {"code": 0, "message": "订单不存在或无权限"}

        _, operator_id, is_vfs_order, order_status, mission_code, center_code, visa_code, urn, vfs_account = row
        if not is_vfs_order:
            return {"code": 0, "message": "该订单非VFS订单，无法立即预约"}
        if str(order_status).lower() in ("deleted", "appointment_canceled", "appointment_downloaded", "payed"):
            return {"code": 0, "message": f"当前状态({order_status})不支持立即预约"}

        # 2) 计算当月起止（YYYY-MM 传入，否则用今天所在月）
        today = datetime.date.today()
        try:
            tgt = datetime.datetime.strptime((month or today.strftime("%Y-%m")) + "-01", "%Y-%m-%d").date()
        except Exception:
            tgt = datetime.date(today.year, today.month, 1)
        start_date = tgt.replace(day=1)
        # 计算月末
        if tgt.month == 12:
            next_first = datetime.date(tgt.year + 1, 1, 1)
        else:
            next_first = datetime.date(tgt.year, tgt.month + 1, 1)
        end_date = next_first - datetime.timedelta(days=1)

        date_range = {
            "order_id": order_id,
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "start_date_calendar": start_date.strftime("%Y-%m-%d"),
            "end_date_calendar": end_date.strftime("%Y-%m-%d"),
        }

        # 3) 组装 customer
        customer = {
            "order_id": order_id,
            "mission_code": mission_code,
            "center_code": center_code,
            "visa_code": visa_code,
            "urn": urn or "",
            "vfs_account": vfs_account or "",
        }

        # 4) 会话处理：复用或创建
        session_key = None
        if session_id:
            # 复用会话
            session_key = f"vfs_session:{order_id}:{session_id}"
            session_raw = redis_client_local.get(session_key)
            if session_raw:
                sess = json.loads(session_raw)
                login_user = sess.get("login_user") or {}
            else:
                # 如果传入 session_id 但不存在，则退化为新会话
                session_id = None
                login_user = None
        else:
            login_user = None

        # 获取或挑选登陆人，并为本次请求获取CF参数
        if not login_user:
            if 'get_login_user_for_country' not in globals():
                return {"code": 0, "message": "VFS模块未加载，无法获取登录用户"}
            login_user = get_login_user_for_country(mission_code, vfs_account)
            if not login_user:
                return {"code": 0, "message": "未找到可用的登录用户，请稍后重试"}
        # 5) 按月扫描日历（403 重试；ITA 优先用登录用户自带 cf 参数，403 时更换账号）
        if 'scan_calendar_for_customer' not in globals():
            return {"code": 0, "message": "VFS模块未加载，无法扫描可预约日期"}

        dates_raw, r_auth = None, None
        cf_params_used = None
        last_err = None
        max_attempts = 3

        is_ita = str(mission_code).lower() == "ita"
        cf_from_login = build_cf_params_from_login_user(login_user) if is_ita else {}

        for attempt in range(1, max_attempts + 1):
            try:
                if is_ita and attempt == 1 and cf_from_login:
                    cf_try = cf_from_login
                    log_info("使用登录用户内置cf参数", request_id=request_id, order_id=order_id, attempt=attempt)
                elif is_ita:
                    # ITA：更换账号 + 新 cf 参数（若账号缺少cf，则回退到池）
                    try:
                        new_login = get_login_user_for_country(mission_code, vfs_account)
                        if new_login:
                            login_user = new_login
                    except Exception as _:
                        pass
                    cf_try = build_cf_params_from_login_user(login_user) or (fetch_cf_bypass_params(None) if 'fetch_cf_bypass_params' in globals() else {})
                    log_info("403后更换登录用户并轮换cf参数", request_id=request_id, order_id=order_id, attempt=attempt)
                else:
                    # 非 ITA：沿用原有池策略
                    cf_try = fetch_cf_bypass_params(None) if 'fetch_cf_bypass_params' in globals() else {}

                dr, ra = scan_calendar_for_customer(customer, date_range, login_user, cf_try or {})
                dates_raw, r_auth = (dr or []), ra
                cf_params_used = cf_try
                break
            except Exception as e:
                last_err = e
                emsg = str(e).lower()
                if ('403' in emsg) or ('forbidden' in emsg):
                    log_info("日历请求403，轮换cf_params重试", request_id=request_id, order_id=order_id, attempt=attempt)
                    continue
                else:
                    raise

        if dates_raw is None:
            return {"code": 0, "message": f"获取可预约日期失败: {str(last_err)}" if last_err else "获取可预约日期失败"}

        dates_raw = dates_raw or []

        # 规范化为 YYYY-MM-DD
        def norm_date(d: str) -> str:
            if not d:
                return d
            if "/" in d and 'convert_mmddyyyy_to_yyyy_mm_dd' in globals():
                nd = convert_mmddyyyy_to_yyyy_mm_dd(d)
                return nd or d
            return d
        dates = sorted(list({norm_date(d) for d in dates_raw if d}))

        # 6) 建立或续期会话（5分钟）
        if not session_id:
            session_id = str(uuid.uuid4())[:16]
            session_key = f"vfs_session:{order_id}:{session_id}"
        session_payload = {
            "r_auth": r_auth or "",
            "login_user": login_user,
            "customer": customer,
            "cf_params": (cf_params_used or {}),  # 保存给 timeslots 复用
            "created_at": int(time.time())
        }
        redis_client_local.set(session_key, json.dumps(session_payload, ensure_ascii=False), 300)

        return {"code": 1, "data": {"dates": dates, "session_id": session_id}}

    except Exception as e:
        log_error("获取可预约日期失败", error=e, request_id=request_id, order_id=order_id)
        return {"code": 0, "message": f"获取可预约日期失败: {str(e)}"}


@app.get("/api/vfs/orders/{order_id}/timeslots", summary="获取指定日期的时间段", tags=["VFS预约"])
async def vfs_get_timeslots(order_id: str, date: str, session_id: str, payload: UserPayload = Depends(verify_jwt_token)):
    request_id = generate_request_id()
    try:
        # 权限校验：存在且可访问
        with db_manager.get_db() as conn:
            cur = conn.cursor()
            if payload.permission in ("admin", "kefu"):
                cur.execute("SELECT operator FROM orders WHERE order_id=%s", (order_id,))
            else:
                cur.execute("SELECT operator FROM orders WHERE order_id=%s AND operator=%s", (order_id, payload.user_id))
            row = cur.fetchone()
        if not row:
            return {"code": 0, "message": "订单不存在或无权限"}

        # 从Redis取会话
        session_key = f"vfs_session:{order_id}:{session_id}"
        session_raw = redis_client_local.get(session_key)
        if not session_raw:
            return {"code": 0, "message": "会话已过期，请重新选择日期"}
        session_obj = json.loads(session_raw)

        r_auth = session_obj.get("r_auth")
        login_user = session_obj.get("login_user") or {}
        customer = session_obj.get("customer") or {}
        cf_params = session_obj.get("cf_params") or {}

        if 'request_time_slots_for_date' not in globals():
            return {"code": 0, "message": "VFS模块未加载，无法获取时间段"}

        # 优先使用会话中的 cf_params；若403则临时轮换一次
        try:
            slots = request_time_slots_for_date(customer, date, login_user, cf_params, r_auth)
        except Exception as e:
            emsg = str(e).lower()
            if ('403' in emsg) or ('forbidden' in emsg):
                tmp_cf = fetch_cf_bypass_params(None) if 'fetch_cf_bypass_params' in globals() else {}
                slots = request_time_slots_for_date(customer, date, login_user, tmp_cf, r_auth)
            else:
                raise
        slots = slots or []

        # 归一化
        normalized = []
        for s in slots:
            try:
                allocation_id = s.get('allocationId') or s.get('slotId') or s.get('allocationID')
                slot_label = s.get('slot') or s.get('time') or s.get('label')
                normalized.append({
                    "allocationId": str(allocation_id) if allocation_id is not None else None,
                    "slot": slot_label or "",
                })
            except Exception:
                pass

        return {"code": 1, "data": {"slots": [ns for ns in normalized if ns.get("allocationId")]}}

    except Exception as e:
        log_error("获取时间段失败", error=e, request_id=request_id, order_id=order_id)
        return {"code": 0, "message": f"获取时间段失败: {str(e)}"}


class VfsScheduleRequest(BaseModel):
    allocationId: str
    date: str
    session_id: str


@app.post("/api/vfs/orders/{order_id}/schedule", summary="立即预约下单", tags=["VFS预约"])
async def vfs_schedule_now(order_id: str, body: VfsScheduleRequest, payload: UserPayload = Depends(verify_jwt_token)):
    request_id = generate_request_id()
    try:
        # 权限校验
        with db_manager.get_db() as conn:
            cur = conn.cursor()
            if payload.permission in ("admin", "kefu"):
                cur.execute(
                    "SELECT order_status FROM orders WHERE order_id=%s",
                    (order_id,)
                )
            else:
                cur.execute(
                    "SELECT order_status FROM orders WHERE order_id=%s AND operator=%s",
                    (order_id, payload.user_id)
                )
            row = cur.fetchone()
        if not row:
            return {"code": 0, "message": "订单不存在或无权限"}
        current_status = (row[0] or "").lower()
        if current_status in ("deleted", "appointment_canceled", "appointment_downloaded", "payed"):
            return {"code": 0, "message": f"当前状态({current_status})不支持立即预约"}

        # 取会话
        session_key = f"vfs_session:{order_id}:{body.session_id}"
        session_raw = redis_client_local.get(session_key)
        if not session_raw:
            return {"code": 0, "message": "会话已过期，请重新选择日期"}
        session_obj = json.loads(session_raw)

        r_auth = session_obj.get("r_auth")
        login_user = session_obj.get("login_user") or {}
        customer = session_obj.get("customer") or {}

        # 调用下单（为本次请求获取CF参数）
        if 'schedule_appointment' not in globals():
            return {"code": 0, "message": "VFS模块未加载，无法提交预约"}

        is_ita = str((customer.get("mission_code") or "")).lower() == "ita"
        cf_from_login = build_cf_params_from_login_user(login_user) if is_ita else {}
        random_slot = {"allocationId": body.allocationId, "slot": ""}
        result = None
        max_attempts = 3
        for attempt in range(1, max_attempts + 1):
            try:
                if is_ita and attempt == 1 and cf_from_login:
                    cf_try = cf_from_login
                    log_info("使用登录用户内置cf参数", request_id=request_id, order_id=order_id, attempt=attempt)
                elif is_ita:
                    # ITA：更换账号 + 优先使用新账号内置 cf 参数
                    try:
                        new_login = get_login_user_for_country(customer.get("mission_code"), customer.get("vfs_account"))
                        if new_login:
                            login_user = new_login
                    except Exception:
                        pass
                    cf_try = build_cf_params_from_login_user(login_user) or (fetch_cf_bypass_params(None) if 'fetch_cf_bypass_params' in globals() else {})
                    log_info("403后更换登录用户并轮换cf参数", request_id=request_id, order_id=order_id, attempt=attempt)
                else:
                    # 非 ITA：沿用池 cf 参数
                    cf_try = fetch_cf_bypass_params(None) if 'fetch_cf_bypass_params' in globals() else {}

                result = schedule_appointment(customer, login_user, random_slot, cf_try or {}, r_auth)
                break
            except Exception as e:
                emsg = str(e).lower()
                if ('403' in emsg) or ('forbidden' in emsg):
                    log_info("下单403，轮换策略重试", request_id=request_id, order_id=order_id, attempt=attempt)
                    continue
                else:
                    raise

        # 清理会话
        try:
            redis_client_local.delete(session_key)
        except Exception:
            pass

        if not result:
            return {"code": 0, "message": "预约提交失败，请重试或更换时间段"}
        log_info("预约提交结果", request_id=request_id, order_id=order_id, result=result)
        # 判定状态
        is_booked = bool(result.get("IsAppointmentBooked"))
        need_pay = bool(result.get("IsPaymentRequired"))
        new_status = "waiting_for_payment" if need_pay else ("wait_download_pdf" if is_booked else current_status)

        # 若需要支付，尝试生成并保存付款二维码（非阻塞）
        if need_pay:
            try:
                # 独立获取一次CF参数用于支付二维码流程
                pay_cf = fetch_cf_bypass_params(None) if 'fetch_cf_bypass_params' in globals() else {}
                ua_from_cf = (pay_cf.get("user_agent") if pay_cf else None)
                cf_clearance = (pay_cf.get("cookie") if pay_cf else None)
                ltsn = login_user.get("ltsn", "")
                cookie_header = ltsn if ltsn else ""
                if cf_clearance:
                    cookie_header = f"{cookie_header}; cf_clearance={cf_clearance}" if cookie_header else f"cf_clearance={cf_clearance}"
                proxy = (pay_cf.get("proxy") if pay_cf else None)
                if 'process_payment_flow' in globals():
                    try:
                        ok_qr = process_payment_flow(result, customer, login_user, proxy, ua_from_cf, cookie_header)
                        log_info("触发支付二维码生成", request_id=request_id, order_id=order_id, ok=bool(ok_qr))
                    except Exception as pe:
                        log_error("支付二维码生成异常", error=pe, request_id=request_id, order_id=order_id)
                else:
                    log_error("支付二维码生成函数缺失", error="process_payment_flow not loaded", request_id=request_id, order_id=order_id)
            except Exception as ie:
                log_error("准备支付二维码参数异常", error=ie, request_id=request_id, order_id=order_id)

        # 提取预约时间信息用于返回
        appt_date_raw = result.get('appointmentDate')
        appt_time = result.get('appointmentTime')
        appt_date = convert_ddmmyyyy_to_yyyy_mm_dd(appt_date_raw) if 'convert_ddmmyyyy_to_yyyy_mm_dd' in globals() else appt_date_raw

        return {
            "code": 1,
            "message": "预约成功，已进入待支付" if need_pay else ("预约成功" if is_booked else "预约已提交"),
            "data": {
                "new_status": new_status,
                "appointment_date": appt_date,
                "appointment_time": appt_time,
            }
        }

    except Exception as e:
        log_error("立即预约失败", error=e, request_id=request_id, order_id=order_id)
        return {"code": 0, "message": f"立即预约失败: {str(e)}"}


@app.post("/api/reschedule_order", summary="重新安排预约", tags=["订单"])
async def reschedule_order(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """重新安排预约 - 重置Redis数据状态为pending"""
    try:
        order_id = data.get("order_id")
        if not order_id:
            return {"code": 0, "message": "缺少订单号"}

        # 验证订单是否存在且属于当前用户
        with db_manager.get_db() as conn:
            cur = conn.cursor()

            # 检查订单是否存在且属于当前用户
            if payload.permission == 'admin' or payload.permission == 'kefu':
                # 管理员可以操作所有订单
                cur.execute("""
                    SELECT o.order_id, o.order_status, o.is_vfs_order, c.passport
                    FROM orders o
                    LEFT JOIN clients c ON o.order_id = c.order_id
                    WHERE o.order_id = %s AND o.order_status IN ('registe_error', 'schedule_error', 'account_deleted', 'urn_create_error')
                    ORDER BY c.id LIMIT 1
                """, (order_id,))
            else:
                # 非管理员只能操作自己的订单
                cur.execute("""
                    SELECT o.order_id, o.order_status, o.is_vfs_order, c.passport
                    FROM orders o
                    LEFT JOIN clients c ON o.order_id = c.order_id
                    WHERE o.order_id = %s AND o.operator = %s
                    AND o.order_status IN ('registe_error', 'schedule_error', 'account_deleted','urn_create_error')
                    ORDER BY c.id LIMIT 1
                """, (order_id, payload.user_id))

            order_info = cur.fetchone()
            if not order_info:
                return {"code": 0, "message": "订单不存在或状态不允许重新安排预约"}

            order_id, current_status, is_vfs_order, passport = order_info

            # 获取关键的旧数据用于日志记录
            cur.execute("""
                SELECT order_status, accept_vip, accept_next_day, travel_date, remark
                FROM orders WHERE order_id = %s
            """, (order_id,))
            order_basic = cur.fetchone()

            old_data = {
                "order_status": order_basic[0],
                "accept_vip": order_basic[1],
                "accept_next_day": order_basic[2],
                "travel_date": str(order_basic[3]) if order_basic[3] else "",
                "remark": order_basic[4] or "",
                "action": "reschedule_request",
            }

            # 更新数据库订单状态为wait_registe（重新开始预约流程）
            cur.execute("""
                UPDATE orders
                SET order_status = 'wait_registe', updated_at = CURRENT_TIMESTAMP
                WHERE order_id = %s
            """, (order_id,))

            # 构建新数据用于日志记录
            new_data = old_data.copy()
            new_data.update({
                "order_status": "wait_registe",
                "action": "reschedule_completed",
                "redis_status": "pending",
                "redis_create_time": int(time.time()),
                "redis_updated": True
            })

            # 记录更新日志
            cur.execute("""
                INSERT INTO order_update_log (order_id, operator, old_data, new_data)
                VALUES (%s, %s, %s, %s)
            """, (order_id, payload.user_id, json.dumps(old_data), json.dumps(new_data)))

            conn.commit()

            # 获取签证类型，检查是否为西班牙签证
            cur.execute("""
                SELECT mission_code, center_code, visa_type, visa_code
                FROM visa_types WHERE order_id = %s
            """, (order_id,))
            visa_types = cur.fetchall()

            spain_visa = None
            for visa in visa_types:
                if visa[0] and "spain" in visa[0].lower():
                    spain_visa = visa
                    break

            if not is_vfs_order:
                if not spain_visa:
                    return {"code": 0, "message": "该订单不是西班牙签证订单，无法重新安排"}

                # 获取中心代码
                center_code = spain_visa[1] if spain_visa and len(spain_visa) > 1 else ""
                if not center_code:
                    return {"code": 0, "message": "无法获取签证中心代码"}

                # 检查Redis中是否存在数据
                redis_key = f"{center_code}-{passport}"
                spain_data_str = redis_client_local.hget('spainUserDatas', redis_key)

                if spain_data_str:
                    # 更新Redis中的状态为pending
                    spain_data = json.loads(spain_data_str)
                    spain_data['status'] = 'pending'
                    spain_data['createTime'] = int(time.time())  # 更新创建时间

                    # 重新写入Redis
                    redis_client_local.hset(
                        'spainUserDatas',
                        redis_key,
                        json.dumps(spain_data)
                    )
                else:
                    # 如果Redis中没有数据，需要重新创建
                    # 获取完整订单信息来重建Redis数据
                    cur.execute("""
                        SELECT c.name, c.passport, c.dob, c.passport_expire, c.gender,
                                c.firstname_pinyin, c.surname_pinyin, c.nationality,
                                c.sign_location, c.bornplace, c.passport_date, c.marital_status,
                                c.passport_image, c.avatar_image,
                                o.travel_date, o.remark, o.accept_next_day, o.accept_vip,
                                dr.start_date, dr.end_date
                        FROM clients c
                        JOIN orders o ON c.order_id = o.order_id
                        LEFT JOIN date_ranges dr ON o.order_id = dr.order_id
                        WHERE c.order_id = %s
                        ORDER BY c.id LIMIT 1
                    """, (order_id,))

                    client_data = cur.fetchone()
                    if not client_data:
                        return {"code": 0, "message": "无法获取客户信息"}

                    # 重建Redis数据
                    spain_data = {
                        "birthday": str(client_data[2]).replace('-', '/') if client_data[2] else "",
                        "chnname": client_data[0] or "",
                        "endDate": str(client_data[19]) if client_data[19] else "",
                        "expiredDT": str(client_data[3]).replace('-', '/') if client_data[3] else "",
                        "gender": client_data[4] or "",
                        "name": client_data[5] or "",
                        "passportNO": client_data[1] or "",
                        "startDate": str(client_data[18]) if client_data[18] else "",
                        "visaTypeCode": spain_visa[3] if len(spain_visa) > 3 else "",
                        "xing": client_data[6] or "",
                        "remark": client_data[15] or "",
                        "vip": 4,
                        "status": "pending",
                        "acceptND": 1 if client_data[16] else 2,
                        "acceptVIP": 1 if client_data[17] else 2,
                        "countryCode": client_data[7] or "",
                        "from": payload.username,
                        "signLocation": client_data[8] or "",
                        "bornplace": client_data[9] or "",
                        "passportDate": str(client_data[10]).replace('-', '/') if client_data[10] else "",
                        "maritalStatus": client_data[11] or "",
                        "travelDate": str(client_data[14]).replace('-', '/') if client_data[14] else "",
                        "missionCode": "spain",
                        "centerCode": center_code,
                        "createTime": int(time.time()),
                        "order_id": order_id,
                        "passport_image": client_data[12] or "",
                        "avatar_image": client_data[13] or ""
                    }

                    # 写入Redis
                    redis_client_local.hset(
                        'spainUserDatas',
                        redis_key,
                        json.dumps(spain_data)
                    )

        return {"code": 1, "message": "预约已重新安排，将重新开始预约流程"}

    except Exception as e:
        print(f"重新安排预约失败: {str(e)}")
        return {"code": 0, "message": f"重新安排失败: {str(e)}"}


@app.post("/api/update_order_status", summary="更新订单状态", tags=["订单"])
def update_order_status(
    data: dict,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """更新订单状态"""
    order_id = data.get("order_id")
    order_status = data.get("order_status")

    if not order_id:
        raise HTTPException(status_code=400, detail="订单ID不能为空")

    if not order_status:
        raise HTTPException(status_code=400, detail="订单状态不能为空")

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 获取关键的旧数据用于日志记录
                if payload.permission == 'admin' or payload.permission == 'kefu':
                    # 管理员和客服可以更新所有订单
                    cur.execute("""
                        SELECT order_id, order_status, accept_vip, accept_next_day, travel_date, remark, operator
                        FROM orders WHERE order_id = %s
                    """, (order_id,))
                else:
                    # 普通用户只能更新自己的订单
                    cur.execute("""
                        SELECT order_id, order_status, accept_vip, accept_next_day, travel_date, remark, operator
                        FROM orders WHERE order_id = %s AND operator = %s
                    """, (order_id, payload.user_id))

                result = cur.fetchone()

                if not result:
                    raise HTTPException(status_code=404, detail="订单不存在或无权操作")

                order_basic = result
                old_status = order_basic[1]

                # 构建旧数据用于日志记录
                old_data = {
                    "order_id": order_basic[0],
                    "order_status": order_basic[1],
                    "accept_vip": order_basic[2],
                    "accept_next_day": order_basic[3],
                    "travel_date": str(order_basic[4]) if order_basic[4] else "",
                    "remark": order_basic[5] or "",
                    "operator": order_basic[6],
                    "action": "status_update_request",
                    "requested_status": order_status,
                    "operator_permission": payload.permission,
                    "timestamp": int(time.time())
                }

                # 更新订单状态
                cur.execute("""
                    UPDATE orders
                    SET order_status = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (order_status, order_id))

                # 构建新数据用于日志记录
                new_data = old_data.copy()
                new_data.update({
                    "order_status": order_status,
                    "action": "status_update_completed",
                    "update_timestamp": int(time.time()),
                    "status_changed": old_status != order_status
                })

                # 记录更新日志
                cur.execute("""
                    INSERT INTO order_update_log (order_id, operator, old_data, new_data)
                    VALUES (%s, %s, %s, %s)
                """, (order_id, payload.user_id, json.dumps(old_data), json.dumps(new_data)))

                # 检查是否有西班牙签证，如果有则同步更新Redis数据
                cur.execute("""
                    SELECT center_code
                    FROM visa_types
                    WHERE order_id = %s AND mission_code = 'spain'
                    LIMIT 1
                """, (order_id,))
                spain_visa = cur.fetchone()

                if spain_visa:
                    try:
                        # 获取客户护照号
                        cur.execute("""
                            SELECT passport
                            FROM clients
                            WHERE order_id = %s
                            LIMIT 1
                        """, (order_id,))
                        client_info = cur.fetchone()

                        if client_info:
                            center_code = spain_visa[0]
                            passport = client_info[0]
                            redis_key = f"{center_code}-{passport}"

                            # 创建Redis客户端实例
                            redis_client = RedisClient()

                            # 从Redis获取现有数据
                            spain_data_str = redis_client_local.hget('spainUserDatas', redis_key)
                            if spain_data_str:
                                spain_data = json.loads(spain_data_str)

                                # 根据订单状态更新Redis中的状态
                                if order_status == 'pause':
                                    spain_data['status'] = 'paused'
                                elif order_status == 'registe_success':
                                    spain_data['status'] = 'update_appointment'

                                # 原样回写到Redis
                                redis_client_local.hset(
                                    'spainUserDatas',
                                    redis_key,
                                    json.dumps(spain_data)
                                )

                                print(f"Redis中西班牙签证状态已同步 - 订单: {order_id}, Key: {redis_key}, 状态: {order_status} -> {spain_data['status']}")
                            else:
                                print(f"Redis中未找到西班牙签证数据 - 订单: {order_id}, Key: {redis_key}")
                        else:
                            print(f"未找到客户护照信息 - 订单: {order_id}")
                    except Exception as redis_error:
                        # Redis操作失败不影响主流程，只记录错误
                        print(f"Redis同步失败 - 订单: {order_id}, 错误: {str(redis_error)}")

                conn.commit()

                return {
                    "code": 1,
                    "message": f"订单状态已更新为{order_status}"
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新订单状态失败: {str(e)}")


@app.get("/api/check_order_status/{order_id}", summary="检查订单状态", tags=["订单"])
def check_order_status(
    order_id: str,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """检查订单的当前状态"""
    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 权限检查
                if payload.permission == 'admin' or payload.permission == 'kefu':
                    # 管理员和客服可以查看所有订单
                    cur.execute(
                        "SELECT order_id, order_status, updated_at FROM orders WHERE order_id = %s",
                        (order_id,)
                    )
                else:
                    # 普通用户只能查看自己的订单
                    cur.execute(
                        "SELECT order_id, order_status, updated_at FROM orders WHERE order_id = %s AND operator = %s",
                        (order_id, payload.user_id)
                    )

                result = cur.fetchone()

                if not result:
                    raise HTTPException(status_code=404, detail="订单不存在或无权查看")

                return {
                    "code": 1,
                    "data": {
                        "order_id": result[0],
                        "order_status": result[1],
                        "updated_at": result[2].isoformat() if result[2] else None
                    }
                }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检查订单状态失败: {str(e)}")


@app.post("/api/cancel_appointment", summary="取消预约", tags=["订单"])
async def cancel_appointment(data: dict, payload: UserPayload = Depends(verify_jwt_token)):
    """取消预约 - 将订单状态改为appointment_canceled并写入Redis队列"""
    try:
        order_id = data.get("order_id")
        appointment_no = data.get("appointment_no")
        if not order_id:
            return {"code": 0, "message": "缺少订单号"}

        # 验证订单是否存在且属于当前用户
        with db_manager.get_db() as conn:
            cur = conn.cursor()

            # 检查订单是否存在且属于当前用户
            if payload.permission == 'admin' or payload.permission == 'kefu':
                # 管理员可以操作所有订单
                cur.execute("""
                    SELECT o.order_id, o.order_status, o.is_vfs_order, c.passport, c.name
                    FROM orders o
                    LEFT JOIN clients c ON o.order_id = c.order_id
                    WHERE o.order_id = %s AND o.order_status IN ('appointment_downloaded', 'appointment_canceled', 'account_deleted')
                    ORDER BY c.id LIMIT 1
                """, (order_id,))
            else:
                # 非管理员只能操作自己的订单
                cur.execute("""
                    SELECT o.order_id, o.order_status, o.is_vfs_order, c.passport, c.name
                    FROM orders o
                    LEFT JOIN clients c ON o.order_id = c.order_id
                    WHERE o.order_id = %s AND o.operator = %s
                    AND o.order_status IN ('appointment_downloaded', 'appointment_canceled', 'account_deleted')
                    ORDER BY c.id LIMIT 1
                """, (order_id, payload.user_id))

            order_info = cur.fetchone()
            if not order_info:
                return {"code": 0, "message": "订单不存在或状态不允许取消预约"}

            order_id, current_status, is_vfs_order, passport, client_name = order_info

            # 如果已经是取消状态，则不需要重复操作
            if current_status == 'appointment_canceled':
                return {"code": 0, "message": "该订单已经取消预约"}

            if is_vfs_order == True:
                cur.execute("""
                    UPDATE orders
                    SET order_status = 'waiting_for_cancel', updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (order_id,))

            else:
                # 获取签证类型，检查是否为西班牙签证
                cur.execute("""
                    SELECT mission_code, center_code, visa_type, visa_code
                    FROM visa_types WHERE order_id = %s
                """, (order_id,))
                visa_types = cur.fetchall()

                spain_visa = None
                for visa in visa_types:
                    if visa[0] and "spain" in visa[0].lower():
                        spain_visa = visa
                        break

                if not spain_visa:
                    return {"code": 0, "message": "该订单不是西班牙签证订单，无法取消预约"}

                # 获取中心代码（领区）
                center_code = spain_visa[1] if spain_visa and len(spain_visa) > 1 else ""
                if not center_code:
                    return {"code": 0, "message": "无法获取签证中心代码"}

                # 构建Redis key：领区-护照号
                redis_key = f"{center_code}-{passport}"

                # 从Redis的spainSuccessUsers中读取数据
                success_data_str = redis_client_local.hget('spainSuccessUsers', redis_key)

                if not success_data_str:
                    return {"code": 0, "message": f"无法找到该订单的预约成功数据 (key: {redis_key})"}

                # 原样写入到spain_users_slot_cancel队列
                user_data = json.loads(success_data_str)
                user_data['appointment_no'] = appointment_no
                user_data_string = json.dumps(user_data, ensure_ascii=False)
                redis_client_local.hset(
                    'spain_users_slot_cancel',
                    redis_key,
                    user_data_string  # 直接使用原始字符串，保持数据完整性
                )

                # 记录取消信息到日志
                print(f"取消预约 - 订单号: {order_id}, Redis Key: {redis_key}, 操作员: {payload.username}, 原因: {data.get('reason', '用户主动取消')}")

                # 更新数据库订单状态为appointment_canceled
                cur.execute("""
                    UPDATE orders
                    SET order_status = 'appointment_canceled', updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (order_id,))

            # 记录取消原因到order_messages表（使用ON CONFLICT处理重复）
            cancel_message = f"用户主动取消预约。原因：{data.get('reason', '用户主动取消')}"
            cur.execute("""
                INSERT INTO order_messages (order_id, message, message_type, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT (order_id) DO UPDATE SET
                    message = EXCLUDED.message,
                    message_type = EXCLUDED.message_type,
                    status = EXCLUDED.status,
                    updated_at = CURRENT_TIMESTAMP
            """, (order_id, cancel_message, 'appointment_canceled', 'appointment_canceled'))

            conn.commit()

        return {"code": 1, "message": "预约已成功取消"}

    except Exception as e:
        print(f"取消预约失败: {str(e)}")
        return {"code": 0, "message": f"取消失败: {str(e)}"}

# ==================== 申根签证表单API ====================


@app.get("/api/schengen/application/{application_id}/steps", summary="获取申根签证申请步骤数据", tags=["申根签证"])
def get_schengen_application_steps(application_id: str):
    """获取申根签证申请的完整步骤数据（无需验证）"""
    request_id = generate_request_id()

    log_info("获取申根签证申请步骤数据",
             request_id=request_id,
             application_id=application_id)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 获取主申请信息
                cur.execute("""
                    SELECT route_id, web_ref_no, first_name, last_name, gender_id,
                           date_of_birth_year, date_of_birth_month, date_of_birth_day,
                           nationality_id, passport_number, expiry_date_year,
                           expiry_date_month, expiry_date_day, is_draft, stage
                    FROM schengen_visa_applications
                    WHERE application_id = %s
                """, (application_id,))
                main_app = cur.fetchone()

                if not main_app:
                    raise HTTPException(status_code=404, detail="申请不存在")

                # 构建结构化的步骤数据
                route_id, web_ref_no, first_name, last_name, gender_id, \
                    birth_year, birth_month, birth_day, nationality_id, passport_number, \
                    expiry_year, expiry_month, expiry_day, is_draft, stage = main_app

                # 辅助函数：解析日期
                def parse_date_to_object(year, month, day):
                    return {
                        "year": year if year else None,
                        "month": month if month else None,
                        "day": day if day else None
                    }

                # 处理空值的辅助函数
                def handle_empty_value(value, default_for_required=None):
                    """
                    处理空值：
                    - 如果值为空字符串、None或空白字符串，返回None
                    - 如果指定了required默认值且值为空，返回默认值
                    - 否则返回原值
                    """
                    if value is None or value == "" or (isinstance(value, str) and value.strip() == ""):
                        return default_for_required
                    return value

                # 获取所有步骤的数据
                steps_data = {}

                # 第一步：基本资格信息
                steps_data["step1"] = {
                    "routeId": route_id or 1,
                    "documentIdentifierId": None,
                    "flowSequence": [
                        "eligibilityCriteria",
                        "passportInformation",
                        "applicantInformation",
                        "travelInformation",
                        "accommodationInformation",
                        "vafInformation"
                    ],
                    "webRefNo": handle_empty_value(web_ref_no, f"KMSW{application_id[:10]}/1"),
                    "firstName": handle_empty_value(first_name),
                    "lastName": handle_empty_value(last_name),
                    "genderId": handle_empty_value(gender_id, "MALE"),
                    "dateOfBirth": parse_date_to_object(birth_year, birth_month, birth_day),
                    "nationalityId": handle_empty_value(nationality_id, "CHN"),
                    "passportNumber": handle_empty_value(passport_number),
                    "expiryDate": parse_date_to_object(expiry_year, expiry_month, expiry_day),
                    "isDraft": bool(is_draft),
                    "stage": handle_empty_value(stage, "eligibilityCriteria")
                }

                # 第二步：护照详细信息
                cur.execute("""
                    SELECT sur_name, surname_at_birth, given_name, date_of_birth_year,
                           date_of_birth_month, date_of_birth_day, country_of_birth,
                           place_of_birth, nationality_id, nationality_at_birth_id,
                           gender_id, marital_status_id, is_minor_applicant, id_number,
                           passport_type_id, passport_number, reenter_number_of_passport,
                           issue_date_year, issue_date_month, issue_date_day, issued_country,
                           expiry_date_year, expiry_date_month, expiry_date_day, issued_by,
                           guardian_type, guardian_surname, guardian_given_name, guardian_address, guardian_nationality
                    FROM schengen_passport_info
                    WHERE application_id = %s
                """, (application_id,))
                passport_info = cur.fetchone()

                if passport_info:
                    (sur_name, surname_at_birth, given_name, p_birth_year, p_birth_month, p_birth_day,
                     country_of_birth, place_of_birth, p_nationality_id, nationality_at_birth_id,
                     p_gender_id, marital_status_id, is_minor_applicant, id_number,
                     passport_type_id, p_passport_number, reenter_number_of_passport,
                     issue_year, issue_month, issue_day, issued_country,
                     p_expiry_year, p_expiry_month, p_expiry_day, issued_by,
                     guardian_type, guardian_surname, guardian_given_name, guardian_address, guardian_nationality) = passport_info

# {"surName":"LI","surnameAtBirth":"LI","givenName":"MU","dateOfBirth":{"year":2017,"month":7,"day":13},"countryOfBirth":"CHN","placeOfBirth":"SHANGHAI","nationalityId":"CHN","nationalityAtBirthId":"CHN","genderId":"FEMALE","maritalStatusId":"SIGL","isMinorApplicant":1,"parentalAuthority":"MOTHER","guardianSurName":"LIU","guardianFirstName":"MU","guardianAddress":"DIZHIJIANHUREN huiwehru i, hausid ","nationalityGuardian":"CHN","passportTypeId":"ORPT","passportNumber":"*********","reenternumberOfPassport":"*********","issueDate":{"year":2019,"month":7,"day":12},"issuedcountry":"CHN","expiryDate":{"year":2027,"month":7,"day":30},"issuedBy":"SHNAGAHIA","isDraft":false,"stage":"applicantInformation","applicationAlphId":"1d32d09f-6887-4d75-9570-ccba9b0f474f"}
                    # 构建基础的step2数据
                    step2_data = {
                        "surName": handle_empty_value(sur_name),
                        "surnameAtBirth": handle_empty_value(surname_at_birth),
                        "givenName": handle_empty_value(given_name),
                        "dateOfBirth": parse_date_to_object(p_birth_year, p_birth_month, p_birth_day),
                        "countryOfBirth": handle_empty_value(country_of_birth),
                        "placeOfBirth": handle_empty_value(place_of_birth),
                        "nationalityId": handle_empty_value(p_nationality_id),
                        "nationalityAtBirthId": handle_empty_value(nationality_at_birth_id),
                        "genderId": handle_empty_value(p_gender_id, "MALE"),
                        "maritalStatusId": handle_empty_value(marital_status_id),
                        "isMinorApplicant": is_minor_applicant or 2,
                        "IdNumber": handle_empty_value(id_number),
                        "passportTypeId": handle_empty_value(passport_type_id),
                        "passportNumber": handle_empty_value(p_passport_number),
                        "reenternumberOfPassport": handle_empty_value(reenter_number_of_passport),
                        "issueDate": parse_date_to_object(issue_year, issue_month, issue_day),
                        "issuedcountry": handle_empty_value(issued_country),
                        "expiryDate": parse_date_to_object(p_expiry_year, p_expiry_month, p_expiry_day),
                        "issuedBy": handle_empty_value(issued_by),
                        "isDraft": False,
                        "stage": "applicantInformation"
                    }

                    # 只有当是未成年申请者时才添加监护人信息
                    if is_minor_applicant == 1:
                        step2_data.update({
                            "parentalAuthority": handle_empty_value(guardian_type),
                            "guardianSurName": handle_empty_value(guardian_surname),
                            "guardianFirstName": handle_empty_value(guardian_given_name),
                            "guardianAddress": handle_empty_value(guardian_address),
                            "nationalityGuardian": handle_empty_value(guardian_nationality)
                        })

                    steps_data["step2"] = step2_data

                # 第三步：申请人信息
                cur.execute("""
                    SELECT application_date, applicant_country, applicant_address, occupation_id,
                           occupation_others, applicant_email, applicant_telephone_isd_code,
                           applicant_telephone_number, residence_other_nationality,
                           residence_country_permit_no, residence_country_permit_valid_until_year,
                           residence_country_permit_valid_until_month, residence_country_permit_valid_until_day,
                           employer_name, employer_address, employer_mobile, employer_city,
                           employer_home_postal_code, employer_home_country, fingerprints_collected,
                           date_of_collection_year, date_of_collection_month, date_of_collection_day,
                           previous_application_number
                    FROM schengen_applicant_info
                    WHERE application_id = %s
                """, (application_id,))
                applicant_info = cur.fetchone()

                if applicant_info:
                    (app_date, app_country, app_address, occupation_id, occupation_others,
                     app_email, app_phone_code, app_phone, residence_other_nationality,
                     residence_permit_no, residence_valid_year, residence_valid_month, residence_valid_day,
                     employer_name, employer_address, employer_mobile, employer_city,
                     employer_postal_code, employer_country, fingerprints_collected,
                     collection_year, collection_month, collection_day, previous_app_number) = applicant_info

                    step3_data = {
                        "applicationDate": app_date,
                        "applicantCountry": handle_empty_value(app_country),
                        "applicantAddress": handle_empty_value(app_address),
                        "occupationId": handle_empty_value(occupation_id),
                        "occupationOthers": handle_empty_value(occupation_others),
                        "applicantEmail": handle_empty_value(app_email),
                        "applicanttelephoneIsdCode": handle_empty_value(app_phone_code, "+86"),
                        "applicanttelePhoneNumber": handle_empty_value(app_phone),
                        "residenceOtherNationality": residence_other_nationality or 2,
                        "employerName": handle_empty_value(employer_name),
                        "employerAddress": handle_empty_value(employer_address),
                        "employerMobile": handle_empty_value(employer_mobile),
                        "employerCity": handle_empty_value(employer_city),
                        "employerHomepostalCode": handle_empty_value(employer_postal_code),
                        "employerHomecountry": handle_empty_value(employer_country),
                        "employerNameNoOcc": None,
                        "employerAddressNoOcc": None,
                        "employerMobileNoOcc": None,
                        "employerCityNoOcc": None,
                        "employerHomepostalCodeNoOcc": None,
                        "employerHomecountryNoOcc": None,
                        "fingerprintsCollected": fingerprints_collected or 2,
                        "isDraft": False,
                        "stage": "travelInformation"
                    }
                    if fingerprints_collected == 1:
                        step3_data.update({
                            "dateOfCollection": parse_date_to_object(collection_year, collection_month, collection_day),
                            "previousApplicationNumber": handle_empty_value(previous_app_number)
                        })
                    if residence_other_nationality == 1:
                        step3_data.update({
                            "residenceCountryPermitNo": handle_empty_value(residence_permit_no),
                            "residenceCountryPermitValidUntil": parse_date_to_object(residence_valid_year, residence_valid_month, residence_valid_day)
                        })

                    steps_data["step3"] = step3_data

                # 第四步：旅行计划信息
                cur.execute("""
                    SELECT purpose_of_travel, purpose_of_travel_others, purpose_of_travel_add_info,
                           number_of_entries, is_schengen_visa_issued, valid_from_year,
                           valid_from_month, valid_from_day, valid_till_year, valid_till_month,
                           valid_till_day, is_adequate_medical_insurance
                    FROM schengen_travel_info
                    WHERE application_id = %s
                """, (application_id,))
                travel_info = cur.fetchone()

                if travel_info:
                    (purpose_of_travel, purpose_of_travel_others, purpose_of_travel_add_info,
                     number_of_entries, is_schengen_visa_issued, valid_from_year,
                     valid_from_month, valid_from_day, valid_till_year, valid_till_month,
                     valid_till_day, is_adequate_medical_insurance) = travel_info
                    step4_data = {
                        "purposeOfTravel": handle_empty_value(purpose_of_travel),
                        "purposeOfTravelOthers": handle_empty_value(purpose_of_travel_others),
                        "purposeOfTraveladdInfo": handle_empty_value(purpose_of_travel_add_info),
                        "numberOfEntries": handle_empty_value(number_of_entries),
                        "IsSchengenVisaIssued": is_schengen_visa_issued or 2,
                        "isAdequateMedicalInsurance": is_adequate_medical_insurance or 2,
                        "isDraft": False,
                        "stage": "accommodationInformation"
                    }
                    if is_schengen_visa_issued == 1:
                        step4_data.update({
                            "valid_from": parse_date_to_object(valid_from_year, valid_from_month, valid_from_day),
                            "valid_till": parse_date_to_object(valid_till_year, valid_till_month, valid_till_day)
                        })

                    steps_data["step4"] = step4_data

                # 第五步：住宿安排信息
                cur.execute("""
                    SELECT inviting_party_id, name_of_organisation, address_of_organisation,
                           name_of_enterprise, address_of_enterprise, postal_code_of_enterprise,
                           email_enterprise, street_enterprise, city_enterprise, country_enterprise,
                           enterprise_telephone_isd_code, enterprise_telephone_number,
                           sur_name_of_contact_inviting_person, first_name_of_contact_inviting_person,
                           address_of_inviting_person, postal_code_of_inviting_person,
                           email_inviting_person, street_inviting_person, city_inviting_person,
                           country_inviting_person, name_of_inviting_hotel, address_of_inviting_hotel,
                           postal_code_of_inviting_hotel, email_inviting_hotel, street_inviting_hotel,
                           city_inviting_hotel, country_inviting_hotel, inviting_hotel_telephone_isd_code,
                           inviting_hotel_telephone_number,phone_code_of_inviting_person,phone_of_inviting_person
                    FROM schengen_accommodation_info
                    WHERE application_id = %s
                """, (application_id,))
                accommodation_info = cur.fetchone()

                if accommodation_info:
                    (inviting_party_id, name_of_organisation, address_of_organisation,
                     name_of_enterprise, address_of_enterprise, postal_code_of_enterprise,
                     email_enterprise, street_enterprise, city_enterprise, country_enterprise,
                     enterprise_telephone_isd_code, enterprise_telephone_number,
                     sur_name_of_contact_inviting_person, first_name_of_contact_inviting_person,
                     address_of_inviting_person, postal_code_of_inviting_person,
                     email_inviting_person, street_inviting_person, city_inviting_person,
                     country_inviting_person, name_of_inviting_hotel, address_of_inviting_hotel,
                     postal_code_of_inviting_hotel, email_inviting_hotel, street_inviting_hotel,
                     city_inviting_hotel, country_inviting_hotel, inviting_hotel_telephone_isd_code,
                     inviting_hotel_telephone_number, phone_code_of_inviting_person, phone_of_inviting_person) = accommodation_info

                    steps_data["step5"] = {
                        "invitingPartyId": inviting_party_id,
                        "nameOforganisation": handle_empty_value(name_of_organisation),
                        "addressOforganisation": handle_empty_value(address_of_organisation),
                        "nameOfenterprise": handle_empty_value(name_of_enterprise),
                        "addressOfenterprise": handle_empty_value(address_of_enterprise),
                        "postalcodeOfenterprise": handle_empty_value(postal_code_of_enterprise),
                        "postofficeBoxenterprise": None,
                        "postofficeBoxnumberenterprise": None,
                        "emailenterprise": handle_empty_value(email_enterprise),
                        "streetenterprise": handle_empty_value(street_enterprise),
                        "cityenterprise": handle_empty_value(city_enterprise),
                        "countryenterprise": handle_empty_value(country_enterprise),
                        "enterprisetelephoneIsdCode": handle_empty_value(enterprise_telephone_isd_code),
                        "enterprisetelePhoneNumber": handle_empty_value(enterprise_telephone_number),
                        "faxNumberenterprise": None,
                        "surNameOfcontactpersonenterprise": None,
                        "firstNameOfcontactpersonenterprise": None,
                        "emailOfcontactpersonenterprise": None,
                        "surNameOfcontactinvitingperson": handle_empty_value(sur_name_of_contact_inviting_person),
                        "firstNameOfcontactinvitingperson": handle_empty_value(first_name_of_contact_inviting_person),
                        "addressOfinvitingperson": handle_empty_value(address_of_inviting_person),
                        "postalcodeOfinvitingperson": handle_empty_value(postal_code_of_inviting_person),
                        "postofficeBoxinvitingperson": None,
                        "postofficeBoxnumberinvitingperson": None,
                        "emailinvitingperson": handle_empty_value(email_inviting_person),
                        "streetinvitingperson": handle_empty_value(street_inviting_person),
                        "cityinvitingperson": handle_empty_value(city_inviting_person),
                        "countryinvitingperson": handle_empty_value(country_inviting_person),
                        "invitingpersontelephoneIsdCode": handle_empty_value(phone_code_of_inviting_person),
                        "invitingpersontelePhoneNumber": handle_empty_value(phone_of_inviting_person),
                        "emailaddressOfcontactinvitingperson": None,
                        "nameOfinvitinghotel": handle_empty_value(name_of_inviting_hotel),
                        "addressOfinvitinghotel": handle_empty_value(address_of_inviting_hotel),
                        "postalcodeOfinvitinghotel": handle_empty_value(postal_code_of_inviting_hotel),
                        "postofficeBoxinvitinghotel": None,
                        "postofficeBoxnumberinvitinghotel": None,
                        "emailinvitinghotel": handle_empty_value(email_inviting_hotel),
                        "streetinvitinghotel": handle_empty_value(street_inviting_hotel),
                        "cityinvitinghotel": handle_empty_value(city_inviting_hotel),
                        "countryinvitinghotel": handle_empty_value(country_inviting_hotel),
                        "invitinghoteltelephoneIsdCode": handle_empty_value(inviting_hotel_telephone_isd_code),
                        "invotinghoteltelePhoneNumber": handle_empty_value(inviting_hotel_telephone_number),
                        "faxNumberinvitinghotel": None,
                        "surNameOfcontactinvitinghotel": None,
                        "firstNameOfcontactinvitinghotel": None,
                        "emailOfcontactinvtinghotel": None,
                        "isDraft": False,
                        "stage": "vafInformation"
                    }

                # 第六步：附加信息及确认
                cur.execute("""
                    SELECT final_destination, arrival_date_year, arrival_date_month, arrival_date_day,
                           departure_date_year, departure_date_month, departure_date_day, duration_of_stay,
                           cost_of_travelling_covered_by, cost_of_travelling_covered_by_others,
                           means_of_support_id, means_of_support_others, is_citizen_id,
                           eu_surname, eu_first_name, eu_nationality_id, eu_date_of_birth_year,
                           eu_date_of_birth_month, eu_date_of_birth_day, eu_passport_number,
                           eu_relationship_id, schengen_state_first_entry, country_of_destination,
                           inviting_person_covered_costs
                    FROM schengen_additional_info
                    WHERE application_id = %s
                """, (application_id,))
                additional_info = cur.fetchone()

                if additional_info:
                    (final_destination, arrival_year, arrival_month, arrival_day,
                     departure_year, departure_month, departure_day, duration_of_stay,
                     cost_covered_by, cost_covered_by_others, means_of_support_id, means_of_support_others,
                     is_citizen_id, eu_surname, eu_first_name, eu_nationality_id,
                     eu_birth_year, eu_birth_month, eu_birth_day, eu_passport_number,
                     eu_relationship_id, schengen_state_first_entry, country_of_destination,
                     inviting_person_covered_costs) = additional_info

                    # 处理数组字段
                    def parse_array_field(field_value):
                        if isinstance(field_value, str):
                            try:
                                import json
                                return json.loads(field_value)
                            except:
                                return field_value.split(',') if field_value else []
                        elif isinstance(field_value, list):
                            return field_value
                        else:
                            return []

                    steps_data["step6"] = {
                        "finalDestination": final_destination or 2,
                        "IssuedBy": None,
                        "arrivalDate": parse_date_to_object(arrival_year, arrival_month, arrival_day),
                        "departureDate": parse_date_to_object(departure_year, departure_month, departure_day),
                        "durationOfStay": handle_empty_value(duration_of_stay),
                        "costOfTravellingCoveredBy": parse_array_field(cost_covered_by),
                        "costOfTravellingCoveredByOthers": handle_empty_value(cost_covered_by_others),
                        "meansOfSupportId": parse_array_field(means_of_support_id),
                        "meansOfSupportOthers": handle_empty_value(means_of_support_others),
                        "isCitizenId": is_citizen_id or 2,
                        "EuSurname": handle_empty_value(eu_surname),
                        "EuFirstName": handle_empty_value(eu_first_name),
                        "EuNationalityId": handle_empty_value(eu_nationality_id),
                        "EuDateOfBirth": parse_date_to_object(eu_birth_year, eu_birth_month, eu_birth_day),
                        "EuPassportNumber": handle_empty_value(eu_passport_number),
                        "EuRelationshipId": handle_empty_value(eu_relationship_id),
                        "schengenStateFirstEntry": handle_empty_value(schengen_state_first_entry),
                        "countryOfDestination": parse_array_field(country_of_destination),
                        "invitingPersonCoveredCosts": inviting_person_covered_costs or 2,
                        "isDraft": False,
                        "stage": "ReceivedAtAC"
                    }

                log_info("获取申根签证申请步骤数据成功",
                         request_id=request_id,
                         application_id=application_id)

                # 返回所有步骤数据
                return steps_data

    except HTTPException:
        raise
    except Exception as e:
        log_error("获取申根签证申请步骤数据失败",
                  error=e,
                  request_id=request_id,
                  application_id=application_id)
        raise HTTPException(status_code=500, detail=f"获取申请数据失败: {str(e)}")


@app.get("/api/schengen-forms", summary="获取申根签证表单列表", tags=["申根签证"])
def get_schengen_forms(
    page: int = 1,
    size: int = 10,
    search: str = "",
    status: str = "",
    payload: UserPayload = Depends(verify_jwt_token)
):
    """获取申根签证表单列表"""
    request_id = generate_request_id()

    log_info("获取申根签证表单列表",
             request_id=request_id,
             user_id=payload.user_id,
             permission=payload.permission,
             page=page,
             size=size,
             search=search,
             status=status)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 构建查询条件
                where_conditions = []
                params = []

                # 根据用户权限决定是否过滤用户
                if payload.permission not in ['kefu', 'admin']:
                    where_conditions.append("user_id = %s")
                    params.append(payload.user_id)
                    log_info("普通用户查询", request_id=request_id, scope="仅本人创建的表单")
                else:
                    log_info("管理员/客服查询", request_id=request_id, scope="所有用户的表单")

                if search:
                    where_conditions.append("(first_name ILIKE %s OR last_name ILIKE %s OR application_id ILIKE %s)")
                    search_param = f"%{search}%"
                    params.extend([search_param, search_param, search_param])

                if status:
                    where_conditions.append("status = %s")
                    params.append(status)

                # 构建WHERE子句
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)
                else:
                    where_clause = ""

                # 获取总数
                count_query = f"""
                    SELECT COUNT(*)
                    FROM schengen_visa_applications
                    {where_clause}
                """
                cur.execute(count_query, params)
                total = cur.fetchone()[0]

                # 获取分页数据
                offset = (page - 1) * size
                data_query = f"""
                    SELECT application_id, first_name, last_name, nationality_id,
                           passport_number, status, created_at, updated_at
                    FROM schengen_visa_applications
                    {where_clause}
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                """
                params.extend([size, offset])
                cur.execute(data_query, params)

                forms = []
                for row in cur.fetchall():
                    applicant_name = f"{row[1]} {row[2]}".strip()
                    forms.append({
                        "id": row[0],  # application_id作为前端的id
                        "application_id": row[0],
                        "applicant_name": applicant_name,  # 申请人姓名
                        "passport_number": row[4] or "",  # 护照号
                        "nationality": row[3] or "",  # 国籍
                        "status": row[5],
                        "created_at": row[6].strftime('%Y-%m-%d %H:%M:%S') if row[6] else None,
                        "updated_at": row[7].strftime('%Y-%m-%d %H:%M:%S') if row[7] else None
                    })

                log_info("获取申根签证表单列表成功",
                         request_id=request_id,
                         total=total,
                         returned=len(forms))

                return {
                    "code": 1,
                    "message": "success",
                    "data": {
                        "items": forms,
                        "total": total,
                        "page": page,
                        "size": size
                    }
                }

    except Exception as e:
        log_error("获取申根签证表单列表失败",
                  error=e,
                  request_id=request_id,
                  user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=f"获取表单列表失败: {str(e)}")


@app.post("/api/schengen-forms", summary="保存申根签证表单", tags=["申根签证"])
def save_schengen_form(
    request_data: dict,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """保存申根签证表单数据（兼容前端格式）"""
    request_id = generate_request_id()

    log_info("保存申根签证表单",
             request_id=request_id,
             user_id=payload.user_id,
             data_keys=list(request_data.keys()))

    try:
        application_id = generate_application_id()

        # 解析日期
        def parse_date(date_str):
            if not date_str:
                return None, None, None
            try:
                # 假设日期格式为 DD/MM/YYYY
                parts = date_str.split('/')
                if len(parts) == 3:
                    return int(parts[2]), int(parts[1]), int(parts[0])  # year, month, day
            except:
                pass
            return None, None, None

        # 从前端数据中提取字段
        surname = request_data.get('surname', '')
        given_name = request_data.get('given_name', '')
        gender = request_data.get('gender', 'M')
        birth_date = request_data.get('birth_date', '')
        nationality = request_data.get('nationality', '')
        passport_number = request_data.get('passport_number', '')
        passport_expire_date = request_data.get('passport_expire_date', '')
        status = request_data.get('status', 'draft')

        birth_year, birth_month, birth_day = parse_date(birth_date)
        expire_year, expire_month, expire_day = parse_date(passport_expire_date)

        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 保存到主表
                cur.execute("""
                    INSERT INTO schengen_visa_applications (
                        application_id, user_id, route_id, web_ref_no, first_name, last_name,
                        gender_id, date_of_birth_year, date_of_birth_month, date_of_birth_day,
                        nationality_id, passport_number, expiry_date_year, expiry_date_month,
                        expiry_date_day, is_draft, stage, status
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, (
                    application_id, payload.user_id, 1, None,
                    given_name, surname,
                    "MALE" if gender == "M" else "FEMALE",
                    birth_year, birth_month, birth_day,
                    nationality, passport_number,
                    expire_year, expire_month, expire_day,
                    status == 'draft', 'eligibilityCriteria', status
                ))

                # 如果有护照详细信息，保存到护照信息表
                if request_data.get('passport_surname') or request_data.get('passport_given_name'):
                    passport_birth_year, passport_birth_month, passport_birth_day = parse_date(request_data.get('passport_birth_date', ''))
                    passport_issue_year, passport_issue_month, passport_issue_day = parse_date(request_data.get('passport_issue_date_detail', ''))
                    passport_expire_year, passport_expire_month, passport_expire_day = parse_date(request_data.get('passport_expire_date_detail', ''))

                    # 首先检查表是否有监护人字段，如果没有则添加
                    try:
                        cur.execute("""
                            ALTER TABLE schengen_passport_info
                            ADD COLUMN IF NOT EXISTS guardian_type VARCHAR(50),
                            ADD COLUMN IF NOT EXISTS guardian_surname VARCHAR(100),
                            ADD COLUMN IF NOT EXISTS guardian_given_name VARCHAR(100),
                            ADD COLUMN IF NOT EXISTS guardian_address TEXT,
                            ADD COLUMN IF NOT EXISTS guardian_nationality VARCHAR(10)
                        """)
                    except Exception:
                        # 如果ALTER失败，可能是字段已存在，继续执行
                        pass

                    cur.execute("""
                        INSERT INTO schengen_passport_info (
                            application_id, sur_name, surname_at_birth, given_name,
                            date_of_birth_year, date_of_birth_month, date_of_birth_day,
                            country_of_birth, place_of_birth, nationality_id, nationality_at_birth_id,
                            gender_id, marital_status_id, is_minor_applicant, id_number,
                            passport_type_id, passport_number, reenter_number_of_passport,
                            issue_date_year, issue_date_month, issue_date_day, issued_country,
                            expiry_date_year, expiry_date_month, expiry_date_day, issued_by,
                            guardian_type, guardian_surname, guardian_given_name, guardian_address, guardian_nationality
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s
                        )
                    """, (
                        application_id,
                        request_data.get('passport_surname', ''),
                        request_data.get('birth_surname', ''),
                        request_data.get('passport_given_name', ''),
                        passport_birth_year, passport_birth_month, passport_birth_day,
                        request_data.get('birth_country', ''),
                        request_data.get('birth_place', ''),
                        request_data.get('current_nationality', ''),
                        request_data.get('birth_nationality', ''),
                        "MALE" if request_data.get('passport_gender') == "M" else "FEMALE",
                        request_data.get('passport_marital_status', ''),
                        1 if request_data.get('is_minor') == 'yes' else 2,
                        request_data.get('passport_number_detail', ''),
                        request_data.get('passport_type', ''),
                        request_data.get('passport_number_detail', ''),
                        request_data.get('passport_number_detail', ''),
                        passport_issue_year, passport_issue_month, passport_issue_day,
                        request_data.get('issuing_country', ''),
                        passport_expire_year, passport_expire_month, passport_expire_day,
                        request_data.get('issuing_authority', ''),
                        # 监护人信息
                        request_data.get('guardian_type', '') if request_data.get('is_minor') == 'yes' else None,
                        request_data.get('guardian_surname', '') if request_data.get('is_minor') == 'yes' else None,
                        request_data.get('guardian_given_name', '') if request_data.get('is_minor') == 'yes' else None,
                        request_data.get('guardian_address', '') if request_data.get('is_minor') == 'yes' else None,
                        request_data.get('guardian_nationality', '') if request_data.get('is_minor') == 'yes' else None
                    ))

                # 保存申请人信息（第三步）
                if request_data.get('applicant_country') or request_data.get('applicant_address'):
                    residence_permit_year, residence_permit_month, residence_permit_day = parse_date(request_data.get('residence_permit_valid_until', ''))
                    collection_year, collection_month, collection_day = parse_date(request_data.get('date_of_collection', ''))

                    cur.execute("""
                        INSERT INTO schengen_applicant_info (
                            application_id, applicant_country, applicant_address, occupation_id,
                            occupation_others, applicant_email, applicant_telephone_isd_code,
                            applicant_telephone_number, residence_other_nationality,
                            residence_country_permit_no, residence_country_permit_valid_until_year,
                            residence_country_permit_valid_until_month, residence_country_permit_valid_until_day,
                            employer_name, employer_address, employer_mobile, employer_city,
                            employer_home_postal_code, employer_home_country, fingerprints_collected,
                            date_of_collection_year, date_of_collection_month, date_of_collection_day,
                            previous_application_number
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                    """, (
                        application_id,
                        request_data.get('applicant_country', ''),
                        request_data.get('applicant_address', ''),
                        request_data.get('occupation_id', ''),
                        request_data.get('occupation_others', ''),
                        request_data.get('applicant_email', ''),
                        '+86',
                        request_data.get('applicant_phone', ''),
                        request_data.get('residence_other_nationality', 2),
                        request_data.get('residence_permit_no', ''),
                        residence_permit_year, residence_permit_month, residence_permit_day,
                        request_data.get('employer_name', ''),
                        request_data.get('employer_address', ''),
                        request_data.get('employer_phone', ''),
                        request_data.get('employer_city', ''),
                        request_data.get('employer_postal_code', ''),
                        request_data.get('employer_country', ''),
                        request_data.get('fingerprints_collected', 2),
                        collection_year, collection_month, collection_day,
                        request_data.get('previous_application_number', '')
                    ))

                # 保存旅行信息（第四步）
                if request_data.get('purpose_of_travel'):
                    valid_from_year, valid_from_month, valid_from_day = parse_date(request_data.get('valid_from', ''))
                    valid_till_year, valid_till_month, valid_till_day = parse_date(request_data.get('valid_till', ''))

                    cur.execute("""
                        INSERT INTO schengen_travel_info (
                            application_id, purpose_of_travel, purpose_of_travel_others,
                            purpose_of_travel_add_info, number_of_entries, is_schengen_visa_issued,
                            valid_from_year, valid_from_month, valid_from_day,
                            valid_till_year, valid_till_month, valid_till_day,
                            is_adequate_medical_insurance
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                    """, (
                        application_id,
                        request_data.get('purpose_of_travel', ''),
                        request_data.get('purpose_of_travel_others', ''),
                        request_data.get('purpose_of_travel_add_info', ''),
                        request_data.get('number_of_entries', ''),
                        request_data.get('is_schengen_visa_issued', 2),
                        valid_from_year, valid_from_month, valid_from_day,
                        valid_till_year, valid_till_month, valid_till_day,
                        request_data.get('is_adequate_medical_insurance', 2)
                    ))

                # 保存住宿信息（第五步）
                inviting_party_type = request_data.get('inviting_party_type', '')
                # 根据正确的映射关系设置inviting_party_id
                inviting_party_id = 3  # 默认值：Hotel
                if inviting_party_type == 'InvitingPerson':
                    inviting_party_id = 1
                elif inviting_party_type == 'Invitingenterprise':
                    inviting_party_id = 2
                elif inviting_party_type == 'Hotel':
                    inviting_party_id = 3
                elif inviting_party_type == 'Temporary accommodation':
                    inviting_party_id = 4
                elif inviting_party_type == 'InvitingOrganisation':
                    inviting_party_id = 5
                elif inviting_party_type == 'RegulationsstayinSwitzerland':
                    inviting_party_id = 6

                # 添加调试日志
                log_info("保存住宿信息映射",
                         request_id=request_id,
                         inviting_party_type=inviting_party_type,
                         inviting_party_id=inviting_party_id)

                # 根据邀请方类型获取相应的数据
                def get_accommodation_data():
                    if inviting_party_type == 'Hotel':
                        return {
                            'name': request_data.get('hotel_name', ''),
                            'address': request_data.get('hotel_address', ''),
                            'postal_code': request_data.get('hotel_postal_code', ''),
                            'email': request_data.get('hotel_email', ''),
                            'street': request_data.get('hotel_street', ''),
                            'city': request_data.get('hotel_city', ''),
                            'country': request_data.get('hotel_country', ''),
                            'phone_code': request_data.get('hotel_phone_code', '+86'),
                            'phone': request_data.get('hotel_phone', '')
                        }
                    elif inviting_party_type == 'Temporary accommodation':
                        return {
                            'name': '',
                            'address': request_data.get('temp_accommodation_address', ''),
                            'postal_code': request_data.get('temp_accommodation_postal_code', ''),
                            'email': request_data.get('temp_accommodation_email', ''),
                            'street': request_data.get('temp_accommodation_street', ''),
                            'city': request_data.get('temp_accommodation_city', ''),
                            'country': request_data.get('temp_accommodation_country', ''),
                            'phone_code': request_data.get('temp_accommodation_phone_code', '+86'),
                            'phone': request_data.get('temp_accommodation_phone', '')
                        }
                    elif inviting_party_type == 'RegulationsstayinSwitzerland':
                        return {
                            'name': '',
                            'address': request_data.get('regulations_address', ''),
                            'postal_code': request_data.get('regulations_postal_code', ''),
                            'email': request_data.get('regulations_email', ''),
                            'street': request_data.get('regulations_street', ''),
                            'city': request_data.get('regulations_city', ''),
                            'country': request_data.get('regulations_country', ''),
                            'phone_code': request_data.get('regulations_phone_code', '+86'),
                            'phone': request_data.get('regulations_phone', '')
                        }
                    else:
                        return {
                            'name': '',
                            'address': '',
                            'postal_code': '',
                            'email': '',
                            'street': '',
                            'city': '',
                            'country': '',
                            'phone_code': '+86',
                            'phone': ''
                        }

                accommodation_data = get_accommodation_data()

                cur.execute("""
                    INSERT INTO schengen_accommodation_info (
                        application_id, inviting_party_id,
                        name_of_organisation, address_of_organisation,
                        name_of_enterprise, address_of_enterprise, postal_code_of_enterprise,
                        email_enterprise, street_enterprise, city_enterprise, country_enterprise,
                        enterprise_telephone_isd_code, enterprise_telephone_number,
                        sur_name_of_contact_inviting_person, first_name_of_contact_inviting_person,
                        address_of_inviting_person, postal_code_of_inviting_person,phone_code_of_inviting_person,
                        phone_of_inviting_person,
                        email_inviting_person, street_inviting_person, city_inviting_person,
                        country_inviting_person, name_of_inviting_hotel, address_of_inviting_hotel,
                        postal_code_of_inviting_hotel, email_inviting_hotel, street_inviting_hotel,
                        city_inviting_hotel, country_inviting_hotel, inviting_hotel_telephone_isd_code,
                        inviting_hotel_telephone_number,
                        organisation_telephone_isd_code, organisation_telephone_number,
                        temp_accommodation_telephone_isd_code, temp_accommodation_telephone_number,
                        regulations_telephone_isd_code, regulations_telephone_number
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, (
                    application_id, inviting_party_id,
                    request_data.get('organisation_name', ''),
                    request_data.get('organisation_address', ''),
                    request_data.get('enterprise_name', ''),
                    request_data.get('enterprise_address', ''),
                    request_data.get('enterprise_postal_code', ''),
                    request_data.get('enterprise_email', ''),
                    request_data.get('enterprise_street', ''),
                    request_data.get('enterprise_city', ''),
                    request_data.get('enterprise_country', ''),
                    request_data.get('enterprise_phone_code', '+86'),
                    request_data.get('enterprise_phone', ''),
                    request_data.get('inviting_person_surname', ''),
                    request_data.get('inviting_person_given_name', ''),
                    request_data.get('inviting_person_address', ''),
                    request_data.get('inviting_person_postal_code', ''),
                    request_data.get('inviting_person_phone_code', ''),
                    request_data.get('inviting_person_phone', ''),
                    request_data.get('inviting_person_email', ''),
                    request_data.get('inviting_person_street', ''),
                    request_data.get('inviting_person_city', ''),
                    request_data.get('inviting_person_country', ''),
                    accommodation_data['name'],
                    accommodation_data['address'],
                    accommodation_data['postal_code'],
                    accommodation_data['email'],
                    accommodation_data['street'],
                    accommodation_data['city'],
                    accommodation_data['country'],
                    accommodation_data['phone_code'],
                    accommodation_data['phone'],
                    # 新增的电话区号字段
                    request_data.get('organisation_phone_code', '+86'),
                    request_data.get('organisation_phone', ''),
                    request_data.get('temp_accommodation_phone_code', '+86'),
                    request_data.get('temp_accommodation_phone', ''),
                    request_data.get('regulations_phone_code', '+86'),
                    request_data.get('regulations_phone', '')
                ))

                # 保存附加信息（第六步）
                # 检查是否有第六步的有效数据（不仅仅是空字符串）
                has_valid_step6_data = (
                    request_data.get('arrival_date', '').strip() or
                    request_data.get('departure_date', '').strip() or
                    request_data.get('duration_of_stay', '').strip() or
                    request_data.get('cost_covered_by') or
                    request_data.get('means_of_support') or
                    request_data.get('first_entry_schengen', '').strip() or
                    request_data.get('destination_countries') or
                    request_data.get('costsCoveredBy', '').strip()
                )

                if has_valid_step6_data:
                    arrival_year, arrival_month, arrival_day = parse_date(request_data.get('arrival_date', ''))
                    departure_year, departure_month, departure_day = parse_date(request_data.get('departure_date', ''))
                    eu_birth_year, eu_birth_month, eu_birth_day = parse_date(request_data.get('eu_citizen_birth_date', ''))

                    # 处理数组字段
                    cost_covered_by = request_data.get('cost_covered_by', [])
                    means_of_support = request_data.get('means_of_support', [])
                    destination_countries = request_data.get('destination_countries', [])

                    cur.execute("""
                        INSERT INTO schengen_additional_info (
                            application_id, final_destination, arrival_date_year, arrival_date_month,
                            arrival_date_day, departure_date_year, departure_date_month, departure_date_day,
                            duration_of_stay, cost_of_travelling_covered_by, cost_of_travelling_covered_by_others,
                            means_of_support_id, means_of_support_others, is_citizen_id,
                            eu_surname, eu_first_name, eu_nationality_id, eu_date_of_birth_year,
                            eu_date_of_birth_month, eu_date_of_birth_day, eu_passport_number,
                            eu_relationship_id, schengen_state_first_entry, country_of_destination,
                            inviting_person_covered_costs, costs_covered_by
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                    """, (
                        application_id,
                        request_data.get('is_transit_visa', 2),
                        arrival_year, arrival_month, arrival_day,
                        departure_year, departure_month, departure_day,
                        request_data.get('duration_of_stay', ''),
                        json.dumps(cost_covered_by),
                        request_data.get('cost_covered_by_others', ''),
                        json.dumps(means_of_support),
                        request_data.get('means_of_support_others', ''),
                        request_data.get('is_eu_citizen_family', 2),
                        request_data.get('eu_citizen_surname', ''),
                        request_data.get('eu_citizen_given_name', ''),
                        request_data.get('eu_citizen_nationality', ''),
                        eu_birth_year, eu_birth_month, eu_birth_day,
                        request_data.get('eu_citizen_passport_number', ''),
                        request_data.get('eu_citizen_relationship', ''),
                        request_data.get('first_entry_schengen', ''),
                        json.dumps(destination_countries),
                        request_data.get('inviting_person_covered_costs', 2),
                        request_data.get('costsCoveredBy', '')
                    ))

                log_info("保存申根签证表单成功",
                         request_id=request_id,
                         application_id=application_id)

                return {
                    "code": 1,
                    "message": "success",
                    "data": {"application_id": application_id, "id": application_id}
                }

    except Exception as e:
        log_error("保存申根签证表单失败",
                  error=e,
                  request_id=request_id,
                  user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=f"保存表单失败: {str(e)}")


@app.post("/api/schengen/application", summary="保存申根签证申请（新格式）", tags=["申根签证"])
def save_schengen_application(
    request_data: SchengenVisaApplicationRequest,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """保存申根签证申请数据（新格式）"""
    request_id = generate_request_id()

    log_info("保存申根签证申请",
             request_id=request_id,
             user_id=payload.user_id)

    try:
        application_id = generate_application_id()
        user_id = request_data.userId or payload.user_id

        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 保存第一步数据（主表）
                if request_data.step1:
                    step1 = request_data.step1
                    cur.execute("""
                        INSERT INTO schengen_visa_applications (
                            application_id, user_id, route_id, web_ref_no, first_name, last_name,
                            gender_id, date_of_birth_year, date_of_birth_month, date_of_birth_day,
                            nationality_id, passport_number, expiry_date_year, expiry_date_month,
                            expiry_date_day, is_draft, stage, status
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                    """, (
                        application_id, user_id, step1.routeId, step1.webRefNo,
                        step1.firstName, step1.lastName, step1.genderId,
                        step1.dateOfBirth.year, step1.dateOfBirth.month, step1.dateOfBirth.day,
                        step1.nationalityId, step1.passportNumber,
                        step1.expiryDate.year, step1.expiryDate.month, step1.expiryDate.day,
                        step1.isDraft, step1.stage, 'draft'
                    ))

                log_info("保存申根签证申请成功",
                         request_id=request_id,
                         application_id=application_id)

                return {
                    "code": 1,
                    "message": "success",
                    "data": {"application_id": application_id}
                }

    except Exception as e:
        log_error("保存申根签证申请失败",
                  error=e,
                  request_id=request_id,
                  user_id=payload.user_id)
        raise HTTPException(status_code=500, detail=f"保存申请失败: {str(e)}")


@app.get("/api/schengen-forms/by-passport/{passport_number}", summary="根据护照号查询申根申请表列表", tags=["申根签证"])
def get_schengen_forms_by_passport(passport_number: str):
    """根据护照号查询申根申请表列表"""
    try:
        log_info("根据护照号查询申根申请表",
                 passport_number=passport_number)

        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 查询该护照号对应的所有申根申请表
                cur.execute("""
                    SELECT
                        sva.application_id,
                        sva.passport_number,
                        u.name as creator_name,
                        sva.created_at,
                        sva.status,
                        sva.is_draft,
                        sva.first_name,
                        sva.last_name
                    FROM schengen_visa_applications sva
                    LEFT JOIN users u ON sva.user_id = u.id
                    WHERE sva.passport_number = %s
                    ORDER BY sva.created_at DESC
                """, (passport_number,))

                results = cur.fetchall()

                if not results:
                    return {
                        "code": 1,
                        "message": "未找到该护照号的申请记录",
                        "data": []
                    }

                # 格式化返回数据
                applications = []
                for row in results:
                    application_id, passport_num, creator_name, created_at, status, is_draft, first_name, last_name = row

                    # 组合申请人姓名
                    applicant_name = f"{last_name or ''} {first_name or ''}".strip() or "未填写"

                    applications.append({
                        "application_id": application_id,
                        "passport_number": passport_num,
                        "creator_name": creator_name or "未知用户",
                        "applicant_name": applicant_name,
                        "created_at": created_at.strftime("%Y-%m-%d %H:%M:%S") if created_at else "",
                        "status": status or "draft",
                        "is_draft": bool(is_draft)
                    })

                log_info("护照号查询成功",
                         passport_number=passport_number,
                         found_count=len(applications))

                return {
                    "code": 1,
                    "message": "查询成功",
                    "data": applications
                }

    except Exception as e:
        log_error("根据护照号查询申根申请表失败",
                  passport_number=passport_number,
                  error=str(e))
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


@app.get("/api/schengen-forms/{form_id}", summary="获取申根签证表单详情", tags=["申根签证"])
def get_schengen_form(
    form_id: str,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """获取申根签证表单详情"""
    request_id = generate_request_id()

    log_info("获取申根签证表单详情",
             request_id=request_id,
             form_id=form_id,
             user_id=payload.user_id,
             permission=payload.permission)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 根据权限构建查询条件
                if payload.permission not in ['kefu', 'admin']:
                    # 普通用户只能查看自己的表单
                    cur.execute("""
                        SELECT application_id, user_id, first_name, last_name, gender_id,
                               date_of_birth_year, date_of_birth_month, date_of_birth_day,
                               nationality_id, passport_number, expiry_date_year, expiry_date_month, expiry_date_day,
                               status, created_at, updated_at
                        FROM schengen_visa_applications
                        WHERE application_id = %s AND user_id = %s
                    """, (form_id, payload.user_id))
                else:
                    # 管理员/客服可以查看所有表单
                    cur.execute("""
                        SELECT application_id, user_id, first_name, last_name, gender_id,
                               date_of_birth_year, date_of_birth_month, date_of_birth_day,
                               nationality_id, passport_number, expiry_date_year, expiry_date_month, expiry_date_day,
                               status, created_at, updated_at
                        FROM schengen_visa_applications
                        WHERE application_id = %s
                    """, (form_id,))

                main_data = cur.fetchone()
                if not main_data:
                    raise HTTPException(status_code=404, detail="表单不存在")

                # 格式化日期函数
                def format_date(year, month, day):
                    if year and month and day:
                        return f"{day:02d}/{month:02d}/{year}"
                    return ""

                # 构建基础数据
                result = {
                    "id": main_data[0],
                    "application_id": main_data[0],
                    "form_name": f"{main_data[2]} {main_data[3]} - {main_data[0]}",
                    "status": main_data[13],
                    "created_at": main_data[14].strftime('%Y-%m-%d %H:%M:%S') if main_data[14] else None,
                    "updated_at": main_data[15].strftime('%Y-%m-%d %H:%M:%S') if main_data[15] else None,

                    # 第一步：基本资格信息
                    "surname": main_data[3] or "",
                    "given_name": main_data[2] or "",
                    "gender": "M" if main_data[4] == "MALE" else "F",
                    "birth_date": format_date(main_data[5], main_data[6], main_data[7]),
                    "nationality": main_data[8] or "",
                    "passport_number": main_data[9] or "",
                    "passport_expire_date": format_date(main_data[10], main_data[11], main_data[12]),
                }

                # 获取护照详细信息（第二步）
                cur.execute("""
                    SELECT sur_name, surname_at_birth, given_name, date_of_birth_year,
                           date_of_birth_month, date_of_birth_day, country_of_birth,
                           place_of_birth, nationality_id, nationality_at_birth_id,
                           gender_id, marital_status_id, is_minor_applicant, id_number,
                           passport_type_id, passport_number, issue_date_year, issue_date_month,
                           issue_date_day, issued_country, expiry_date_year, expiry_date_month,
                           expiry_date_day, issued_by, guardian_type, guardian_surname,
                           guardian_given_name, guardian_address, guardian_nationality
                    FROM schengen_passport_info
                    WHERE application_id = %s
                """, (form_id,))
                passport_data = cur.fetchone()

                if passport_data:
                    result.update({
                        "passport_surname": passport_data[0] or "",
                        "birth_surname": passport_data[1] or "",
                        "passport_given_name": passport_data[2] or "",
                        "passport_birth_date": format_date(passport_data[3], passport_data[4], passport_data[5]),
                        "birth_country": passport_data[6] or "",
                        "birth_place": passport_data[7] or "",
                        "current_nationality": passport_data[8] or "",
                        "birth_nationality": passport_data[9] or "",
                        "passport_gender": "M" if passport_data[10] == "MALE" else "F",
                        "passport_marital_status": passport_data[11] or "",
                        "is_minor": "yes" if passport_data[12] == 1 else "no",
                        "passport_number_detail": passport_data[15] or "",
                        "passport_type": passport_data[14] or "",
                        "passport_issue_date_detail": format_date(passport_data[16], passport_data[17], passport_data[18]),
                        "issuing_country": passport_data[19] or "",
                        "passport_expire_date_detail": format_date(passport_data[20], passport_data[21], passport_data[22]),
                        "issuing_authority": passport_data[23] or "",
                        # 监护人信息
                        "guardian_type": passport_data[24] or "",
                        "guardian_surname": passport_data[25] or "",
                        "guardian_given_name": passport_data[26] or "",
                        "guardian_address": passport_data[27] or "",
                        "guardian_nationality": passport_data[28] or "",
                    })
                else:
                    # 如果没有护照详细信息，使用默认值
                    result.update({
                        "passport_surname": "",
                        "birth_surname": "",
                        "passport_given_name": "",
                        "passport_birth_date": "",
                        "birth_country": "CHN",
                        "birth_place": "",
                        "current_nationality": "CHN",
                        "birth_nationality": "CHN",
                        "passport_gender": "",
                        "passport_marital_status": "",
                        "is_minor": "no",
                        "passport_number_detail": "",
                        "passport_type": "",
                        "passport_issue_date_detail": "",
                        "issuing_country": "CHN",
                        "passport_expire_date_detail": "",
                        "issuing_authority": "",
                        # 监护人信息默认值
                        "guardian_type": "",
                        "guardian_surname": "",
                        "guardian_given_name": "",
                        "guardian_address": "",
                        "guardian_nationality": "CHN",
                    })

                # 获取申请人信息（第三步）
                cur.execute("""
                    SELECT applicant_country, applicant_address, occupation_id, occupation_others,
                           applicant_email, applicant_telephone_number, residence_other_nationality,
                           residence_country_permit_no, residence_country_permit_valid_until_year,
                           residence_country_permit_valid_until_month, residence_country_permit_valid_until_day,
                           employer_name, employer_address, employer_mobile, employer_city,
                           employer_home_postal_code, employer_home_country, fingerprints_collected,
                           date_of_collection_year, date_of_collection_month, date_of_collection_day,
                           previous_application_number
                    FROM schengen_applicant_info
                    WHERE application_id = %s
                """, (form_id,))
                applicant_data = cur.fetchone()

                if applicant_data:
                    result.update({
                        "applicant_country": applicant_data[0] or "",
                        "applicant_address": applicant_data[1] or "",
                        "occupation_id": applicant_data[2] or "",
                        "occupation_others": applicant_data[3] or "",
                        "applicant_email": applicant_data[4] or "",
                        "applicant_phone": applicant_data[5] or "",
                        "residence_other_nationality": applicant_data[6] or 2,
                        "residence_permit_no": applicant_data[7] or "",
                        "residence_permit_valid_until": format_date(applicant_data[8], applicant_data[9], applicant_data[10]),
                        "employer_name": applicant_data[11] or "",
                        "employer_address": applicant_data[12] or "",
                        "employer_phone": applicant_data[13] or "",
                        "employer_city": applicant_data[14] or "",
                        "employer_postal_code": applicant_data[15] or "",
                        "employer_country": applicant_data[16] or "",
                        "fingerprints_collected": applicant_data[17] or 2,
                        "date_of_collection": format_date(applicant_data[18], applicant_data[19], applicant_data[20]),
                        "previous_application_number": applicant_data[21] or "",
                    })
                else:
                    result.update({
                        "applicant_country": "",
                        "applicant_address": "",
                        "occupation_id": "",
                        "occupation_others": "",
                        "applicant_email": "",
                        "applicant_phone": "",
                        "residence_other_nationality": 2,
                        "residence_permit_no": "",
                        "residence_permit_valid_until": "",
                        "employer_name": "",
                        "employer_address": "",
                        "employer_phone": "",
                        "employer_city": "",
                        "employer_postal_code": "",
                        "employer_country": "",
                        "fingerprints_collected": 2,
                        "date_of_collection": "",
                        "previous_application_number": "",
                    })

                # 获取旅行信息（第四步）
                cur.execute("""
                    SELECT purpose_of_travel, purpose_of_travel_others, purpose_of_travel_add_info,
                           number_of_entries, is_schengen_visa_issued, valid_from_year,
                           valid_from_month, valid_from_day, valid_till_year, valid_till_month,
                           valid_till_day, is_adequate_medical_insurance
                    FROM schengen_travel_info
                    WHERE application_id = %s
                """, (form_id,))
                travel_data = cur.fetchone()

                if travel_data:
                    result.update({
                        "purpose_of_travel": travel_data[0] or "",
                        "purpose_of_travel_others": travel_data[1] or "",
                        "purpose_of_travel_add_info": travel_data[2] or "",
                        "number_of_entries": travel_data[3] or "",
                        "is_schengen_visa_issued": travel_data[4] or 2,
                        "valid_from": format_date(travel_data[5], travel_data[6], travel_data[7]),
                        "valid_till": format_date(travel_data[8], travel_data[9], travel_data[10]),
                        "is_adequate_medical_insurance": travel_data[11] or 2,
                    })
                else:
                    result.update({
                        "purpose_of_travel": "",
                        "purpose_of_travel_others": "",
                        "purpose_of_travel_add_info": "",
                        "number_of_entries": "",
                        "is_schengen_visa_issued": 2,
                        "valid_from": "",
                        "valid_till": "",
                        "is_adequate_medical_insurance": 2,
                    })

                # 获取住宿信息（第五步）
                cur.execute("""
                    SELECT inviting_party_id, name_of_organisation, address_of_organisation,
                           name_of_enterprise, address_of_enterprise, postal_code_of_enterprise,
                           email_enterprise, street_enterprise, city_enterprise, country_enterprise,
                           enterprise_telephone_isd_code, enterprise_telephone_number,
                           sur_name_of_contact_inviting_person, first_name_of_contact_inviting_person,
                           address_of_inviting_person, postal_code_of_inviting_person, email_inviting_person,
                           street_inviting_person, city_inviting_person, country_inviting_person,
                           name_of_inviting_hotel, address_of_inviting_hotel, postal_code_of_inviting_hotel,
                           email_inviting_hotel, street_inviting_hotel, city_inviting_hotel, country_inviting_hotel,
                           inviting_hotel_telephone_isd_code, inviting_hotel_telephone_number,
                           organisation_telephone_isd_code, organisation_telephone_number,
                           temp_accommodation_telephone_isd_code, temp_accommodation_telephone_number,
                           regulations_telephone_isd_code, regulations_telephone_number,phone_code_of_inviting_person,
                                phone_of_inviting_person
                    FROM schengen_accommodation_info
                    WHERE application_id = %s
                """, (form_id,))
                accommodation_data = cur.fetchone()

                if accommodation_data:
                    # 根据邀请方类型确定类型名称（使用正确的映射）
                    inviting_party_type_mapping = {
                        1: "InvitingPerson",
                        2: "Invitingenterprise",
                        3: "Hotel",
                        4: "Temporary accommodation",
                        5: "InvitingOrganisation",
                        6: "RegulationsstayinSwitzerland"
                    }
                    inviting_party_type = inviting_party_type_mapping.get(accommodation_data[0], "Hotel")

                    # 添加调试日志
                    log_info("获取住宿信息映射",
                             request_id=request_id,
                             inviting_party_id_from_db=accommodation_data[0],
                             mapped_inviting_party_type=inviting_party_type)

                    # 基础数据（更新字段索引以匹配新的SELECT语句）
                    base_data = {
                        "inviting_party_type": inviting_party_type,
                        "organisation_name": accommodation_data[1] or "",
                        "organisation_address": accommodation_data[2] or "",
                        "enterprise_name": accommodation_data[3] or "",
                        "enterprise_address": accommodation_data[4] or "",
                        "enterprise_postal_code": accommodation_data[5] or "",
                        "enterprise_email": accommodation_data[6] or "",
                        "enterprise_street": accommodation_data[7] or "",
                        "enterprise_city": accommodation_data[8] or "",
                        "enterprise_country": accommodation_data[9] or "",
                        "enterprise_phone_code": accommodation_data[10] or "",
                        "enterprise_phone": accommodation_data[11] or "",
                        "inviting_person_surname": accommodation_data[12] or "",
                        "inviting_person_given_name": accommodation_data[13] or "",
                        "inviting_person_address": accommodation_data[14] or "",
                        "inviting_person_postal_code": accommodation_data[15] or "",
                        "inviting_person_email": accommodation_data[16] or "",
                        "inviting_person_street": accommodation_data[17] or "",
                        "inviting_person_city": accommodation_data[18] or "",
                        "inviting_person_country": accommodation_data[19] or "",
                        "inviting_person_phone_code": accommodation_data[35] or "",
                        "inviting_person_phone": accommodation_data[36] or "",
                        "hotel_name": accommodation_data[20] or "",
                        "hotel_address": accommodation_data[21] or "",
                        "hotel_postal_code": accommodation_data[22] or "",
                        "hotel_email": accommodation_data[23] or "",
                        "hotel_street": accommodation_data[24] or "",
                        "hotel_city": accommodation_data[25] or "",
                        "hotel_country": accommodation_data[26] or "",
                        "hotel_phone_code": accommodation_data[27] or "",
                        "hotel_phone": accommodation_data[28] or "",
                        "organisation_phone_code": accommodation_data[29] or "",
                        "organisation_phone": accommodation_data[30] or "",
                        "temp_accommodation_phone_code": accommodation_data[31] or "",
                        "temp_accommodation_phone": accommodation_data[32] or "",
                        "regulations_phone_code": accommodation_data[33] or "",
                        "regulations_phone": accommodation_data[34] or "",
                    }

                    # 根据邀请方类型添加特定字段（使用正确的字段索引）
                    if inviting_party_type == "Temporary accommodation":
                        base_data.update({
                            "temp_accommodation_address": accommodation_data[21] or "",
                            "temp_accommodation_postal_code": accommodation_data[22] or "",
                            "temp_accommodation_email": accommodation_data[23] or "",
                            "temp_accommodation_street": accommodation_data[24] or "",
                            "temp_accommodation_city": accommodation_data[25] or "",
                            "temp_accommodation_country": accommodation_data[26] or "",
                            # 电话区号和电话号码已经在base_data中正确设置
                        })
                    elif inviting_party_type == "RegulationsstayinSwitzerland":
                        base_data.update({
                            "regulations_address": accommodation_data[21] or "",
                            "regulations_postal_code": accommodation_data[22] or "",
                            "regulations_email": accommodation_data[23] or "",
                            "regulations_street": accommodation_data[24] or "",
                            "regulations_city": accommodation_data[25] or "",
                            "regulations_country": accommodation_data[26] or "",
                            # 电话区号和电话号码已经在base_data中正确设置
                        })

                    result.update(base_data)
                else:
                    result.update({
                        "inviting_party_type": "Hotel",
                        "organisation_name": "",
                        "organisation_address": "",
                        "enterprise_name": "",
                        "enterprise_address": "",
                        "enterprise_postal_code": "",
                        "enterprise_email": "",
                        "enterprise_street": "",
                        "enterprise_city": "",
                        "enterprise_country": "CHN",
                        "enterprise_phone": "",
                        "inviting_person_surname": "",
                        "inviting_person_given_name": "",
                        "inviting_person_address": "",
                        "inviting_person_postal_code": "",
                        "inviting_person_email": "",
                        "inviting_person_street": "",
                        "inviting_person_city": "",
                        "inviting_person_country": "CHN",
                        "hotel_name": "",
                        "hotel_address": "",
                        "hotel_postal_code": "",
                        "hotel_email": "",
                        "hotel_street": "",
                        "hotel_city": "",
                        "hotel_country": "",
                        "hotel_phone": "",
                        # 临时住宿默认值
                        "temp_accommodation_address": "",
                        "temp_accommodation_postal_code": "",
                        "temp_accommodation_email": "",
                        "temp_accommodation_street": "",
                        "temp_accommodation_city": "",
                        "temp_accommodation_country": "CHN",
                        "temp_accommodation_phone": "",
                        "temp_accommodation_phone_code": "+86",
                        # 瑞士居留规定默认值
                        "regulations_address": "",
                        "regulations_postal_code": "",
                        "regulations_email": "",
                        "regulations_street": "",
                        "regulations_city": "",
                        "regulations_country": "CHN",
                        "regulations_phone": "",
                        "regulations_phone_code": "+86",
                    })

                # 获取附加信息（第六步）
                cur.execute("""
                    SELECT final_destination, arrival_date_year, arrival_date_month, arrival_date_day,
                           departure_date_year, departure_date_month, departure_date_day, duration_of_stay,
                           cost_of_travelling_covered_by, cost_of_travelling_covered_by_others,
                           means_of_support_id, means_of_support_others, is_citizen_id,
                           eu_surname, eu_first_name, eu_nationality_id, eu_date_of_birth_year,
                           eu_date_of_birth_month, eu_date_of_birth_day, eu_passport_number,
                           eu_relationship_id, schengen_state_first_entry, country_of_destination,
                           inviting_person_covered_costs, costs_covered_by
                    FROM schengen_additional_info
                    WHERE application_id = %s
                """, (form_id,))
                additional_data = cur.fetchone()

                if additional_data:
                    # 解析JSON字段
                    cost_covered_by = additional_data[8]
                    if isinstance(cost_covered_by, str):
                        try:
                            cost_covered_by = json.loads(cost_covered_by)
                        except:
                            cost_covered_by = []

                    means_of_support = additional_data[10]
                    if isinstance(means_of_support, str):
                        try:
                            means_of_support = json.loads(means_of_support)
                        except:
                            means_of_support = []

                    destination_countries = additional_data[22]
                    if isinstance(destination_countries, str):
                        try:
                            destination_countries = json.loads(destination_countries)
                        except:
                            destination_countries = []

                    result.update({
                        "is_transit_visa": additional_data[0] or 2,
                        "arrival_date": format_date(additional_data[1], additional_data[2], additional_data[3]),
                        "departure_date": format_date(additional_data[4], additional_data[5], additional_data[6]),
                        "duration_of_stay": additional_data[7] or "",
                        "cost_covered_by": cost_covered_by,
                        "cost_covered_by_others": additional_data[9] or "",
                        "means_of_support": means_of_support,
                        "means_of_support_others": additional_data[11] or "",
                        "is_eu_citizen_family": additional_data[12] or 2,
                        "eu_citizen_surname": additional_data[13] or "",
                        "eu_citizen_given_name": additional_data[14] or "",
                        "eu_citizen_nationality": additional_data[15] or "",
                        "eu_citizen_birth_date": format_date(additional_data[16], additional_data[17], additional_data[18]),
                        "eu_citizen_passport_number": additional_data[19] or "",
                        "eu_citizen_relationship": additional_data[20] or "",
                        "first_entry_schengen": additional_data[21] or "",
                        "destination_countries": destination_countries,
                        "inviting_person_covered_costs": additional_data[23] or 2,
                        "costsCoveredBy": additional_data[24] or "",
                    })
                else:
                    result.update({
                        "is_transit_visa": 2,
                        "arrival_date": "",
                        "departure_date": "",
                        "duration_of_stay": "",
                        "cost_covered_by": [],
                        "cost_covered_by_others": "",
                        "means_of_support": [],
                        "means_of_support_others": "",
                        "is_eu_citizen_family": 2,
                        "eu_citizen_surname": "",
                        "eu_citizen_given_name": "",
                        "eu_citizen_nationality": "",
                        "eu_citizen_birth_date": "",
                        "eu_citizen_passport_number": "",
                        "eu_citizen_relationship": "",
                        "first_entry_schengen": "",
                        "destination_countries": [],
                        "inviting_person_covered_costs": 2,
                        "costsCoveredBy": "",
                    })

                log_info("获取申根签证表单详情成功",
                         request_id=request_id,
                         form_id=form_id)

                return {
                    "code": 1,
                    "message": "success",
                    "data": result
                }

    except HTTPException:
        raise
    except Exception as e:
        log_error("获取申根签证表单详情失败",
                  error=e,
                  request_id=request_id,
                  form_id=form_id)
        raise HTTPException(status_code=500, detail=f"获取表单详情失败: {str(e)}")


@app.put("/api/schengen-forms/{form_id}", summary="更新申根签证表单", tags=["申根签证"])
def update_schengen_form(
    form_id: str,
    request_data: dict,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """更新申根签证表单"""
    request_id = generate_request_id()

    log_info("更新申根签证表单",
             request_id=request_id,
             form_id=form_id,
             user_id=payload.user_id,
             permission=payload.permission,
             data_keys=list(request_data.keys()))

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 根据权限检查表单是否存在
                if payload.permission not in ['kefu', 'admin']:
                    # 普通用户只能编辑自己的表单
                    cur.execute("""
                        SELECT id FROM schengen_visa_applications
                        WHERE application_id = %s AND user_id = %s
                    """, (form_id, payload.user_id))
                else:
                    # 管理员/客服可以编辑所有表单
                    cur.execute("""
                        SELECT id FROM schengen_visa_applications
                        WHERE application_id = %s
                    """, (form_id,))

                if not cur.fetchone():
                    raise HTTPException(status_code=404, detail="表单不存在")

                # 解析日期函数
                def parse_date(date_str):
                    if not date_str:
                        return None, None, None
                    try:
                        parts = date_str.split('/')
                        if len(parts) == 3:
                            return int(parts[2]), int(parts[1]), int(parts[0])
                    except:
                        pass
                    return None, None, None

                # 从前端数据中提取字段
                surname = request_data.get('surname', '')
                given_name = request_data.get('given_name', '')
                gender = request_data.get('gender', 'M')
                birth_date = request_data.get('birth_date', '')
                nationality = request_data.get('nationality', '')
                passport_number = request_data.get('passport_number', '')
                passport_expire_date = request_data.get('passport_expire_date', '')
                status = request_data.get('status', 'draft')

                birth_year, birth_month, birth_day = parse_date(birth_date)
                expire_year, expire_month, expire_day = parse_date(passport_expire_date)

                # 更新主表数据
                cur.execute("""
                    UPDATE schengen_visa_applications
                    SET first_name = %s, last_name = %s, gender_id = %s,
                        date_of_birth_year = %s, date_of_birth_month = %s, date_of_birth_day = %s,
                        nationality_id = %s, passport_number = %s,
                        expiry_date_year = %s, expiry_date_month = %s, expiry_date_day = %s,
                        status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE application_id = %s AND user_id = %s
                """, (
                    given_name, surname,
                    "MALE" if gender == "M" else "FEMALE",
                    birth_year, birth_month, birth_day,
                    nationality, passport_number,
                    expire_year, expire_month, expire_day,
                    status, form_id, payload.user_id
                ))

                # 更新或插入护照详细信息
                if request_data.get('passport_surname') or request_data.get('passport_given_name'):
                    passport_birth_year, passport_birth_month, passport_birth_day = parse_date(request_data.get('passport_birth_date', ''))
                    passport_issue_year, passport_issue_month, passport_issue_day = parse_date(request_data.get('passport_issue_date_detail', ''))
                    passport_expire_year, passport_expire_month, passport_expire_day = parse_date(request_data.get('passport_expire_date_detail', ''))

                    # 检查是否已存在护照信息
                    cur.execute("SELECT id FROM schengen_passport_info WHERE application_id = %s", (form_id,))
                    if cur.fetchone():
                        # 更新
                        cur.execute("""
                            UPDATE schengen_passport_info SET
                                sur_name = %s, surname_at_birth = %s, given_name = %s,
                                date_of_birth_year = %s, date_of_birth_month = %s, date_of_birth_day = %s,
                                country_of_birth = %s, place_of_birth = %s, nationality_id = %s,
                                nationality_at_birth_id = %s, gender_id = %s, marital_status_id = %s,
                                is_minor_applicant = %s, id_number = %s, passport_type_id = %s,
                                passport_number = %s, reenter_number_of_passport = %s,
                                issue_date_year = %s, issue_date_month = %s, issue_date_day = %s,
                                issued_country = %s, expiry_date_year = %s, expiry_date_month = %s,
                                expiry_date_day = %s, issued_by = %s,
                                guardian_type = %s, guardian_surname = %s, guardian_given_name = %s,
                                guardian_address = %s, guardian_nationality = %s,
                                updated_at = CURRENT_TIMESTAMP
                            WHERE application_id = %s
                        """, (
                            request_data.get('passport_surname', ''),
                            request_data.get('birth_surname', ''),
                            request_data.get('passport_given_name', ''),
                            passport_birth_year, passport_birth_month, passport_birth_day,
                            request_data.get('birth_country', ''),
                            request_data.get('birth_place', ''),
                            request_data.get('current_nationality', ''),
                            request_data.get('birth_nationality', ''),
                            "MALE" if request_data.get('passport_gender') == "M" else "FEMALE",
                            request_data.get('passport_marital_status', ''),
                            1 if request_data.get('is_minor') == 'yes' else 2,
                            request_data.get('passport_number_detail', ''),
                            request_data.get('passport_type', ''),
                            request_data.get('passport_number_detail', ''),
                            request_data.get('passport_number_detail', ''),
                            passport_issue_year, passport_issue_month, passport_issue_day,
                            request_data.get('issuing_country', ''),
                            passport_expire_year, passport_expire_month, passport_expire_day,
                            request_data.get('issuing_authority', ''),
                            # 监护人信息
                            request_data.get('guardian_type', '') if request_data.get('is_minor') == 'yes' else None,
                            request_data.get('guardian_surname', '') if request_data.get('is_minor') == 'yes' else None,
                            request_data.get('guardian_given_name', '') if request_data.get('is_minor') == 'yes' else None,
                            request_data.get('guardian_address', '') if request_data.get('is_minor') == 'yes' else None,
                            request_data.get('guardian_nationality', '') if request_data.get('is_minor') == 'yes' else None,
                            form_id
                        ))
                    else:
                        # 插入新的护照信息记录
                        cur.execute("""
                            INSERT INTO schengen_passport_info (
                                application_id, sur_name, surname_at_birth, given_name,
                                date_of_birth_year, date_of_birth_month, date_of_birth_day,
                                country_of_birth, place_of_birth, nationality_id,
                                nationality_at_birth_id, gender_id, marital_status_id,
                                is_minor_applicant, id_number, passport_type_id,
                                passport_number, reenter_number_of_passport,
                                issue_date_year, issue_date_month, issue_date_day,
                                issued_country, expiry_date_year, expiry_date_month,
                                expiry_date_day, issued_by,
                                guardian_type, guardian_surname, guardian_given_name,
                                guardian_address, guardian_nationality
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                        """, (
                            form_id,
                            request_data.get('passport_surname', ''),
                            request_data.get('birth_surname', ''),
                            request_data.get('passport_given_name', ''),
                            passport_birth_year, passport_birth_month, passport_birth_day,
                            request_data.get('birth_country', ''),
                            request_data.get('birth_place', ''),
                            request_data.get('current_nationality', ''),
                            request_data.get('birth_nationality', ''),
                            "MALE" if request_data.get('passport_gender') == "M" else "FEMALE",
                            request_data.get('passport_marital_status', ''),
                            1 if request_data.get('is_minor') == 'yes' else 2,
                            request_data.get('passport_number_detail', ''),
                            request_data.get('passport_type', ''),
                            request_data.get('passport_number_detail', ''),
                            request_data.get('passport_number_detail', ''),
                            passport_issue_year, passport_issue_month, passport_issue_day,
                            request_data.get('issuing_country', ''),
                            passport_expire_year, passport_expire_month, passport_expire_day,
                            request_data.get('issuing_authority', ''),
                            # 监护人信息
                            request_data.get('guardian_type', '') if request_data.get('is_minor') == 'yes' else None,
                            request_data.get('guardian_surname', '') if request_data.get('is_minor') == 'yes' else None,
                            request_data.get('guardian_given_name', '') if request_data.get('is_minor') == 'yes' else None,
                            request_data.get('guardian_address', '') if request_data.get('is_minor') == 'yes' else None,
                            request_data.get('guardian_nationality', '') if request_data.get('is_minor') == 'yes' else None
                        ))

                # 更新申请人信息（第三步）
                if any(key in request_data for key in ['applicant_country', 'applicant_address', 'occupation_id', 'applicant_email']):
                    # 检查是否已存在申请人信息
                    cur.execute("SELECT id FROM schengen_applicant_info WHERE application_id = %s", (form_id,))
                    if cur.fetchone():
                        # 更新申请人信息
                        # 解析日期
                        residence_permit_year, residence_permit_month, residence_permit_day = parse_date(request_data.get('residence_permit_valid_until', ''))
                        collection_year, collection_month, collection_day = parse_date(request_data.get('date_of_collection', ''))

                        cur.execute("""
                            UPDATE schengen_applicant_info SET
                                applicant_country = %s,
                                applicant_address = %s,
                                occupation_id = %s,
                                occupation_others = %s,
                                applicant_email = %s,
                                applicant_telephone_isd_code = %s,
                                applicant_telephone_number = %s,
                                residence_other_nationality = %s,
                                residence_country_permit_no = %s,
                                residence_country_permit_valid_until_year = %s,
                                residence_country_permit_valid_until_month = %s,
                                residence_country_permit_valid_until_day = %s,
                                employer_name = %s,
                                employer_address = %s,
                                employer_mobile = %s,
                                employer_city = %s,
                                employer_home_postal_code = %s,
                                employer_home_country = %s,
                                fingerprints_collected = %s,
                                date_of_collection_year = %s,
                                date_of_collection_month = %s,
                                date_of_collection_day = %s,
                                previous_application_number = %s,
                                updated_at = CURRENT_TIMESTAMP
                            WHERE application_id = %s
                        """, (
                            request_data.get('applicant_country', ''),
                            request_data.get('applicant_address', ''),
                            request_data.get('occupation_id', ''),
                            request_data.get('occupation_others', ''),
                            request_data.get('applicant_email', ''),
                            request_data.get('applicant_phone_code', '+86'),
                            request_data.get('applicant_phone', ''),
                            request_data.get('residence_other_nationality', 2),
                            request_data.get('residence_permit_no', ''),
                            residence_permit_year, residence_permit_month, residence_permit_day,
                            request_data.get('employer_name', ''),
                            request_data.get('employer_address', ''),
                            request_data.get('employer_phone', ''),
                            request_data.get('employer_city', ''),
                            request_data.get('employer_postal_code', ''),
                            request_data.get('employer_country', ''),
                            request_data.get('fingerprints_collected', 2),
                            collection_year, collection_month, collection_day,
                            request_data.get('previous_application_number', ''),
                            form_id
                        ))
                    else:
                        # 插入新的申请人信息记录
                        residence_permit_year, residence_permit_month, residence_permit_day = parse_date(request_data.get('residence_permit_valid_until', ''))
                        collection_year, collection_month, collection_day = parse_date(request_data.get('date_of_collection', ''))

                        cur.execute("""
                            INSERT INTO schengen_applicant_info (
                                application_id, applicant_country, applicant_address, occupation_id,
                                occupation_others, applicant_email, applicant_telephone_isd_code,
                                applicant_telephone_number, residence_other_nationality,
                                residence_country_permit_no, residence_country_permit_valid_until_year,
                                residence_country_permit_valid_until_month, residence_country_permit_valid_until_day,
                                employer_name, employer_address, employer_mobile, employer_city,
                                employer_home_postal_code, employer_home_country, fingerprints_collected,
                                date_of_collection_year, date_of_collection_month, date_of_collection_day,
                                previous_application_number
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                        """, (
                            form_id,
                            request_data.get('applicant_country', ''),
                            request_data.get('applicant_address', ''),
                            request_data.get('occupation_id', ''),
                            request_data.get('occupation_others', ''),
                            request_data.get('applicant_email', ''),
                            request_data.get('applicant_phone_code', '+86'),
                            request_data.get('applicant_phone', ''),
                            request_data.get('residence_other_nationality', 2),
                            request_data.get('residence_permit_no', ''),
                            residence_permit_year, residence_permit_month, residence_permit_day,
                            request_data.get('employer_name', ''),
                            request_data.get('employer_address', ''),
                            request_data.get('employer_phone', ''),
                            request_data.get('employer_city', ''),
                            request_data.get('employer_postal_code', ''),
                            request_data.get('employer_country', ''),
                            request_data.get('fingerprints_collected', 2),
                            collection_year, collection_month, collection_day,
                            request_data.get('previous_application_number', '')
                        ))

                # 更新旅行信息（第四步）
                if any(key in request_data for key in ['purpose_of_travel', 'number_of_entries', 'is_schengen_visa_issued']):
                    # 解析日期
                    valid_from_year, valid_from_month, valid_from_day = parse_date(request_data.get('valid_from', ''))
                    valid_till_year, valid_till_month, valid_till_day = parse_date(request_data.get('valid_till', ''))

                    # 检查是否已存在旅行信息
                    cur.execute("SELECT id FROM schengen_travel_info WHERE application_id = %s", (form_id,))
                    if cur.fetchone():
                        # 更新旅行信息
                        cur.execute("""
                            UPDATE schengen_travel_info SET
                                purpose_of_travel = %s,
                                purpose_of_travel_others = %s,
                                purpose_of_travel_add_info = %s,
                                number_of_entries = %s,
                                is_schengen_visa_issued = %s,
                                valid_from_year = %s,
                                valid_from_month = %s,
                                valid_from_day = %s,
                                valid_till_year = %s,
                                valid_till_month = %s,
                                valid_till_day = %s,
                                is_adequate_medical_insurance = %s,
                                updated_at = CURRENT_TIMESTAMP
                            WHERE application_id = %s
                        """, (
                            request_data.get('purpose_of_travel', ''),
                            request_data.get('purpose_of_travel_others', ''),
                            request_data.get('purpose_of_travel_add_info', ''),
                            request_data.get('number_of_entries', ''),
                            request_data.get('is_schengen_visa_issued', 2),
                            valid_from_year, valid_from_month, valid_from_day,
                            valid_till_year, valid_till_month, valid_till_day,
                            request_data.get('is_adequate_medical_insurance', 2),
                            form_id
                        ))
                    else:
                        # 插入新的旅行信息记录
                        cur.execute("""
                            INSERT INTO schengen_travel_info (
                                application_id, purpose_of_travel, purpose_of_travel_others,
                                purpose_of_travel_add_info, number_of_entries, is_schengen_visa_issued,
                                valid_from_year, valid_from_month, valid_from_day,
                                valid_till_year, valid_till_month, valid_till_day,
                                is_adequate_medical_insurance
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                        """, (
                            form_id,
                            request_data.get('purpose_of_travel', ''),
                            request_data.get('purpose_of_travel_others', ''),
                            request_data.get('purpose_of_travel_add_info', ''),
                            request_data.get('number_of_entries', ''),
                            request_data.get('is_schengen_visa_issued', 2),
                            valid_from_year, valid_from_month, valid_from_day,
                            valid_till_year, valid_till_month, valid_till_day,
                            request_data.get('is_adequate_medical_insurance', 2)
                        ))

                # 更新住宿信息（第五步）
                if request_data.get('inviting_party_type'):
                    inviting_party_type = request_data.get('inviting_party_type', '')
                    # 根据正确的映射关系设置inviting_party_id
                    inviting_party_id = 3  # 默认值：Hotel
                    if inviting_party_type == 'InvitingPerson':
                        inviting_party_id = 1
                    elif inviting_party_type == 'Invitingenterprise':
                        inviting_party_id = 2
                    elif inviting_party_type == 'Hotel':
                        inviting_party_id = 3
                    elif inviting_party_type == 'Temporary accommodation':
                        inviting_party_id = 4
                    elif inviting_party_type == 'InvitingOrganisation':
                        inviting_party_id = 5
                    elif inviting_party_type == 'RegulationsstayinSwitzerland':
                        inviting_party_id = 6

                    # 添加调试日志
                    log_info("更新住宿信息映射",
                             request_id=request_id,
                             inviting_party_type=inviting_party_type,
                             inviting_party_id=inviting_party_id)

                    # 根据邀请方类型获取相应的数据
                    def get_accommodation_data_for_update():
                        if inviting_party_type == 'Hotel':
                            return {
                                'name': request_data.get('hotel_name', ''),
                                'address': request_data.get('hotel_address', ''),
                                'postal_code': request_data.get('hotel_postal_code', ''),
                                'email': request_data.get('hotel_email', ''),
                                'street': request_data.get('hotel_street', ''),
                                'city': request_data.get('hotel_city', ''),
                                'country': request_data.get('hotel_country', ''),
                                'phone_code': request_data.get('hotel_phone_code', '+86'),
                                'phone': request_data.get('hotel_phone', '')
                            }
                        elif inviting_party_type == 'Temporary accommodation':
                            return {
                                'name': '',
                                'address': request_data.get('temp_accommodation_address', ''),
                                'postal_code': request_data.get('temp_accommodation_postal_code', ''),
                                'email': request_data.get('temp_accommodation_email', ''),
                                'street': request_data.get('temp_accommodation_street', ''),
                                'city': request_data.get('temp_accommodation_city', ''),
                                'country': request_data.get('temp_accommodation_country', ''),
                                'phone_code': request_data.get('temp_accommodation_phone_code', '+86'),
                                'phone': request_data.get('temp_accommodation_phone', '')
                            }
                        elif inviting_party_type == 'RegulationsstayinSwitzerland':
                            return {
                                'name': '',
                                'address': request_data.get('regulations_address', ''),
                                'postal_code': request_data.get('regulations_postal_code', ''),
                                'email': request_data.get('regulations_email', ''),
                                'street': request_data.get('regulations_street', ''),
                                'city': request_data.get('regulations_city', ''),
                                'country': request_data.get('regulations_country', ''),
                                'phone_code': request_data.get('regulations_phone_code', '+86'),
                                'phone': request_data.get('regulations_phone', '')
                            }
                        else:
                            return {
                                'name': '',
                                'address': '',
                                'postal_code': '',
                                'email': '',
                                'street': '',
                                'city': '',
                                'country': '',
                                'phone_code': '+86',
                                'phone': ''
                            }

                    accommodation_data = get_accommodation_data_for_update()

                    # 检查是否已存在住宿信息
                    cur.execute("SELECT id FROM schengen_accommodation_info WHERE application_id = %s", (form_id,))
                    if cur.fetchone():
                        # 更新住宿信息
                        cur.execute("""
                            UPDATE schengen_accommodation_info SET
                                inviting_party_id = %s,
                                name_of_organisation = %s,
                                address_of_organisation = %s,
                                name_of_enterprise = %s,
                                address_of_enterprise = %s,
                                postal_code_of_enterprise = %s,
                                email_enterprise = %s,
                                street_enterprise = %s,
                                city_enterprise = %s,
                                country_enterprise = %s,
                                enterprise_telephone_isd_code = %s,
                                enterprise_telephone_number = %s,
                                sur_name_of_contact_inviting_person = %s,
                                first_name_of_contact_inviting_person = %s,
                                address_of_inviting_person = %s,
                                postal_code_of_inviting_person = %s,
                                phone_code_of_inviting_person = %s,
                                phone_of_inviting_person = %s,
                                email_inviting_person = %s,
                                street_inviting_person = %s,
                                city_inviting_person = %s,
                                country_inviting_person = %s,
                                name_of_inviting_hotel = %s,
                                address_of_inviting_hotel = %s,
                                postal_code_of_inviting_hotel = %s,
                                email_inviting_hotel = %s,
                                street_inviting_hotel = %s,
                                city_inviting_hotel = %s,
                                country_inviting_hotel = %s,
                                inviting_hotel_telephone_isd_code = %s,
                                inviting_hotel_telephone_number = %s,
                                organisation_telephone_isd_code = %s,
                                organisation_telephone_number = %s,
                                temp_accommodation_telephone_isd_code = %s,
                                temp_accommodation_telephone_number = %s,
                                regulations_telephone_isd_code = %s,
                                regulations_telephone_number = %s,
                                updated_at = CURRENT_TIMESTAMP
                            WHERE application_id = %s
                        """, (
                            inviting_party_id,
                            request_data.get('organisation_name', ''),
                            request_data.get('organisation_address', ''),
                            request_data.get('enterprise_name', ''),
                            request_data.get('enterprise_address', ''),
                            request_data.get('enterprise_postal_code', ''),
                            request_data.get('enterprise_email', ''),
                            request_data.get('enterprise_street', ''),
                            request_data.get('enterprise_city', ''),
                            request_data.get('enterprise_country', ''),
                            request_data.get('enterprise_phone_code', ''),
                            request_data.get('enterprise_phone', ''),
                            request_data.get('inviting_person_surname', ''),
                            request_data.get('inviting_person_given_name', ''),
                            request_data.get('inviting_person_address', ''),
                            request_data.get('inviting_person_postal_code', ''),
                            request_data.get('inviting_person_phone_code', ''),
                            request_data.get('inviting_person_phone', ''),
                            request_data.get('inviting_person_email', ''),
                            request_data.get('inviting_person_street', ''),
                            request_data.get('inviting_person_city', ''),
                            request_data.get('inviting_person_country', ''),
                            accommodation_data['name'],
                            accommodation_data['address'],
                            accommodation_data['postal_code'],
                            accommodation_data['email'],
                            accommodation_data['street'],
                            accommodation_data['city'],
                            accommodation_data['country'],
                            accommodation_data['phone_code'],
                            accommodation_data['phone'],
                            # 新增的电话区号字段
                            request_data.get('organisation_phone_code', '+86'),
                            request_data.get('organisation_phone', ''),
                            request_data.get('temp_accommodation_phone_code', '+86'),
                            request_data.get('temp_accommodation_phone', ''),
                            request_data.get('regulations_phone_code', '+86'),
                            request_data.get('regulations_phone', ''),
                            form_id
                        ))
                    else:
                        # 插入新的住宿信息记录
                        cur.execute("""
                            INSERT INTO schengen_accommodation_info (
                                application_id, inviting_party_id, name_of_organisation, address_of_organisation,
                                name_of_enterprise, address_of_enterprise, postal_code_of_enterprise,
                                email_enterprise, street_enterprise, city_enterprise, country_enterprise,
                                enterprise_telephone_isd_code, enterprise_telephone_number,
                                sur_name_of_contact_inviting_person, first_name_of_contact_inviting_person,
                                address_of_inviting_person, postal_code_of_inviting_person,phone_code_of_inviting_person,
                                phone_of_inviting_person,
                                email_inviting_person, street_inviting_person, city_inviting_person,
                                country_inviting_person, name_of_inviting_hotel, address_of_inviting_hotel,
                                postal_code_of_inviting_hotel, email_inviting_hotel, street_inviting_hotel,
                                city_inviting_hotel, country_inviting_hotel, inviting_hotel_telephone_isd_code,
                                inviting_hotel_telephone_number,
                                organisation_telephone_isd_code, organisation_telephone_number,
                                temp_accommodation_telephone_isd_code, temp_accommodation_telephone_number,
                                regulations_telephone_isd_code, regulations_telephone_number
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                        """, (
                            form_id, inviting_party_id,
                            request_data.get('organisation_name', ''),
                            request_data.get('organisation_address', ''),
                            request_data.get('enterprise_name', ''),
                            request_data.get('enterprise_address', ''),
                            request_data.get('enterprise_postal_code', ''),
                            request_data.get('enterprise_email', ''),
                            request_data.get('enterprise_street', ''),
                            request_data.get('enterprise_city', ''),
                            request_data.get('enterprise_country', ''),
                            request_data.get('enterprise_phone_code', ''),
                            request_data.get('enterprise_phone', ''),
                            request_data.get('inviting_person_surname', ''),
                            request_data.get('inviting_person_given_name', ''),
                            request_data.get('inviting_person_address', ''),
                            request_data.get('inviting_person_postal_code', ''),
                            request_data.get('inviting_person_phone_code', ''),
                            request_data.get('inviting_person_phone', ''),
                            request_data.get('inviting_person_email', ''),
                            request_data.get('inviting_person_street', ''),
                            request_data.get('inviting_person_city', ''),
                            request_data.get('inviting_person_country', ''),
                            request_data.get('hotel_name', ''),
                            request_data.get('hotel_address', ''),
                            request_data.get('hotel_postal_code', ''),
                            request_data.get('hotel_email', ''),
                            request_data.get('hotel_street', ''),
                            request_data.get('hotel_city', ''),
                            request_data.get('hotel_country', ''),
                            request_data.get('hotel_phone_code', ''),
                            request_data.get('hotel_phone', ''),
                            # 新增的电话区号字段
                            request_data.get('organisation_phone_code', '+86'),
                            request_data.get('organisation_phone', ''),
                            request_data.get('temp_accommodation_phone_code', '+86'),
                            request_data.get('temp_accommodation_phone', ''),
                            request_data.get('regulations_phone_code', '+86'),
                            request_data.get('regulations_phone', '')
                        ))

                # 更新附加信息（第六步）
                if any(key in request_data for key in ['arrival_date', 'departure_date', 'is_transit_visa', 'duration_of_stay',
                                                       'cost_covered_by', 'means_of_support', 'is_eu_citizen_family',
                                                       'first_entry_schengen', 'destination_countries', 'inviting_person_covered_costs',
                                                       'costsCoveredBy']):
                    # 解析日期
                    arrival_year, arrival_month, arrival_day = parse_date(request_data.get('arrival_date', ''))
                    departure_year, departure_month, departure_day = parse_date(request_data.get('departure_date', ''))
                    eu_birth_year, eu_birth_month, eu_birth_day = parse_date(request_data.get('eu_citizen_birth_date', ''))

                    # 处理JSON数组字段（与保存接口保持一致）

                    cost_covered_by = request_data.get('cost_covered_by', [])
                    if not isinstance(cost_covered_by, list):
                        cost_covered_by = [cost_covered_by] if cost_covered_by else []

                    means_of_support = request_data.get('means_of_support', [])
                    if not isinstance(means_of_support, list):
                        means_of_support = [means_of_support] if means_of_support else []

                    destination_countries = request_data.get('destination_countries', [])
                    if not isinstance(destination_countries, list):
                        destination_countries = [destination_countries] if destination_countries else []

                    # 检查是否已存在附加信息
                    cur.execute("SELECT id FROM schengen_additional_info WHERE application_id = %s", (form_id,))
                    if cur.fetchone():
                        # 更新附加信息
                        cur.execute("""
                            UPDATE schengen_additional_info SET
                                final_destination = %s,
                                arrival_date_year = %s,
                                arrival_date_month = %s,
                                arrival_date_day = %s,
                                departure_date_year = %s,
                                departure_date_month = %s,
                                departure_date_day = %s,
                                duration_of_stay = %s,
                                cost_of_travelling_covered_by = %s,
                                cost_of_travelling_covered_by_others = %s,
                                means_of_support_id = %s,
                                means_of_support_others = %s,
                                is_citizen_id = %s,
                                eu_surname = %s,
                                eu_first_name = %s,
                                eu_nationality_id = %s,
                                eu_date_of_birth_year = %s,
                                eu_date_of_birth_month = %s,
                                eu_date_of_birth_day = %s,
                                eu_passport_number = %s,
                                eu_relationship_id = %s,
                                schengen_state_first_entry = %s,
                                country_of_destination = %s,
                                inviting_person_covered_costs = %s,
                                costs_covered_by = %s,
                                updated_at = CURRENT_TIMESTAMP
                            WHERE application_id = %s
                        """, (
                            request_data.get('is_transit_visa', 2),
                            arrival_year, arrival_month, arrival_day,
                            departure_year, departure_month, departure_day,
                            request_data.get('duration_of_stay', ''),
                            json.dumps(cost_covered_by),
                            request_data.get('cost_covered_by_others', ''),
                            json.dumps(means_of_support),
                            request_data.get('means_of_support_others', ''),
                            request_data.get('is_eu_citizen_family', 2),
                            request_data.get('eu_citizen_surname', ''),
                            request_data.get('eu_citizen_given_name', ''),
                            request_data.get('eu_citizen_nationality', ''),
                            eu_birth_year, eu_birth_month, eu_birth_day,
                            request_data.get('eu_citizen_passport_number', ''),
                            request_data.get('eu_citizen_relationship', ''),
                            request_data.get('first_entry_schengen', ''),
                            json.dumps(destination_countries),
                            request_data.get('inviting_person_covered_costs', 2),
                            request_data.get('costsCoveredBy', ''),
                            form_id
                        ))
                    else:
                        # 插入新的附加信息记录
                        cur.execute("""
                            INSERT INTO schengen_additional_info (
                                application_id, final_destination, arrival_date_year, arrival_date_month,
                                arrival_date_day, departure_date_year, departure_date_month, departure_date_day,
                                duration_of_stay, cost_of_travelling_covered_by, cost_of_travelling_covered_by_others,
                                means_of_support_id, means_of_support_others, is_citizen_id,
                                eu_surname, eu_first_name, eu_nationality_id, eu_date_of_birth_year,
                                eu_date_of_birth_month, eu_date_of_birth_day, eu_passport_number,
                                eu_relationship_id, schengen_state_first_entry, country_of_destination,
                                inviting_person_covered_costs, costs_covered_by
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                        """, (
                            form_id,
                            request_data.get('is_transit_visa', 2),
                            arrival_year, arrival_month, arrival_day,
                            departure_year, departure_month, departure_day,
                            request_data.get('duration_of_stay', ''),
                            json.dumps(cost_covered_by),
                            request_data.get('cost_covered_by_others', ''),
                            json.dumps(means_of_support),
                            request_data.get('means_of_support_others', ''),
                            request_data.get('is_eu_citizen_family', 2),
                            request_data.get('eu_citizen_surname', ''),
                            request_data.get('eu_citizen_given_name', ''),
                            request_data.get('eu_citizen_nationality', ''),
                            eu_birth_year, eu_birth_month, eu_birth_day,
                            request_data.get('eu_citizen_passport_number', ''),
                            request_data.get('eu_citizen_relationship', ''),
                            request_data.get('first_entry_schengen', ''),
                            json.dumps(destination_countries),
                            request_data.get('inviting_person_covered_costs', 2),
                            request_data.get('costsCoveredBy', '')
                        ))

                log_info("更新申根签证表单成功",
                         request_id=request_id,
                         form_id=form_id)

                return {
                    "code": 1,
                    "message": "success",
                    "data": None
                }

    except HTTPException:
        raise
    except Exception as e:
        log_error("更新申根签证表单失败",
                  error=e,
                  request_id=request_id,
                  form_id=form_id)
        raise HTTPException(status_code=500, detail=f"更新表单失败: {str(e)}")


@app.delete("/api/schengen-forms/{form_id}", summary="删除申根签证表单", tags=["申根签证"])
def delete_schengen_form(
    form_id: str,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """删除申根签证表单"""
    request_id = generate_request_id()

    log_info("删除申根签证表单",
             request_id=request_id,
             form_id=form_id,
             user_id=payload.user_id,
             permission=payload.permission)

    try:
        with db_manager.get_db() as conn:
            with conn.cursor() as cur:
                # 根据权限检查表单是否存在
                if payload.permission not in ['kefu', 'admin']:
                    # 普通用户只能删除自己的表单
                    cur.execute("""
                        SELECT id FROM schengen_visa_applications
                        WHERE application_id = %s AND user_id = %s
                    """, (form_id, payload.user_id))

                    if not cur.fetchone():
                        raise HTTPException(status_code=404, detail="表单不存在")

                    # 删除表单（级联删除相关数据）
                    cur.execute("""
                        DELETE FROM schengen_visa_applications
                        WHERE application_id = %s AND user_id = %s
                    """, (form_id, payload.user_id))
                else:
                    # 管理员/客服可以删除所有表单
                    cur.execute("""
                        SELECT id FROM schengen_visa_applications
                        WHERE application_id = %s
                    """, (form_id,))

                    if not cur.fetchone():
                        raise HTTPException(status_code=404, detail="表单不存在")

                    # 删除表单（级联删除相关数据）
                    cur.execute("""
                        DELETE FROM schengen_visa_applications
                        WHERE application_id = %s
                    """, (form_id,))

                log_info("删除申根签证表单成功",
                         request_id=request_id,
                         form_id=form_id)

                return {
                    "code": 1,
                    "message": "success",
                    "data": None
                }

    except HTTPException:
        raise
    except Exception as e:
        log_error("删除申根签证表单失败",
                  error=e,
                  request_id=request_id,
                  form_id=form_id)
        raise HTTPException(status_code=500, detail=f"删除表单失败: {str(e)}")


def generate_schengen_html(form_data: dict) -> str:
    """生成申根签证表单HTML"""
    # 安全地获取数据，避免KeyError
    def safe_get(data, key, default=""):
        """安全获取字典值"""
        value = data.get(key, default)
        return str(value) if value is not None else default

    # 安全处理数组字段
    def safe_join(data, key, default=""):
        """安全连接数组字段"""
        value = data.get(key, [])
        if isinstance(value, list):
            return ', '.join(str(item) for item in value)
        return str(value) if value else default

    # 生成HTML内容
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>申根签证申请表</title>
        <style>
            body {{
                font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
                font-size: 12px;
                line-height: 1.4;
                margin: 20px;
                color: #333;
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 10px;
            }}
            .header h1 {{
                font-size: 24px;
                margin: 0;
                color: #2c3e50;
            }}
            .section {{
                margin-bottom: 25px;
                page-break-inside: avoid;
            }}
            .section-title {{
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
                padding: 8px 12px;
                background-color: #ecf0f1;
                border-left: 4px solid #3498db;
            }}
            .info-table {{
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 15px;
            }}
            .info-table th, .info-table td {{
                border: 1px solid #bdc3c7;
                padding: 8px 12px;
                text-align: left;
                vertical-align: top;
            }}
            .info-table th {{
                background-color: #ecf0f1;
                font-weight: bold;
                width: 30%;
            }}
            .info-table td {{
                background-color: #fff;
            }}
            .footer {{
                margin-top: 30px;
                text-align: center;
                font-size: 10px;
                color: #7f8c8d;
                border-top: 1px solid #bdc3c7;
                padding-top: 10px;
            }}
            @media print {{
                body {{ margin: 0; }}
                .section {{ page-break-inside: avoid; }}
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>申根签证申请表</h1>
            <p>Schengen Visa Application Form</p>
        </div>

        <!-- 第一步：基本资格信息 -->
        <div class="section">
            <div class="section-title">第一步：基本资格信息</div>
            <table class="info-table">
                <tr><th>申请人姓名</th><td>{safe_get(form_data, 'applicant_name')}</td></tr>
                <tr><th>姓（拼音）</th><td>{safe_get(form_data, 'surname')}</td></tr>
                <tr><th>名（拼音）</th><td>{safe_get(form_data, 'given_name')}</td></tr>
                <tr><th>性别</th><td>{'男' if safe_get(form_data, 'gender') == 'M' else '女'}</td></tr>
                <tr><th>出生日期</th><td>{safe_get(form_data, 'birth_date')}</td></tr>
                <tr><th>国籍</th><td>{safe_get(form_data, 'nationality')}</td></tr>
                <tr><th>护照号码</th><td>{safe_get(form_data, 'passport_number')}</td></tr>
                <tr><th>护照到期日期</th><td>{safe_get(form_data, 'passport_expire_date')}</td></tr>
            </table>
        </div>

        <!-- 第二步：护照详细信息 -->
        <div class="section">
            <div class="section-title">第二步：护照详细信息</div>
            <table class="info-table">
                <tr><th>护照姓氏</th><td>{safe_get(form_data, 'passport_surname')}</td></tr>
                <tr><th>出生时姓氏</th><td>{safe_get(form_data, 'birth_surname')}</td></tr>
                <tr><th>护照名字</th><td>{safe_get(form_data, 'passport_given_name')}</td></tr>
                <tr><th>护照出生日期</th><td>{safe_get(form_data, 'passport_birth_date')}</td></tr>
                <tr><th>出生国家</th><td>{safe_get(form_data, 'birth_country')}</td></tr>
                <tr><th>出生地点</th><td>{safe_get(form_data, 'birth_place')}</td></tr>
                <tr><th>现国籍</th><td>{safe_get(form_data, 'current_nationality')}</td></tr>
                <tr><th>出生时国籍</th><td>{safe_get(form_data, 'birth_nationality')}</td></tr>
                <tr><th>护照性别</th><td>{'男' if safe_get(form_data, 'passport_gender') == 'M' else '女'}</td></tr>
                <tr><th>婚姻状况</th><td>{safe_get(form_data, 'passport_marital_status')}</td></tr>
                <tr><th>是否未成年</th><td>{'是' if safe_get(form_data, 'is_minor') == 'yes' else '否'}</td></tr>
                <tr><th>护照类型</th><td>{safe_get(form_data, 'passport_type')}</td></tr>
                <tr><th>护照签发日期</th><td>{safe_get(form_data, 'passport_issue_date_detail')}</td></tr>
                <tr><th>签发国家</th><td>{safe_get(form_data, 'issuing_country')}</td></tr>
                <tr><th>护照到期日期</th><td>{safe_get(form_data, 'passport_expire_date_detail')}</td></tr>
                <tr><th>签发机关</th><td>{safe_get(form_data, 'issuing_authority')}</td></tr>
            </table>
        </div>

        <!-- 第三步：申请人信息 -->
        <div class="section">
            <div class="section-title">第三步：申请人信息</div>
            <table class="info-table">
                <tr><th>申请人居住国</th><td>{safe_get(form_data, 'applicant_country')}</td></tr>
                <tr><th>申请人地址</th><td>{safe_get(form_data, 'applicant_address')}</td></tr>
                <tr><th>职业</th><td>{safe_get(form_data, 'occupation_id')}</td></tr>
                <tr><th>其他职业信息</th><td>{safe_get(form_data, 'occupation_others')}</td></tr>
                <tr><th>邮箱</th><td>{safe_get(form_data, 'applicant_email')}</td></tr>
                <tr><th>电话</th><td>{safe_get(form_data, 'applicant_phone')}</td></tr>
                <tr><th>雇主名称</th><td>{safe_get(form_data, 'employer_name')}</td></tr>
                <tr><th>雇主地址</th><td>{safe_get(form_data, 'employer_address')}</td></tr>
                <tr><th>雇主电话</th><td>{safe_get(form_data, 'employer_phone')}</td></tr>
                <tr><th>雇主城市</th><td>{safe_get(form_data, 'employer_city')}</td></tr>
                <tr><th>雇主邮编</th><td>{safe_get(form_data, 'employer_postal_code')}</td></tr>
                <tr><th>雇主国家</th><td>{safe_get(form_data, 'employer_country')}</td></tr>
                <tr><th>是否采集过指纹</th><td>{'是' if safe_get(form_data, 'fingerprints_collected') == '1' else '否'}</td></tr>
                <tr><th>指纹采集日期</th><td>{safe_get(form_data, 'date_of_collection')}</td></tr>
                <tr><th>上次申请号</th><td>{safe_get(form_data, 'previous_application_number')}</td></tr>
            </table>
        </div>

        <!-- 第四步：旅行计划信息 -->
        <div class="section">
            <div class="section-title">第四步：旅行计划信息</div>
            <table class="info-table">
                <tr><th>旅行目的</th><td>{safe_get(form_data, 'purpose_of_travel')}</td></tr>
                <tr><th>其他旅行目的</th><td>{safe_get(form_data, 'purpose_of_travel_others')}</td></tr>
                <tr><th>停留目的补充信息</th><td>{safe_get(form_data, 'purpose_of_travel_add_info')}</td></tr>
                <tr><th>入境次数</th><td>{safe_get(form_data, 'number_of_entries')}</td></tr>
                <tr><th>是否有申根签证</th><td>{'是' if safe_get(form_data, 'is_schengen_visa_issued') == '1' else '否'}</td></tr>
                <tr><th>签证有效期从</th><td>{safe_get(form_data, 'valid_from')}</td></tr>
                <tr><th>签证有效期至</th><td>{safe_get(form_data, 'valid_till')}</td></tr>
                <tr><th>是否有旅行保险</th><td>{'是' if safe_get(form_data, 'is_adequate_medical_insurance') == '1' else '否'}</td></tr>
            </table>
        </div>

        <!-- 第五步：住宿安排信息 -->
        <div class="section">
            <div class="section-title">第五步：住宿安排信息</div>
            <table class="info-table">
                <tr><th>邀请方类型</th><td>{safe_get(form_data, 'inviting_party_type')}</td></tr>
                <tr><th>酒店名称</th><td>{safe_get(form_data, 'hotel_name')}</td></tr>
                <tr><th>酒店地址</th><td>{safe_get(form_data, 'hotel_address')}</td></tr>
                <tr><th>酒店邮编</th><td>{safe_get(form_data, 'hotel_postal_code')}</td></tr>
                <tr><th>酒店邮箱</th><td>{safe_get(form_data, 'hotel_email')}</td></tr>
                <tr><th>酒店电话</th><td>{safe_get(form_data, 'hotel_phone')}</td></tr>
                <tr><th>邀请人姓氏</th><td>{safe_get(form_data, 'inviting_person_surname')}</td></tr>
                <tr><th>邀请人名字</th><td>{safe_get(form_data, 'inviting_person_given_name')}</td></tr>
                <tr><th>邀请人地址</th><td>{safe_get(form_data, 'inviting_person_address')}</td></tr>
                <tr><th>企业名称</th><td>{safe_get(form_data, 'enterprise_name')}</td></tr>
                <tr><th>企业地址</th><td>{safe_get(form_data, 'enterprise_address')}</td></tr>
                <tr><th>机构名称</th><td>{safe_get(form_data, 'organisation_name')}</td></tr>
                <tr><th>机构地址</th><td>{safe_get(form_data, 'organisation_address')}</td></tr>
            </table>
        </div>

        <!-- 第六步：附加信息及确认 -->
        <div class="section">
            <div class="section-title">第六步：附加信息及确认</div>
            <table class="info-table">
                <tr><th>是否过境签证</th><td>{'是' if safe_get(form_data, 'is_transit_visa') == '1' else '否'}</td></tr>
                <tr><th>到达日期</th><td>{safe_get(form_data, 'arrival_date')}</td></tr>
                <tr><th>离开日期</th><td>{safe_get(form_data, 'departure_date')}</td></tr>
                <tr><th>停留天数</th><td>{safe_get(form_data, 'duration_of_stay')}</td></tr>
                <tr><th>费用承担方</th><td>{safe_join(form_data, 'cost_covered_by')}</td></tr>
                <tr><th>其他费用承担方</th><td>{safe_get(form_data, 'cost_covered_by_others')}</td></tr>
                <tr><th>支付方式</th><td>{safe_join(form_data, 'means_of_support')}</td></tr>
                <tr><th>其他支付方式</th><td>{safe_get(form_data, 'means_of_support_others')}</td></tr>
                <tr><th>是否欧盟公民家庭成员</th><td>{'是' if safe_get(form_data, 'is_eu_citizen_family') == '1' else '否'}</td></tr>
                <tr><th>欧盟公民姓氏</th><td>{safe_get(form_data, 'eu_citizen_surname')}</td></tr>
                <tr><th>欧盟公民名字</th><td>{safe_get(form_data, 'eu_citizen_given_name')}</td></tr>
                <tr><th>欧盟公民国籍</th><td>{safe_get(form_data, 'eu_citizen_nationality')}</td></tr>
                <tr><th>欧盟公民出生日期</th><td>{safe_get(form_data, 'eu_citizen_birth_date')}</td></tr>
                <tr><th>欧盟公民护照号</th><td>{safe_get(form_data, 'eu_citizen_passport_number')}</td></tr>
                <tr><th>与欧盟公民关系</th><td>{safe_get(form_data, 'eu_citizen_relationship')}</td></tr>
                <tr><th>首入申根国</th><td>{safe_get(form_data, 'first_entry_schengen')}</td></tr>
                <tr><th>目的地国家</th><td>{safe_join(form_data, 'destination_countries')}</td></tr>
                <tr><th>邀请人支付费用</th><td>{'是' if safe_get(form_data, 'inviting_person_covered_costs') == '1' else '否'}</td></tr>
            </table>
        </div>

        <!-- 申请状态信息 -->
        <div class="section">
            <div class="section-title">申请状态信息</div>
            <table class="info-table">
                <tr><th>申请ID</th><td>{safe_get(form_data, 'application_id')}</td></tr>
                <tr><th>申请状态</th><td>{'草稿' if safe_get(form_data, 'status') == 'draft' else '已提交' if safe_get(form_data, 'status') == 'submitted' else '已完成'}</td></tr>
                <tr><th>创建时间</th><td>{safe_get(form_data, 'created_at')}</td></tr>
                <tr><th>更新时间</th><td>{safe_get(form_data, 'updated_at')}</td></tr>
            </table>
        </div>

        <div class="footer">
            <p>申请ID: {safe_get(form_data, 'application_id')} |
               状态: {'草稿' if safe_get(form_data, 'status') == 'draft' else '已提交' if safe_get(form_data, 'status') == 'submitted' else '已完成'} |
               生成时间: {safe_get(form_data, 'created_at')}</p>
        </div>
    </body>
    </html>
    """

    return html_content


def generate_schengen_pdf(form_data: dict) -> str:
    """生成申根签证表单PDF（使用HTML转换）"""
    try:
        import tempfile
        import os

        # 生成HTML内容
        html_content = generate_schengen_html(form_data)

        # 创建临时HTML文件
        temp_dir = tempfile.gettempdir()
        applicant_name = form_data.get("applicant_name", "Unknown")
        safe_name = applicant_name.replace(" ", "_").replace("/", "_").replace("\\", "_") if applicant_name != "Unknown" else "Unknown"

        html_filename = f"schengen_form_{safe_name}_{form_data.get('id', 'unknown')}.html"
        html_path = os.path.join(temp_dir, html_filename)

        # 写入HTML文件
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return html_path

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"HTML生成失败: {str(e)}")


@app.get("/api/schengen-forms/{form_id}/download", summary="下载申根签证表单", tags=["申根签证"])
def download_schengen_form(
    form_id: str,
    payload: UserPayload = Depends(verify_jwt_token)
):
    """下载申根签证表单（优先PDF，降级为JSON）"""
    request_id = generate_request_id()

    log_info("下载申根签证表单",
             request_id=request_id,
             form_id=form_id,
             user_id=payload.user_id)

    try:
        # 获取完整的表单数据
        form_response = get_schengen_form(form_id, payload)

        if form_response["code"] != 1:
            raise HTTPException(status_code=404, detail="表单不存在")

        form_data = form_response["data"]

        # 尝试生成HTML文件，如果失败则降级为JSON
        try:
            html_path = generate_schengen_pdf(form_data)  # 实际上生成的是HTML

            # 构建HTML文件名
            applicant_name = form_data.get("applicant_name", "Unknown")
            safe_name = applicant_name.replace(" ", "_").replace("/", "_").replace("\\", "_") if applicant_name != "Unknown" else "Unknown"
            filename = f"schengen_form_{safe_name}_{form_id}.html"

            # 对文件名进行URL编码以支持中文
            import urllib.parse
            encoded_filename = urllib.parse.quote(f"申根签证表单_{applicant_name}_{form_id}.html")

            log_info("下载申根签证表单HTML成功",
                     request_id=request_id,
                     form_id=form_id,
                     filename=filename)

            # 返回HTML文件
            return FileResponse(
                path=html_path,
                filename=filename,
                media_type='text/html',
                headers={
                    "Content-Disposition": f"attachment; filename={filename}; filename*=UTF-8''{encoded_filename}"
                }
            )

        except Exception as pdf_error:
            log_error("PDF生成失败，降级为JSON下载",
                      error=pdf_error,
                      request_id=request_id,
                      form_id=form_id)

            # 降级为JSON下载
            import tempfile
            import os

            temp_dir = tempfile.gettempdir()
            applicant_name = form_data.get("applicant_name", "Unknown")
            safe_name = applicant_name.replace(" ", "_").replace("/", "_").replace("\\", "_") if applicant_name != "Unknown" else "Unknown"
            filename = f"schengen_form_{safe_name}_{form_id}.json"
            file_path = os.path.join(temp_dir, filename)

            # 写入JSON数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(form_data, f, ensure_ascii=False, indent=2)

            # 对文件名进行URL编码以支持中文
            import urllib.parse
            encoded_filename = urllib.parse.quote(f"申根签证表单_{applicant_name}_{form_id}.json")

            log_info("下载申根签证表单JSON成功",
                     request_id=request_id,
                     form_id=form_id,
                     filename=filename)

            # 返回JSON文件
            return FileResponse(
                path=file_path,
                filename=filename,
                media_type='application/json',
                headers={
                    "Content-Disposition": f"attachment; filename={filename}; filename*=UTF-8''{encoded_filename}"
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        log_error("下载申根签证表单失败",
                  error=e,
                  request_id=request_id,
                  form_id=form_id)
        raise HTTPException(status_code=500, detail=f"下载表单失败: {str(e)}")


def fix_pdf_chinese_fonts(pdf_base64):
    """
    尝试修复PDF中的中文字体显示问题
    这是一个简化的解决方案，主要是添加字体嵌入提示
    """
    try:
        # 解码base64
        pdf_bytes = base64.b64decode(pdf_base64)

        # 创建PdfReader对象
        pdf_reader = PdfReader(io.BytesIO(pdf_bytes))
        pdf_writer = PdfWriter()

        # 复制所有页面到新的PDF
        for page in pdf_reader.pages:
            pdf_writer.add_page(page)

        # 添加元数据，提示字体信息
        pdf_writer.add_metadata({
            '/Title': 'VAF Form with Chinese Font Support',
            '/Creator': 'VFS Global with Font Fix',
            '/Producer': 'Python PDF Processor'
        })

        # 输出新的PDF
        output_buffer = io.BytesIO()
        pdf_writer.write(output_buffer)
        output_buffer.seek(0)

        # 转换回base64
        fixed_pdf_bytes = output_buffer.getvalue()
        fixed_pdf_base64 = base64.b64encode(fixed_pdf_bytes).decode('utf-8')

        return fixed_pdf_base64

    except Exception as e:
        print(f"PDF字体修复过程中出错: {str(e)}")
        # 如果处理失败，返回原始PDF
        return pdf_base64


def download_vaf_form_sync(request_data: dict, headers: dict, proxy_config: dict, max_retries: int = 3):
    """同步下载VAF表单，使用curl_cffi"""
    try:
        from curl_cffi import requests as cf_requests
    except ImportError:
        # 如果curl_cffi不可用，使用普通的requests
        cf_requests = requests

    retry_delay = 1
    last_error = None

    for attempt in range(max_retries):
        try:
            # 调用VFS接口
            response = cf_requests.post(
                "https://lift-apicn.vfsglobal.com/appointment/downloadvafform",
                json=request_data,
                headers=headers,
                proxies=proxy_config,
                timeout=130,
                impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
                verify=False,
            )

            if response.status_code == 200:
                response_data = response.json()

                # 检查响应格式
                if 'error' in response_data and response_data['error'] is not None:
                    error_msg = f"VFS接口返回错误: {response_data['error']}"
                    if attempt < max_retries - 1:
                        print(f"VFS接口调用失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                        time.sleep(retry_delay * (attempt + 1))
                        continue
                    else:
                        raise Exception(error_msg)

                if 'pdfdata' not in response_data:
                    error_msg = "VFS接口返回数据格式错误"
                    if attempt < max_retries - 1:
                        print(f"VFS接口调用失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                        time.sleep(retry_delay * (attempt + 1))
                        continue
                    else:
                        raise Exception(error_msg)

                # 成功获取数据
                return {"success": True, "data": response_data}

            else:
                error_msg = f"VFS接口调用失败，状态码: {response.status_code}"
                if attempt < max_retries - 1:
                    print(f"VFS接口调用失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                else:
                    raise Exception(error_msg)

        except Exception as e:
            error_msg = f"请求异常: {str(e)}"
            if attempt < max_retries - 1:
                print(f"VFS接口调用异常 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                time.sleep(retry_delay * (attempt + 1))
                continue
            else:
                raise Exception(f"{error_msg}，已重试{max_retries}次")

    # 如果所有重试都失败了
    raise Exception(f"下载失败，已重试{max_retries}次")


async def download_vaf_form_async(task_id: str, matching_data: dict, account: dict):
    """异步下载VAF表单的后台任务"""
    try:
        # 更新任务状态为处理中
        vaf_download_tasks[task_id]["status"] = "processing"
        vaf_download_tasks[task_id]["message"] = "正在下载表单..."

        # 提取必要的参数
        passport_no = matching_data.get('passportNO')
        center_code = matching_data.get('centerCode')
        mission_code = matching_data.get('missionCode')
        login_user = matching_data.get('loginUser')
        urn = matching_data.get('arn')
        if not urn:
            urn = f"{matching_data.get('urn')}/1"
        form_id = matching_data.get('formId')

        # 构建请求参数
        request_data = {
            "countryCode": "chn",
            "missionCode": mission_code,
            "loginUser": account.get('email'),
            "lOGinUsEr": login_user,
            "centerCode": center_code,
            "aurn": urn
        }
        print(request_data)

        # 构建请求headers
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }

        # 配置代理
        proxy_config = {
            "http": "http://t15117693479940:<EMAIL>:15818",
            "https": "http://t15117693479940:<EMAIL>:15818",
        }

        # 使用线程池执行curl_cffi请求
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            thread_pool,
            download_vaf_form_sync,
            request_data,
            headers,
            proxy_config
        )

        if result["success"]:
            response_data = result["data"]

            # 成功获取数据
            vaf_download_tasks[task_id]["status"] = "completed"
            vaf_download_tasks[task_id]["message"] = "下载成功"
            vaf_download_tasks[task_id]["data"] = {
                "pdfdata": response_data['pdfdata'],
                "form_id": form_id,
                "passport_no": passport_no
            }
        else:
            raise Exception("下载失败")

    except Exception as e:
        # 更新任务状态为失败
        vaf_download_tasks[task_id]["status"] = "failed"
        vaf_download_tasks[task_id]["message"] = str(e)
        print(f"VAF表单下载任务失败 (task_id: {task_id}): {str(e)}")


@app.post("/api/download_vaf_form", summary="下载VAF表单", tags=["VAF表单"])
async def download_vaf_form(data: dict, background_tasks: BackgroundTasks, payload: UserPayload = Depends(verify_jwt_token)):
    """下载VAF表单 - 异步非阻塞版本"""
    print(f"[DEBUG] download_vaf_form started at {time.time()}")
    try:
        form_id = data.get("form_id")
        if not form_id:
            return {"code": 0, "message": "缺少表单ID"}

        print(f"[DEBUG] Processing form_id: {form_id}")

        # 生成任务ID
        task_id = str(uuid.uuid4())
        print(f"[DEBUG] Generated task_id: {task_id}")

        # 初始化任务状态
        vaf_download_tasks[task_id] = {
            "status": "pending",
            "message": "任务已创建，正在准备...",
            "data": None,
            "created_at": time.time()
        }

        print(f"[DEBUG] Starting to fetch Redis data at {time.time()}")

        # 将Redis操作移到异步执行，避免阻塞
        try:
            # 第一步：从redis_client.remote获取successUserDatas全量数据，找出formId相等的数据
            loop = asyncio.get_event_loop()
            success_user_datas = redis_client_local.hgetall('successUserDatas')
            print(f"[DEBUG] Redis data fetched, size: {len(success_user_datas)} at {time.time()}")
        except Exception as e:
            print(f"[ERROR] Failed to fetch Redis data: {str(e)}")
            vaf_download_tasks[task_id]["status"] = "failed"
            vaf_download_tasks[task_id]["message"] = f"获取Redis数据失败: {str(e)}"
            return {"code": 0, "message": f"获取Redis数据失败: {str(e)}"}

        matching_data = None
        for data_dict in success_user_datas:
            try:
                if data_dict.get('child') != None and len(data_dict.get('child')) > 0:
                    for child in data_dict.get('child'):
                        if child.get('formId') == form_id:
                            child['loginUser'] = data_dict.get('loginUser')
                            child['centerCode'] = data_dict.get('centerCode')
                            child['missionCode'] = data_dict.get('missionCode')
                            child['formId'] = form_id  # 确保formId存在
                            matching_data = child
                            break
                elif data_dict.get('formId') == form_id:
                    matching_data = data_dict
                    break
            except json.JSONDecodeError:
                continue

        if not matching_data:
            vaf_download_tasks[task_id]["status"] = "failed"
            vaf_download_tasks[task_id]["message"] = "未找到匹配的表单数据"
            return {"code": 0, "message": "未找到匹配的表单数据"}

        # 第二步：提取数据的护照号passportNO/centerCode/missionCode/loginUser/urn
        passport_no = matching_data.get('passportNO')
        center_code = matching_data.get('centerCode')
        mission_code = matching_data.get('missionCode')
        login_user = matching_data.get('loginUser')
        urn = matching_data.get('arn')
        if not urn:
            urn = f"{matching_data.get('urn')}/1"

        if not all([passport_no, center_code, mission_code, login_user, urn]):
            print("表单数据不完整", matching_data)
            vaf_download_tasks[task_id]["status"] = "failed"
            vaf_download_tasks[task_id]["message"] = "表单数据不完整"
            return {"code": 0, "message": "表单数据不完整"}

        # 从Redis获取account数据
        account_key = f"{mission_code}LoginUser"
        print(f"[DEBUG] Fetching account data for key: {account_key}")

        try:
            account_hash = redis_client_local.hgetall(account_key)
            print(f"[DEBUG] Account data fetched, size: {len(account_hash)}")
        except Exception as e:
            print(f"[ERROR] Failed to fetch account data: {str(e)}")
            vaf_download_tasks[task_id]["status"] = "failed"
            vaf_download_tasks[task_id]["message"] = f"获取账户数据失败: {str(e)}"
            return {"code": 0, "message": f"获取账户数据失败: {str(e)}"}

        if not account_hash:
            vaf_download_tasks[task_id]["status"] = "failed"
            vaf_download_tasks[task_id]["message"] = f"未找到{mission_code}的登录用户数据"
            return {"code": 0, "message": f"未找到{mission_code}的登录用户数据"}

        try:
            # 从hash中获取所有账户数据
            account_list = []
            for account_data in account_hash:
                account_list.append(account_data)

            if not account_list:
                vaf_download_tasks[task_id]["status"] = "failed"
                vaf_download_tasks[task_id]["message"] = f"{mission_code}登录用户数据为空"
                return {"code": 0, "message": f"{mission_code}登录用户数据为空"}

            # 随机选择一个account
            account = random.choice(account_list)
            if not account.get("token"):
                vaf_download_tasks[task_id]["status"] = "failed"
                vaf_download_tasks[task_id]["message"] = "选中的账户缺少token信息"
                return {"code": 0, "message": "选中的账户缺少token信息"}

        except json.JSONDecodeError:
            vaf_download_tasks[task_id]["status"] = "failed"
            vaf_download_tasks[task_id]["message"] = f"{mission_code}登录用户数据JSON解析失败"
            return {"code": 0, "message": f"{mission_code}登录用户数据JSON解析失败"}

        # 启动后台异步下载任务
        background_tasks.add_task(download_vaf_form_async, task_id, matching_data, account)

        # 立即返回任务ID
        return {
            "code": 1,
            "message": "下载任务已启动，请使用task_id查询进度",
            "data": {
                "task_id": task_id,
                "status": "pending"
            }
        }

    except Exception as e:
        print(f"启动VAF表单下载任务失败: {str(e)}")
        return {"code": 0, "message": f"启动下载任务失败: {str(e)}"}


@app.get("/api/download_vaf_form_status/{task_id}", summary="查询VAF表单下载状态", tags=["VAF表单"])
async def get_vaf_download_status(task_id: str, payload: UserPayload = Depends(verify_jwt_token)):
    """查询VAF表单下载任务状态"""
    try:
        if task_id not in vaf_download_tasks:
            return {"code": 0, "message": "任务不存在"}

        task_info = vaf_download_tasks[task_id]

        # 返回任务状态
        response_data = {
            "code": 1,
            "data": {
                "task_id": task_id,
                "status": task_info["status"],  # pending, processing, completed, failed
                "message": task_info["message"],
                "created_at": task_info["created_at"]
            }
        }

        # 如果任务完成，返回数据
        if task_info["status"] == "completed" and task_info["data"]:
            response_data["data"]["result"] = task_info["data"]

        # 清理超过1小时的已完成或失败任务
        current_time = time.time()
        if (task_info["status"] in ["completed", "failed"] and
                current_time - task_info["created_at"] > 3600):  # 1小时
            del vaf_download_tasks[task_id]

        return response_data

    except Exception as e:
        print(f"查询VAF下载状态失败: {str(e)}")
        return {"code": 0, "message": f"查询状态失败: {str(e)}"}


@app.post("/api/download_appointment_with_filename", summary="启动预约信PDF异步下载", tags=["预约信"])
async def download_appointment_with_filename(data: dict, background_tasks: BackgroundTasks, payload: UserPayload = Depends(verify_jwt_token)):
    """
    启动预约信PDF异步下载任务
    """
    try:
        order_id = data.get("order_id")
        pdf_url = data.get("pdf_url")
        filename = data.get("filename", "appointment.pdf")

        if not order_id or not pdf_url:
            return {"code": 0, "message": "缺少必要参数"}

        # 检查订单权限
        with db_manager.get_db() as conn:
            cur = conn.cursor()
            if payload.permission not in ("admin", "kefu"):
                cur.execute("""
                    SELECT operator FROM orders
                    WHERE order_id = %s AND order_status != 'deleted'
                """, (order_id,))
                order = cur.fetchone()
                if not order or order[0] != payload.user_id:
                    return {"code": 0, "message": "无权限访问该订单"}

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 初始化任务状态
        appointment_download_tasks[task_id] = {
            "status": "pending",
            "message": "任务已创建，正在准备下载...",
            "created_at": time.time(),
            "data": None,
            "order_id": order_id,
            "filename": filename
        }

        # 启动后台异步下载任务
        background_tasks.add_task(download_appointment_pdf_async, task_id, pdf_url, filename)

        # 立即返回任务ID
        return {
            "code": 1,
            "message": "下载任务已启动，请使用task_id查询进度",
            "data": {
                "task_id": task_id,
                "status": "pending"
            }
        }

    except Exception as e:
        logger.error(f"启动预约信下载任务失败: {str(e)}")
        return {"code": 0, "message": f"启动任务失败: {str(e)}"}


async def download_appointment_pdf_async(task_id: str, pdf_url: str, filename: str):
    """异步下载预约信PDF的后台任务"""
    try:
        # 更新任务状态为处理中
        appointment_download_tasks[task_id]["status"] = "processing"
        appointment_download_tasks[task_id]["message"] = "正在下载PDF文件..."

        # 使用线程池执行同步下载操作
        loop = asyncio.get_event_loop()
        pdf_content = await loop.run_in_executor(
            thread_pool,
            download_appointment_pdf_sync,
            pdf_url
        )

        if pdf_content:
            # 下载成功
            appointment_download_tasks[task_id]["status"] = "completed"
            appointment_download_tasks[task_id]["message"] = "下载完成"
            appointment_download_tasks[task_id]["data"] = {
                "pdf_content": base64.b64encode(pdf_content).decode('utf-8'),
                "filename": filename
            }
        else:
            # 下载失败
            appointment_download_tasks[task_id]["status"] = "failed"
            appointment_download_tasks[task_id]["message"] = "PDF下载失败"

    except Exception as e:
        print(f"异步下载预约信失败: {str(e)}")
        appointment_download_tasks[task_id]["status"] = "failed"
        appointment_download_tasks[task_id]["message"] = f"下载失败: {str(e)}"


def download_appointment_pdf_sync(pdf_url: str) -> bytes:
    """同步下载预约信PDF文件"""
    try:
        import curl_cffi.requests as cf_requests

        proxy_config = {
            "http": "http://t15117693479940:<EMAIL>:15818",
            "https": "http://t15117693479940:<EMAIL>:15818",
        }

        # 下载PDF文件
        response = cf_requests.get(
            pdf_url,
            timeout=30,
            impersonate="chrome136",
            proxies=proxy_config,
            verify=False,
        )

        if response.status_code == 200:
            return response.content
        else:
            print(f"PDF下载失败，状态码: {response.status_code}")
            return None

    except Exception as e:
        print(f"同步下载预约信失败: {str(e)}")
        return None


@app.get("/api/download_appointment_status/{task_id}", summary="查询预约信PDF下载状态", tags=["预约信"])
async def get_appointment_download_status(task_id: str, payload: UserPayload = Depends(verify_jwt_token)):
    """查询预约信PDF下载任务状态"""
    try:
        if task_id not in appointment_download_tasks:
            return {"code": 0, "message": "任务不存在"}

        task_info = appointment_download_tasks[task_id]

        # 返回任务状态
        response_data = {
            "code": 1,
            "data": {
                "task_id": task_id,
                "status": task_info["status"],  # pending, processing, completed, failed
                "message": task_info["message"],
                "created_at": task_info["created_at"]
            }
        }

        # 如果任务完成，返回数据
        if task_info["status"] == "completed" and task_info["data"]:
            response_data["data"]["result"] = task_info["data"]

        # 清理超过1小时的已完成或失败任务
        current_time = time.time()
        if (task_info["status"] in ["completed", "failed"] and
                current_time - task_info["created_at"] > 3600):  # 1小时
            del appointment_download_tasks[task_id]

        return response_data

    except Exception as e:
        print(f"查询预约信下载状态失败: {str(e)}")
        return {"code": 0, "message": f"查询状态失败: {str(e)}"}


# ==================== 启动应用 ====================
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5005)
