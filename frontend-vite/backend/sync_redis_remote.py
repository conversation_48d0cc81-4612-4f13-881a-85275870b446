import time
from RedisClient import RedisClient
redis_client_local = RedisClient()
redis_client_remote = RedisClient(host='************', port=6379, password='TicketsCache#2023', db=0)


while True:
    # successUserDatas
    successUserDatas = redis_client_remote.client.hgetall("successUserDatas")
    print(f"successUserDatas remote_count={len(successUserDatas)}")
    redis_client_local.delete("successUserDatas")
    if successUserDatas:
        for key, value in successUserDatas.items():
            redis_client_local.hset("successUserDatas", key, value)
    local_count = redis_client_local.client.hlen("successUserDatas")
    print(f"successUserDatas local_count={local_count}")
    print("successUserDatas同步完成")

    # vfs_calendar
    vfs_calendar = redis_client_remote.client.hgetall("vfs_calendar")
    print(f"vfs_calendar remote_count={len(vfs_calendar)}")
    redis_client_local.delete("vfs_calendar")
    if vfs_calendar:
        for key, value in vfs_calendar.items():
            redis_client_local.hset("vfs_calendar", key, value)
    local_count = redis_client_local.client.hlen("vfs_calendar")
    print(f"vfs_calendar local_count={local_count}")
    print("vfs_calendar同步完成")

    # cheLoginUser
    cheLoginUser = redis_client_remote.client.hgetall("cheLoginUser")
    print(f"cheLoginUser remote_count={len(cheLoginUser)}")
    redis_client_local.delete("cheLoginUser")
    if cheLoginUser:
        for key, value in cheLoginUser.items():
            redis_client_local.hset("cheLoginUser", key, value)
    local_count = redis_client_local.client.hlen("cheLoginUser")
    print(f"cheLoginUser local_count={local_count}")
    print("cheLoginUser同步完成")

    time.sleep(60)
