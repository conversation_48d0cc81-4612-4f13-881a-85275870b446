wx_hook_urls = [
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=290b6096-f038-4b36-a17a-bd999b4df63f",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=aaec6551-d994-43ff-b9a9-3ded45a6a57e",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9c9f5f17-80f4-4307-9e5d-3307119e14f2",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=425c5aed-ce49-46b9-8622-79bfd31231d2",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fc171c94-5512-45f4-b955-80156a877e25",
]

APIKEY = "a397a7c92a6340e29eafe05e4f5af211"  # 环境变量 CB_APIKEY
# captcha v2 目标参数：

websiteKey_v2 = "6LcDpXcfAAAAAM7wOEsF_38DNsL20tTvPTKxpyn0"
websiteKey_v3 = "6LcTpXcfAAAAAM3VojNhyV-F1z92ADJIvcSZ39Y9" # V3 普通版本

# captcha v2 目标参数：
websiteURL = "https://www.google.com/recaptcha/api2/demo"
websiteURL_v3 = "https://fr.tlscontact.com/"
# yescaptcha key
CAPTCHA_KEY = "5eb8d30d05a90b906062c792b171b87ef636be9226751"


# 北京 广州  长沙 成都 重庆 福州  杭州  济南 昆明  南京  沈阳  深圳  武汉  西安
# 1PE 1CB 1CS  1CA  1CQ  1FU  1HN  1JN 1KU  1NA  1SY  1SZ 1WU  1XI

submissionMap = {
    "SHANGHAI": "1SH",
    "BEIJING": "1PE",
    "GUANGZHOU": "1CB",
    "CHANGSHA": "1CS",
    "CHENGDU": "1CA",
    "CHONGQING": "1CQ",
    "FUZHOU": "1FU",
    "HANGZHOU": "1HN",
    "JINAN": "1JN",
    "KUNMING": "1KU",
    "NANJING": "1NA",
    "SHENYANG": "1SY",
    "SHENZHEN": "1SZ",
    "WUHAN": "1WU",
    "XIAN": "1XI",
}
issuerMap = {
    "SHANGHAI": "cnSHA2fr",
    "BEIJING": "cnBJS2fr",
    "GUANGZHOU": "cnCAN2fr",
    "CHANGSHA": "cnCSX2fr",
    "CHENGDU": "cnCNG2fr",
    "CHONGQING": "cnCKG2fr",
    "FUZHOU": "cnFOC2fr",
    "HANGZHOU": "cnHGH2fr",
    "JINAN": "cnTNA2fr",
    "KUNMING": "cnKMG2fr",
    "NANJING": "cnNKG2fr",
    "SHENYANG": "cnSHE2fr",
    "SHENZHEN": "cnSZX2fr",
    "WUHAN": "cnWUH2fr",
    "XIAN": "cnXIY2fr",
}

wx_hook_urls = [
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=290b6096-f038-4b36-a17a-bd999b4df63f",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=aaec6551-d994-43ff-b9a9-3ded45a6a57e",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9c9f5f17-80f4-4307-9e5d-3307119e14f2",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=425c5aed-ce49-46b9-8622-79bfd31231d2",
    "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fc171c94-5512-45f4-b955-80156a877e25",
]


step1_dict = [
    {
        "jakarta.faces.source": "formStep1:remoteRefreshFocus",
        "jakarta.faces.partial.execute": "formStep1:remoteRefreshFocus",
        "formStep1:remoteRefreshFocus": "formStep1:remoteRefreshFocus",
    },
    {
        "jakarta.faces.source": "formHeader:navigationLanguage",
        "jakarta.faces.partial.execute": "formHeader:navigationLanguage",
        "jakarta.faces.partial.render": "headerContent bodyContent footerContent",
        "formHeader": "formHeader",
        "formHeader:navigationLanguage_input": "en",
        "jakarta.faces.behavior.event": "change",
        "jakarta.faces.partial.event": "change",
    },
    {
        "jakarta.faces.source": "formStep1:visas-selected-nationality",
        "jakarta.faces.partial.execute": "formStep1:visas-selected-nationality",
        "jakarta.faces.partial.render": "formStep1:Visas-selected-authority formStep1:Visas-dde-travel-document formStep1:Visas-dde-travel-document-number formStep1:Visas-selected-purposeCategory formStep1:Visas-selected-purpose formStep1:Visas-selected-stayDuration-familleUE-infoTypeVisas formStep1:mdrResponsePanel formStep1:panelButton",
        "jakarta.faces.behavior.event": "change",
        "jakarta.faces.partial.event": "change",
    },
    {
        "jakarta.faces.source": "formStep1:Visas-selected-stayDuration",
        "jakarta.faces.partial.execute": "formStep1:Visas-selected-stayDuration formStep1:hasNationalFamily",
        "jakarta.faces.partial.render": "formStep1:idPanelDestination formStep1:idPanelFinalDestination formStep1:fragment-deposit-country-worldmap formStep1:Visas-selected-deposit-country formStep1:Visas-selected-deposit-town formStep1:TA_titreSejour formStep1:TA_FinalDestinationPermit formStep1:Visas-selected-authority formStep1:Visas-dde-travel-document formStep1:Visas-selected-purposeCategory formStep1:Visas-selected-purpose formStep1:purposeComplement formStep1:mdrResponsePanel formStep1:panelButton formStep1:idPanelModal formStep1:TA_titreSejourselect formStep1:visas-label-is-educated-minor",
        "jakarta.faces.behavior.event": "change",
        "jakarta.faces.partial.event": "change",
    },
    {
        "jakarta.faces.source": "formStep1:Visas-selected-deposit-country",
        "jakarta.faces.partial.execute": "formStep1:Visas-selected-deposit-country",
        "jakarta.faces.partial.render": "formStep1:Visas-selected-deposit-town formStep1:Visas-selected-stayDuration formStep1:idPanelDestination formStep1:Visas-selected-finalDestination formStep1:Visas-selected-authority formStep1:Visas-dde-travel-document formStep1:Visas-selected-purposeCategory formStep1:Visas-selected-purpose formStep1:fragment-deposit-country-worldmap formStep1:panelButton formStep1:depositCountryOpenPanel formStep1:mdrResponsePanel formStep1:idPanelModal",
        "jakarta.faces.behavior.event": "change",
        "jakarta.faces.partial.event": "change",
    },
    {
        "jakarta.faces.source": "formStep1:Visas-selected-destination",
        "jakarta.faces.partial.execute": "formStep1:Visas-selected-destination",
        "jakarta.faces.partial.render": "formStep1:Visas-selected-destination formStep1:Visas-selected-authority formStep1:Visas-selected-deposit-town formStep1:Visas-dde-travel-document formStep1:panelButton formStep1:idPanelModal formStep1:purposeComplement formStep1:depositCountryOpenPanel formStep1:mdrResponsePanel formStep1:Visa-transit-page-message-DDE001_MESS29 formStep1:Visas-selected-purposeCategory formStep1:Visas-selected-purpose",
        "jakarta.faces.behavior.event": "change",
        "jakarta.faces.partial.event": "change",
    },
    {
        "jakarta.faces.source": "formStep1:Visas-selected-deposit-town",
        "jakarta.faces.partial.execute": "formStep1:Visas-selected-deposit-town",
        "jakarta.faces.partial.render": "formStep1:Visas-selected-stayDuration formStep1:idPanelDestination formStep1:Visas-selected-finalDestination formStep1:Visas-selected-authority formStep1:fragment-deposit-country-worldmap formStep1:Visas-dde-travel-document formStep1:Visas-selected-purposeCategory formStep1:Visas-selected-purpose formStep1:idPanelModal formStep1:mdrResponsePanel formStep1:panelButton",
        "jakarta.faces.behavior.event": "change",
        "jakarta.faces.partial.event": "change",
    },
    {
        "jakarta.faces.source": "formStep1:Visas-dde-travel-document",
        "jakarta.faces.partial.execute": "formStep1:Visas-dde-travel-document formStep1:visas-selected-nationality formStep1:hasNationalFamily",
        "jakarta.faces.partial.render": "formStep1:Visas-selected-purposeCategory formStep1:Visas-selected-purpose formStep1:mdrResponsePanel formStep1:panelButton",
        "jakarta.faces.behavior.event": "change",
        "jakarta.faces.partial.event": "change",
    },
    {
        "jakarta.faces.source": "formStep1:Visas-dde-travel-document-number",
        "jakarta.faces.partial.execute": "formStep1:Visas-dde-travel-document-number",
        "jakarta.faces.behavior.event": "change",
        "jakarta.faces.partial.event": "change",
    },
    {
        "jakarta.faces.source": "formStep1:Visas-selected-purposeCategory",
        "jakarta.faces.partial.execute": "formStep1:Visas-selected-purposeCategory formStep1:hasNationalFamily",
        "jakarta.faces.partial.render": "formStep1:purposeCodeHasNationalFamily",
        "jakarta.faces.behavior.event": "focus",
        "jakarta.faces.partial.event": "focus",
    },
    {
        "jakarta.faces.source": "formStep1:Visas-selected-purposeCategory",
        "jakarta.faces.partial.execute": "formStep1:Visas-selected-purposeCategory",
        "jakarta.faces.partial.render": "formStep1:purposeCodeHasNationalFamily",
        "jakarta.faces.behavior.event": "blur",
        "jakarta.faces.partial.event": "blur",
    },
    {
        "jakarta.faces.source": "formStep1:Visas-selected-purposeCategory",
        "jakarta.faces.partial.execute": "formStep1:Visas-selected-purposeCategory",
        "jakarta.faces.partial.render": "formStep1:Visas-selected-purpose formStep1:mdrResponsePanel formStep1:panelButton formStep1:visas-label-is-educated-minor",
        "jakarta.faces.behavior.event": "change",
        "jakarta.faces.partial.event": "change",
    },
    {
        "jakarta.faces.source": "formStep1:Visas-selected-purpose",
        "jakarta.faces.partial.execute": "formStep1:Visas-selected-purpose",
        "jakarta.faces.partial.render": "formStep1:mdrResponsePanel formStep1:panelButton formStep1:visas-label-is-educated-minor",
        "jakarta.faces.behavior.event": "change",
        "jakarta.faces.partial.event": "change",
    },
    {
        "jakarta.faces.source": "formStep1:btnVerifier",
        "jakarta.faces.partial.execute": "@all",
        "jakarta.faces.partial.render": "formStep1",
        "formStep1:btnVerifier": "formStep1:btnVerifier",
    },
    {
        "jakarta.faces.source": "formStep1:remoteRefreshFocus",
        "jakarta.faces.partial.execute": "formStep1:remoteRefreshFocus",
        "formStep1:remoteRefreshFocus": "formStep1:remoteRefreshFocus",
    },
    {
        "jakarta.faces.source": "formStep1:btnSuivant",
        "jakarta.faces.partial.execute": "@all",
        "jakarta.faces.partial.render": "formStep1",
        "formStep1:btnSuivant": "formStep1:btnSuivant",
    },
    {
        "jakarta.faces.source": "formStep1:btnValiderModal",
        "jakarta.faces.partial.execute": "formStep1",
        "jakarta.faces.partial.render": "formStep1",
        "formStep1:btnValiderModal": "formStep1:btnValiderModal",
    },
]

