from curl_cffi import requests
from curl_cffi.requests import BrowserType
from extension.captcha_request import get_result
from bs4 import BeautifulSoup
import random
import string
from tool import get_new_proxy, create_session

user_form = {"password": "Kq123456@", "username": "<EMAIL>"}

proxy = get_new_proxy()

proxies = {"http": proxy, "https": proxy}

session = create_session()
# 目标网址
url_home = "https://fr.tlscontact.com/visa/gb/gbMNC2fr/home"
url_login = "https://fr.tlscontact.com/oauth2/authorization/oidc"

# 使用接口返回的值来请求
task_result = get_result(url_home, proxy)
if not task_result.get("solution"):
    print("任务失败", task_result)

solution = task_result.get("solution")
headers = solution.get("request_headers")
headers.update(solution.get("headers"))
cookies = solution.get("cookies")
user_agent = solution.get("user_agent")
# print("Headers: ", headers)
print("\n\nCookies: ", cookies.keys())

session.headers.update(headers)
session.cookies.update(cookies)

res_home = session.get(url_login, proxies=proxies)
soup_login = BeautifulSoup(res_home.text, "html.parser")
login_submit_url = soup_login.select_one("form#kc-form-login").get("action")

session.headers.update({"Content-Type": "application/x-www-form-urlencoded"})
res_login = session.post(login_submit_url, data=user_form, proxies=proxies)

print("end")
