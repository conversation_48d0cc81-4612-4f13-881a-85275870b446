from bs4 import BeautifulSoup
from datetime import datetime
import datetime as dt
import cloudbypass
import random
from curl_cffi import requests
from curl_cffi.requests import BrowserType
from extension.logger import logger
from config import websiteKey_v3, websiteURL_v3
from extension.captcha_request import get_result, CaptchaType
import time
from tool import get_new_proxy, send_dd_msg, send_wx_msg, get_login_proxy
from jwt_decrypt import make_jwt_from_json
from RedisTool import save_user_2_redis_queue, get_login_cookie, get_users_with_queue_name, frace_faker_queue
from http.cookies import SimpleCookie


APIKEY = "a397a7c92a6340e29eafe05e4f5af211"  # 环境变量 CB_APIKEY

default_headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "sec-ch-ua": '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"macOS"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "referer": "https://fr.tlscontact.com",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
}


def get_user_cookie_dict(user):
    cookie_str = user.get("cookie")
    if not cookie_str:
        return None
    logger.info(f"获取到用户cookie: {user['chnname']} {user['username']} {user['issuer']}")
    cookie = SimpleCookie()
    cookie.load(cookie_str)
    # 转换为字典
    cookie_dict = {key: morsel.value for key, morsel in cookie.items()}
    return cookie_dict


def str_2_timestamp(date_str):
    date_str = date_str.replace("/", "-")
    # 定义字符串的格式
    date_format = "%Y-%m-%d"
    # 将字符串转换为datetime对象
    date_obj = datetime.strptime(date_str, date_format)
    # 将datetime对象转换为时间戳（秒数）
    timestamp = date_obj.timestamp()
    return timestamp


def pick_user_accept_day(user_info, open_dates):
    # 判断出是否有可预约日期
    start_date = user_info.get("startDate", "").replace("/", "-")
    end_date = user_info.get("endDate", "").replace("/", "-")
    # 筛选出第一个可预约日期
    available_dates = []
    for date_item in open_dates:  # 2025-06-16
        if str_2_timestamp(start_date) <= str_2_timestamp(date_item) <= str_2_timestamp(end_date):
            available_dates.append(date_item)
    return available_dates


def extract_alert_msg(html_string):
    try:  # 解析并打印下错误提示
        soup = BeautifulSoup(html_string, "html.parser")
        p_tag = soup.find("p", id="kc-page-description")
        if not p_tag:
            p_tag = soup.find("p", id="kc-page-description")
        if not p_tag:
            p_tag = soup.find("p", id="kc-page-description")
        if p_tag:
            return p_tag.get_text(strip=True)
    except Exception as e:
        logger.error(f"##放号查询## extract_danger_alert_msg func: {e}")
    return html_string[-200:]


def get_datadome_cookie():
    url = "https://api-js.datadome.co/js/"
    data = {
        "jsData": '{"ttst":8.400000095367432,"ifov":false,"hc":12,"br_oh":1005,"br_ow":1728,"ua":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","wbd":false,"dp0":true,"tagpu":3.738121195913918,"wdif":false,"wdifrm":false,"npmtm":false,"br_h":887,"br_w":1720,"isf":false,"nddc":1,"rs_h":1117,"rs_w":1728,"rs_cd":30,"phe":false,"nm":false,"jsf":false,"lg":"zh-CN","pr":2,"ars_h":1014,"ars_w":1728,"tz":-480,"str_ss":true,"str_ls":true,"str_idb":true,"str_odb":false,"plgod":false,"plg":5,"plgne":true,"plgre":true,"plgof":false,"plggt":false,"pltod":false,"hcovdr":false,"hcovdr2":true,"plovdr":false,"plovdr2":true,"ftsovdr":false,"ftsovdr2":true,"lb":false,"eva":33,"lo":false,"ts_mtp":0,"ts_tec":false,"ts_tsa":false,"vnd":"Google Inc.","bid":"NA","mmt":"application/pdf,text/pdf","plu":"PDF Viewer,Chrome PDF Viewer,Chromium PDF Viewer,Microsoft Edge PDF Viewer,WebKit built-in PDF","hdn":false,"awe":false,"geb":false,"dat":false,"med":"defined","aco":"probably","acots":false,"acmp":"probably","acmpts":true,"acw":"probably","acwts":false,"acma":"maybe","acmats":false,"acaa":"probably","acaats":true,"ac3":"maybe","ac3ts":false,"acf":"probably","acfts":false,"acmp4":"maybe","acmp4ts":false,"acmp3":"probably","acmp3ts":false,"acwm":"maybe","acwmts":false,"ocpt":false,"vco":"","vcots":false,"vch":"probably","vchts":true,"vcw":"probably","vcwts":true,"vc3":"maybe","vc3ts":false,"vcmp":"","vcmpts":false,"vcq":"","vcqts":false,"vc1":"probably","vc1ts":true,"dvm":8,"sqt":false,"so":"landscape-primary","wdw":true,"cokys":"bG9hZFRpbWVzY3NpYXBwL=","ecpc":false,"lgs":true,"lgsod":false,"psn":true,"edp":true,"addt":true,"wsdc":true,"ccsr":true,"nuad":true,"bcda":true,"idn":true,"capi":false,"svde":false,"vpbq":true,"ucdv":false,"spwn":false,"emt":false,"bfr":false,"dbov":false,"cfpfe":"RXJyb3I6IENhbm5vdCByZWFkIHByb3BlcnRpZXMgb2YgbnVsbA==","stcfp":"ZWEyZjFhMDk0ZmYzMGM1ZWNiNS5jaHVuay5qczoyOjE2NTIzNykKICAgIGF0IFBlcmZvcm1hbmNlT2JzZXJ2ZXIuPGFub255bW91cz4gKGh0dHBzOi8vZnIudGxzY29udGFjdC5jb20vYXBwLzAuMGVhMmYxYTA5NGZmMzBjNWVjYjUuY2h1bmsuanM6MjoxNjM2NTgp","ckwa":true,"prm":true,"cvs":true,"usb":"defined","emd":"k:ai,vi,ao","glvd":"Google Inc. (Apple)","glrd":"ANGLE (Apple, ANGLE Metal Renderer: Apple M2 Max, Unspecified Version)","wwl":false,"jset":1744872891}',
        "eventCounters": [],
        "jsType": "ch",
        "cid": "UBud3WUMvERFFDEIMfQKfojj0gGQljSoSgH72x1wW6Bb0lf4eh5dYWuzJ_ilEEsNZqCDLRgp4hN0~0y7K8XykO9Vt~WFCCyBEKEb8xaokDF3395dllTljmL9Yk4EWmu5",
        "ddk": "19F136CEBC7D86CFF635113AD2A8EA",
        "Referer": "https%3A%2F%2Ffr.tlscontact.com%2Fvisa%2Fcn%2FcnSHA2fr%2Fhome",
        "request": "%2Fvisa%2Fcn%2FcnSHA2fr%2Fhome",
        "responsePage": "origin",
        "ddv": "4.46.0",
    }
    resp = requests.post(url, data=data, headers={"content-type": "application/x-www-form-urlencoded"}, impersonate=BrowserType.chrome131)
    if resp.status_code == 200:
        return resp.json()


def user_login(user_info, session):
    try:
        # 先请求一次首页 获取到XSRF-TOKEN
        issuer = user_info.get("issuer")
        home_page_resp = session.get(f"https://fr.tlscontact.com/visa/cn/{issuer}/home", timeout=60)

        if home_page_resp.status_code != 200:
            logger.error(f"cnSHA2fr/home {home_page_resp.status_code}")
            return False, None
        print("Status:", home_page_resp.status_code, home_page_resp.headers.get("x-cb-status"))
        print("Cookie:", home_page_resp.cookies)

        challenge = session.get("https://fr.tlscontact.com/cdn-cgi/challenge-platform/scripts/jsd/main.js")
        print("Status:", challenge.status_code, challenge.headers.get("x-cb-status"))
        print("Cookie:", challenge.cookies)
        # datadome = get_datadome_cookie()
        # print("datadome:", datadome)
        # 获取登录页面
        login_page_resp = session.get("https://fr.tlscontact.com/oauth2/authorization/oidc", timeout=60)  # , headers=home_page_resp.headers, cookies=home_page_resp.cookies
        print(login_page_resp.status_code)
        print(login_page_resp.headers)
        if login_page_resp.status_code != 200:
            logger.error(f"authorization/oidc {login_page_resp.status_code}")
            return False, None
        # 解析登录页面 form#kc-form-login
        login_page_soup = BeautifulSoup(login_page_resp.text, "html.parser")
        login_form = login_page_soup.select_one("form#kc-form-login")
        login_url = login_form.attrs.get("action").replace("amp;", "")  # 处理请求地址参数 & 转码
        print(login_url)
        # 发起登录请求
        login_form_data = {"username": user_info["username"], "password": user_info["password"]}
        login_resp = session.post(login_url, headers={"Content-Type": "application/x-www-form-urlencoded"}, data=login_form_data, timeout=60)
        if login_resp.status_code == 200:
            logger.success(f"#登录# 成功 {login_resp.status_code}")
            return True, (dict(session.cookies.get_dict()), dict(login_resp.headers))

        print(f"login_resp: {extract_alert_msg(login_resp.text)}")
        return False, None
    except Exception as e:
        logger.error(f"#登录# {user_info.get('username')},登录失败：{e}")
        return False, None


def user_login_fr(user_info, session: requests.Session):
    while True:
        cookie_info = get_login_cookie()
        login_form_data = {"username": user_info["username"], "password": user_info["password"]}
        login_url = cookie_info.get("login_url")
        cookie_str = cookie_info.get("cookie").replace("__cf_bm", "ccc").replace("cf_clearance", "ddd")
        try:
            session.headers.update({"cookie": cookie_str, "Content-Type": "application/x-www-form-urlencoded"})
            login_resp = session.post(login_url, verify=False, data=login_form_data)
        except Exception as e:
            print(f"登录请求失败: {e}")
            raise
        if login_resp.status_code == 200 and (xsrf_token := login_resp.cookies.get_dict().get("XSRF-TOKEN")):
            logger.success(f"#登录# 成功 {user_info['username']} {xsrf_token}")
            session.headers.update({"x-xsrf-token": xsrf_token})
            account_resp = session.get("https://visas-fr.tlscontact.com/api/account")
            logger.info(account_resp)
            return True, cookie_str
        else:
            print(login_resp.status_code)
            time.sleep(1)


def user_logout(user_info, session):
    try:
        res_logout = session.post("https://fr.tlscontact.com/api/logout", data={})
        print(res_logout)
    except Exception as e:
        logger.error(f"退出登录错误: {e}")


def edit_user_info(user, fg_id, issuer, session: requests.Session):
    # issuer = user.get("issuer")
    # fg_id = user.get("fgId")
    f_id = user.get("f_id")
    log_msg = f"{user.get('chnname')} {user.get('username')} {user.get('centerCode')}: "
    for _ in range(3):
        try:
            res_page_group = session.get(f"https://fr.tlscontact.com/formGroup/cn/{issuer}")
            if res_page_group.status_code == 200:
                break
        except Exception as e:
            proxy = get_login_proxy()
            session.proxies.update({"http": proxy, "https": proxy})

    # 订阅协议
    # https://fr.tlscontact.com/services/customerservice/api/tls/consent/fr/cnNKG2fr/subscribe?selectTypes=survey,marketing_from_trusted_partners

    # 获取表单组ID
    # res_fg_ls = session.get(f"https://fr.tlscontact.com/services/customerservice/api/tls/formgroup?client=fr&issuer={issuer}")
    # if res_fg_ls.status_code == 200 and not fg_id:
    #     fg_id = res_fg_ls.json()[0].get("fg_id")
    # 获取表单ID
    # url_f_ls = f"https://fr.tlscontact.com/services/customerservice/api/tls/forms/list/fr/{issuer}?fg_id={fg_id}"

    if not fg_id:
        # 创建表单组
        # group_data = {"client": "fr", "issuer": issuer, "fgName": issuer}
        res_fg = session.post(f"https://fr.tlscontact.com/services/customerservice/api/tls/formgroup?client=fr&issuer={issuer}&fgName={issuer}")

        if res_fg.status_code != 200:
            logger.error(f"创建表单组错误:{res_fg.status_code} {res_fg.text}")
            return False, {}

        fg_json = res_fg.json()
        fg_id = fg_json.get("fg_id")

    if not f_id:
        # 创建表单
        query_data = {
            "client": "fr",
            "issuer": issuer,
            "fgId": fg_id,
        }
        res_f = session.post("https://fr.tlscontact.com/services/customerservice/api/tls/forms/form", params=query_data)
        if res_f.status_code != 200:
            logger.error(f"创建表单错误:{res_f.status_code} {res_f.text}")
            return False, {"fgId": fg_id}

        f_id = res_f.json().get("f_id")

    url_get_form = f"https://fr.tlscontact.com/services/customerservice/api/tls/forms/form/fr/{issuer}/{f_id}"
    res_default_form = session.get(url_get_form)
    if res_default_form.status_code != 200:
        logger.error(f"获取默认表单错误:{res_default_form.status_code}: {res_default_form.text}")
        return False, {"fgId": fg_id, "f_id": f_id}

    res_default_form = res_default_form.json()
    res_default_form.pop("f_tech_deleted", None)
    res_default_form.pop("f_is_anonymised", None)
    res_default_form.pop("f_is_purged", None)
    res_default_form.pop("u_tech_deleted", None)
    res_default_form.pop("u_is_anonymised", None)
    res_default_form.pop("u_change_password_when_login", None)
    res_default_form.pop("u_logged_times", None)
    res_default_form.pop("u_last_session", None)
    res_default_form.pop("u_is_purged", None)
    res_default_form.pop("fg_tech_deleted", None)
    res_default_form.pop("fg_is_anonymised", None)
    res_default_form.pop("fg_is_purged", None)
    res_default_form.pop("ug_tech_deleted", None)
    res_default_form.pop("ff_flags", None)
    res_default_form.pop("fl_links", None)
    mock_travel_date = (datetime.now() + dt.timedelta(days=30)).strftime("%Y-%m-%d")
    mock_leave_date = (datetime.now() + dt.timedelta(days=60)).strftime("%Y-%m-%d")

    saved_infos = {
        "f_pers_sex": "M" if user.get("gender") == "男" else "F",  # 性别
        "f_pers_birth_date": user.get("birthday").replace("/", "-"),
        "f_pers_nationality": user.get("nationality", "cn"),
        "f_pers_province": user.get("signLocation").split("/")[-1],
        "f_identity_type": "ordinary_passport",
        "f_pass_num": user.get("passportNO"),
        "f_pers_mobile_phone": "86" + user.get("phone"),
        "fi_trav_origin_departure_date": user.get("travelDate", mock_travel_date).replace("/", "-"),  # 到达日期,
        "f_trav_departure_date": user.get("travelDate", mock_travel_date).replace("/", "-"),  # 出发日期,
        "f_trav_arrival_date": user.get("travelBack", mock_leave_date).replace("/", "-"),  # 离开日期,
        "f_trav_go_to_domtom": "f",
        "fi_fingerprints_collected": "f",
        "fi_first_schengen_trip": "f",
        "f_visa_type": "short_stay",
        "f_trav_purpose": user.get("tourism_private_visit", "tourism_private_visit"),
        "fg_id": str(fg_id),
        "f_xref_fg_id": str(fg_id),
        "f_id": int(f_id),
        "iat": int(time.time()),
        "exp": None,
    }
    post_cache_from = res_default_form | saved_infos
    # 生成jwt，获取过期参数
    exp_ = session.get("https://fr.tlscontact.com/services/customerservice/api/tls/util/jwt-expiration-date")
    exp_time = int(exp_.json())
    post_cache_from["exp"] = int(exp_time)
    post_cache_from["iat"] = int(exp_time - 35)
    # json生成jwt token
    jwt_token = make_jwt_from_json(post_cache_from)
    # logger.info(f"jwt_token_cache: {jwt_token}")
    cache_info_url = "https://fr.tlscontact.com/services/customerservice/api/tls/forms/generate-form-schema/fr"
    res_cache = session.post(cache_info_url, json={"token": jwt_token}, headers={"Content-Type": "application/json"}, timeout=60)
    if res_cache.status_code != 200:
        logger.error(f"表单缓存错误:{res_cache.status_code}: {res_cache.text}")
        return False, {"fgId": fg_id, "f_id": f_id}

        # 生成jwt，获取过期参数, 先保存，所有同行人加好再提交
    for status in ["pending", "done"]:
        put_cache_from = saved_infos | {
            "f_status": status,
            "f_xref_f_id": int(f_id),
            "f_cai": user.get("fra_no"),
            "f_pers_surnames": user.get("xing"),
            "f_pers_givennames": user.get("name"),
        }
        exp_ = session.get("https://fr.tlscontact.com/services/customerservice/api/tls/util/jwt-expiration-date")
        exp_time = int(exp_.json())
        put_cache_from["exp"] = int(exp_time)
        put_cache_from["iat"] = int(exp_time - 35)

        jwt_token_commit = make_jwt_from_json(put_cache_from)

        # logger.info(f"jwt_token_put: {jwt_token_commit}")
        url_commit = f"https://fr.tlscontact.com/services/customerservice/api/tls/forms/form/fr/{issuer}/{f_id}?accountType=INDI"
        res_put_commit = session.put(url_commit, json={"token": jwt_token_commit}, headers={"Content-Type": "application/json"}, timeout=60)
        # logger.info(f"提交表单: {res_put_commit.json()}")
        if res_put_commit.status_code != 200:
            err_msg = log_msg
            try:
                for item in res_put_commit.json().get("schema")["groups"]:
                    for err_info in item["fields"]:
                        if err_info.get("errors"):
                            err_msg += f"{err_info.get('label')}: {err_info.get('errors')[0].split(',')[0]}, "
            except Exception as e:
                err_msg = err_msg + str(e)
            logger.error(f"表单提交错误:{res_put_commit.status_code}: {err_msg}")
            send_wx_msg(f"填表错误: {user['chnname']} {user['username']}  {err_msg}", centerCode=user["username"])
            send_dd_msg(f"填表错误: {user['chnname']} {user['username']} {err_msg}", centerCode=user["username"])
            return False, {"fgId": fg_id, "f_id": f_id}

    logger.success(f"put_cache_from: {put_cache_from}")
    # if not len(user.get("users", [])):  #
    #     # 提交表单组 确认组内成员
    #     url_submit_app = f"https://fr.tlscontact.com/services/customerservice/api/tls/formgroup/submit?client=fr&issuer={issuer}&formGroupId={fg_id}"
    #     res_submit_app = session.post(url_submit_app, data={})
    #     if res_submit_app.status_code != 200:
    #         logger.error(f"预约提交表单错误:{res_submit_app.status_code}: {res_submit_app.text}")
    #         return False, res_submit_app.status_code

    #     logger.success(f"预约提交表单接口返回: {res_submit_app.json()}")
    return True, {"fgId": fg_id, "f_id": f_id}


def commint_user_form(fg_id, f_id, issuer, session: requests.Session):
    try:
        url_cmooit_status = f"https://fr.tlscontact.com/services/customerservice/api/tls/forms/actions/fr/{issuer}/{f_id}?includeDeleted=true"
        res_status = session.get(url_cmooit_status)
        if res_status.status_code == 200:
            if len(res_status.json()) and res_status.json()[0]["a_what"] == "submit":
                return True, None
        url_submit_app = f"https://fr.tlscontact.com/services/customerservice/api/tls/formgroup/submit?client=fr&issuer={issuer}&formGroupId={fg_id}"
        for _ in range(3):
            # 提交表单组 确认组内成员
            res_submit_app = session.post(url_submit_app, data={})
            if res_submit_app.status_code != 200:
                logger.info(f"#提交表单#重试:{res_submit_app.status_code}: {res_submit_app.text}")
                proxy = get_new_proxy()
                session.proxies.update({"http": proxy, "https": proxy})
                continue
            else:
                logger.success(f"#提交表单#成功: {res_submit_app.json()}")
                return True, res_submit_app.json()

        logger.error(f"#提交表单#错误:{res_submit_app.status_code}: {res_submit_app.text}")
        return False, res_submit_app.status_code
    except Exception as e:
        logger.error(f"#提交表单#错误:{e}")
        return False, e


def user_date_appointment(user, session: requests.Session):
    try:
        issuer = user.get("issuer")
        fg_id = user.get("fgId")
        # # 查询开放日期
        # flag_query, open_dates = query_open_days(user, session)
        # if not flag_query:
        #     logger.error(f"查询预约日期错误: {open_dates}")
        #     return False, open_dates
        # day_ = sorted(open_dates.keys())  # 最早的日期
        # all_ok_days = pick_user_accept_day(user, day_)
        # ok_day = random.choice(all_ok_days) if all_ok_days else None
        # if not ok_day:
        #     logger.error(f"没有可预约日期: {user['username']}")
        #     # return False, None
        # else:
        #     ok_time = random.choice(list(open_dates[ok_day].keys()))

        date_url = "https://fr.tlscontact.com/services/customerservice/api/tls/appointment/book"
        date_params = {
            "client": "fr",
            "issuer": issuer,
            "formGroupId": fg_id,
            "timeslot": "2025-05-27 09:30",  # f"{ok_day} {ok_time}",
            "appointmentType": "normal",
            "accountType": "INDI",
            "lang": "zh-cn",
        }
        reCaptcha_token = None
        for _ in range(5):
            try:
                task_result = get_result(websiteURL_v3, websiteKey_v3, task_type=CaptchaType.CAPTCHA_V3.value)
                if not task_result.get("solution"):
                    print("任务失败", task_result)
                    continue
                else:  # 获得了recaptcha_token
                    solution = task_result.get("solution")
                    reCaptcha_token = solution["gRecaptchaResponse"]

                    session.headers.update({"recaptcha-token": reCaptcha_token, "content-type": "application/json"})

                    res_book = session.post(date_url, params=date_params, data=None)
                    if res_book.status_code != 200:
                        logger.error(f"预约日期错误:{res_book.status_code}: {res_book.text}")
                        proxy = get_new_proxy()
                        session.proxies.update({"http": proxy, "https": proxy})
                        continue
                    else:
                        logger.success(f"预约成功: {res_book.json()}")
                        msg_success = f"法国用户预约成功: {user['chnname']} {user['username']} {user['centerCode']} {date_params['timeslot']} {res_book.json()}"
                        send_dd_msg(msg_success)
                        send_wx_msg(msg_success)
                        return True, date_params
            except Exception as e:
                proxy = get_new_proxy()
                session.proxies.update({"http": proxy, "https": proxy})
                continue
        return False, None
    except Exception as e:
        logger.error(f"预约日期错误: {e}")
        return False, None


def get_fg_id(user_info, session):
    try:
        issuer = user_info.get("issuer")
        url_form_group_id = f"https://fr.tlscontact.com/services/customerservice/api/tls/formgroup?client=fr&issuer={issuer}"
        res_fg_ids = session.get(url_form_group_id)
        if res_fg_ids.status_code != 200:
            logger.error(res_fg_ids.text)
            return False, res_fg_ids.status_code

        fg_ids = res_fg_ids.json()
        print("fg_ids：", fg_ids)
        if len(fg_ids) == 0:
            logger.error("fg_ids is empty")
            return False, None

        fg_id = fg_ids[0].get("fg_id")
        return True, fg_id
    except Exception as e:
        logger.error(f"get_fg_id Error: {e}")
        return False, None


def query_open_days(user_info, session, appointmentType="normal"):
    fg_id = user_info.get("fgId")
    issuer = user_info.get("issuer")
    query_data = {
        "client": "fr",
        "formGroupId": fg_id,
        "appointmentType": appointmentType,  # normal 、 premium 、 prime time
        "appointmentStage": "appointment",
    }
    open_day_url = f"https://fr.tlscontact.com/services/customerservice/api/tls/appointment/cn/{issuer}/table"
    try:
        res_open_days = session.get(open_day_url, params=query_data)
        if res_open_days.status_code != 200:
            logger.debug(f"#放号日期# {user_info['username']} {appointmentType} {res_open_days.status_code}")
            return False, res_open_days.status_code
        open_days = res_open_days.json()
        # open_days =
        # logger.info(f"#放号日期# {user_info['username']} {appointmentType} {sorted(open_days.keys())[:5]}")
        return True, open_days
    except Exception as e:
        logger.warning(f"#放号日期查询错误# {appointmentType} {e}")
        return False, None


def fill_tls_user_form(user: dict, is_faker=False):
    # 转换为字典
    cookie_dict = get_user_cookie_dict(user)
    if not cookie_dict:
        return

    proxy_ = get_new_proxy()  # get_login_proxy()
    proxies = {"http": proxy_, "https": proxy_}
    session = requests.Session(
        impersonate=BrowserType.chrome131,
        headers={**default_headers, "x-xsrf-token": cookie_dict.get("XSRF-TOKEN")},
        cookies=cookie_dict,
        proxies=proxies,
    )

    fgId = user.get("fgId", None)
    issuer = user.get("issuer", None)
    if not fgId:
        flag, fgId_ = get_fg_id(user, session)
        if flag:
            fgId = fgId_
            user["fgId"] = fgId_

    flag_commit = True
    if user.get("slot_status") != "form_ok":
        flag, res = edit_user_info(user, fgId, issuer, session)
        user.update(res)
        if flag:
            user["slot_status"] = "form_ok"
            save_user_2_redis_queue(user)
        else:
            flag_commit = False

    # 添加表单组内的子用户，同行人员
    fgId = user.get("fgId", None)
    for sub_user in user.get("users", []):
        if sub_user.get("slot_status") == "form_ok":
            continue

        flag_sub, res = edit_user_info(sub_user, fgId, issuer, session)
        sub_user.update(res)
        if res.get("fgId"):  # 更新 fgId
            user["fgId"] = res.get("fgId")
            fgId = res.get("fgId")

        if flag_sub:
            sub_user["slot_status"] = "form_ok"
        else:
            flag_commit = False

        save_user_2_redis_queue(user)

    save_user_2_redis_queue(user)
    # 提交表单组 等待预约
    if flag_commit and fgId and user.get("f_id"):
        res_commit, res = commint_user_form(fgId, user.get("f_id"), issuer, session)
        if res_commit:
            user["status"] = "visa_ok"
            msg = f"法国用户等待预约: {user['chnname']} {user['username']} {user['password']} {user['password_gov']} {user['centerCode']} {user['startDate']}-{user['endDate']}"
            # 查询开放日期
            flag_nor, nor_days = query_open_days(user, session)
            if flag_nor:
                day_nor = sorted(nor_days.keys())[0]  # 最早的日期
                msg = msg + f" | 最早Normal日期: {day_nor}"

            flag_vip, vip_days = query_open_days(user, session, appointmentType="prime time")
            if flag_vip:
                day_vip = sorted(vip_days.keys())[0]
                msg = msg + f" | 最早premium日期: {day_vip}"

            msg = msg + f"\n 预约地址: https://fr.tlscontact.com/appointment/cn/{issuer}/{fgId}"
            send_dd_msg(msg)
            if not is_faker:
                send_wx_msg(msg)
        else:
            user["cookie"] = None
    else:
        user["cookie"] = None
    save_user_2_redis_queue(user)
    # logger.info(user)


def date_appointment_pipline(user):
    # 转换为字典
    cookie_dict = get_user_cookie_dict(user)
    if not cookie_dict:
        return

    proxy_ = get_login_proxy()
    proxies = {"http": proxy_, "https": proxy_}
    session = requests.Session(
        impersonate=BrowserType.chrome131,
        headers={
            **default_headers,
            "x-xsrf-token": cookie_dict.get("XSRF-TOKEN"),
        },
        cookies=cookie_dict,
        proxies=proxies,
    )
    user_date_appointment(user, session)


if __name__ == "__main__":
    users = get_users_with_queue_name(frace_faker_queue)
    for u in users:
        if u["username"] == "<EMAIL>":
            print(u["username"], u["chnname"])
            # flag = fill_tls_user_form(u, True)
            date_appointment_pipline(u)

    logger.info("111")
