from bs4 import BeautifulSoup
import datetime
import random
from curl_cffi import requests
from curl_cffi.requests import BrowserType
from extension.logger import logger
from config import websiteKey_v3, websiteURL_v3
from extension.captcha_request import get_result, CaptchaType
import time
from tool import get_new_proxy, create_session, send_wx_msg
from jwt_decrypt import make_jwt_from_json
from RedisTool import save_user_2_redis_queue, get_login_cookie, get_users_with_queue_name, frace_faker_queue
from http.cookies import SimpleCookie

def extract_alert_msg(html_string):
    try:  # 解析并打印下错误提示
        err_soup = BeautifulSoup(html_string, "html.parser")
        alert_el = err_soup.select_one(".alert-danger")
        if not alert_el:
            alert_el = err_soup.select_one(".alert-warning")
        if not alert_el:
            alert_el = err_soup.select_one(".alert-error")
        if alert_el:
            return alert_el.text.strip()
    except Exception as e:
        logger.error(f"##放号查询## extract_danger_alert_msg func: {e}")
    return html_string[-200:]



def user_login(user_info, session: requests.Session):
    try:
        # 先请求一次首页 获取到XSRF-TOKEN
        issuer = user_info.get("issuer")
        home_page_resp = session.get(f"https://fr.tlscontact.com/visa/cn/{issuer}/home", timeout=60)

        if home_page_resp.status_code != 200:
            logger.error(f"cnSHA2fr/home {home_page_resp.status_code}")
            return False, None
        print("Status:", home_page_resp.status_code, home_page_resp.headers.get("x-cb-status"))
        print("Cookie:", home_page_resp.cookies)

        challenge = session.get("https://fr.tlscontact.com/cdn-cgi/challenge-platform/scripts/jsd/main.js")
        print("Status:", challenge.status_code, challenge.headers.get("x-cb-status"))
        print("Cookie:", challenge.cookies)
        # datadome = get_datadome_cookie()
        # print("datadome:", datadome)
        # 获取登录页面
        login_page_resp = session.get("https://fr.tlscontact.com/oauth2/authorization/oidc", timeout=60)  # , headers=home_page_resp.headers, cookies=home_page_resp.cookies
        print(login_page_resp.status_code)
        print(login_page_resp.headers)
        if login_page_resp.status_code != 200:
            logger.error(f"authorization/oidc {login_page_resp.status_code}")
            return False, None
        # 解析登录页面 form#kc-form-login
        login_page_soup = BeautifulSoup(login_page_resp.text, "html.parser")
        login_form = login_page_soup.select_one("form#kc-form-login")
        login_url = login_form.attrs.get("action").replace("amp;", "")  # 处理请求地址参数 & 转码
        print(login_url)
        # 发起登录请求
        login_form_data = {"username": user_info["username"], "password": user_info["password"]}
        login_resp = session.post(login_url, headers={"Content-Type": "application/x-www-form-urlencoded"}, data=login_form_data, timeout=60)
        if login_resp.status_code == 200:
            logger.success(f"#登录# 成功 {login_resp.status_code}")
            return True, (dict(session.cookies.get_dict()), dict(login_resp.headers))

        print(f"login_resp: {extract_alert_msg(login_resp.text)}")
        return False, None
    except Exception as e:
        logger.error(f"#登录# {user_info.get('username')},登录失败：{e}")
        return False, None


user_ = {
        "username": "<EMAIL>",
        "password": "Kq123456@",
        "password_gov": "Kq1234567890@",
        "issuer": "cnBJS2fr",  # cnNKG2fr cnBJS2fr
        "queue_name": "fraceFakerUsers",
        "updateTime": time.time(),
        "status": "pending",
        "passportNO": "*********",
        "fras": ["FRA1PE20257049601", "FRA1NA20257013429", "FRA1SH20257064601", "FRA1PE20257046942"],
        "fra_no": "FRA1PE20257050207",
        "birthday": "1999/03/27",
        "chnname": "刘家熙",
        "endDate": "2025/04/28",
        "expiredDT": "2034/05/14",
        "gender": "男",
        "name": "JIAXI",
        "xing": "LIU",
        "phone": 17621970677,
        "startDate": "2025/04/25",
        "visaTypeCode": "Tourism",
        "countryCode": "CHN",
        "signLocation": "上海/SHANGHAI",
        "bornplace": "江苏/JIANGSU",
        "passportDate": "2024/05/15",
        "maritalStatus": "D",
        "travelDate": "2025/05/13",
        "centerCode": "SHANGHAI",
    }

session = create_session()

user_login(user_, session)
