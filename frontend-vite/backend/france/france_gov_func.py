from bs4 import BeautifulSoup
from curl_cffi import requests
from curl_cffi.requests import BrowserType
from extension.logger import logger
from datetime import datetime
import re
import time
from tool import get_new_proxy, get_login_proxy, captcha_img, generate_random_email, generate_complex_pwd, get_faker_user, create_session
from config import step1_dict, submissionMap, issuerMap
from RedisTool import get_email_otp, save_user_2_redis_queue, get_user_info, get_users_with_queue_name
import os
import base64
# url_host = "https://application-form.france-visas.gouv.fr"


# 北京 广州  长沙 成都 重庆 福州  杭州  济南 昆明  南京  沈阳  深圳  武汉  西安
# 1PE 1CB 1CS  1CA  1CQ  1FU  1HN  1JN 1KU  1NA  1SY  1SZ 1WU  1XI

url_host = "https://connect.france-visas.gouv.fr"


def extract_alert_msg(html_string):
    try:  # 解析并打印下错误提示
        err_soup = BeautifulSoup(html_string, "html.parser")
        alert_el = err_soup.select_one(".alert-danger")
        if not alert_el:
            alert_el = err_soup.select_one(".alert-warning")
        if not alert_el:
            alert_el = err_soup.select_one(".alert-error")
        if alert_el:
            return alert_el.text.strip()
    except Exception as e:
        logger.error(f"##放号查询## extract_danger_alert_msg func: {e}")
    return html_string[-200:]


def parse_xml(html_string: str):
    try:
        soup = BeautifulSoup(html_string, "xml")
        # 查找CDATA部分中的form标签
        cdata = soup.find("update", {"id": "formStep1"})
        if cdata:
            form_soup = BeautifulSoup(cdata.text, "html.parser")
            if form_soup:
                # 查找csrf token
                csrf_input = form_soup.find("input", {"name": "_csrf"})
                if csrf_input:
                    return csrf_input["value"]
        return None
    except Exception as e:
        logger.error(f"解析xml错误: {e}")
        return None


def gov_user_registe(user: dict, session: requests.Session):
    for _ in range(3):
        try:
            logger.info(f"开始注册france_gov用户: {user['username']} {user['chnname']} ")
            res_ = session.get("https://application-form.france-visas.gouv.fr/fv-fo-dde/")
            if res_.status_code != 200:
                return False, None
            soup = BeautifulSoup(res_.text, "html.parser")
            el_reg = soup.select_one(".primaire")
            url_reg_home = el_reg.attrs.get("href").replace("amp;", "")
            print("注册链接", url_reg_home)
            # 注册页面 解析 验证码链接和post接口地址
            res_reg_home = session.get(url_host + url_reg_home)
            soup_registe = BeautifulSoup(res_reg_home.text, "html.parser")

            for _ in range(3):
                # 验证码图片获取链接
                el_tat = soup_registe.select_one("div#captchetat")
                captcha_c = el_tat.attrs.get("captchastylename")
                bacnkend = el_tat.attrs.get("urlbackend").replace("amp;", "")
                query_data = {"get": "image", "c": captcha_c}
                # 获取验证码图片
                capimg_res = session.get(bacnkend, params=query_data)
                if capimg_res.status_code != 200:
                    return False, None

                re_json = capimg_res.json()
                uuid_re = re_json["uuid"]
                # 验证码识别
                captcha_num = captcha_img(re_json["imageb64"])
                print("注册验证码", captcha_num)
                # 验证码验证接口
                form_registe = soup_registe.select_one("form#kc-register-form")
                reg_post_url = form_registe.attrs.get("action").replace("amp;", "")
                registe_form = {
                    "lastName": user["xing"],
                    "firstName": user["name"],
                    "email": user["username"],
                    "emailVerif": user["username"],
                    "user.attributes.ddeLanguage": "English",
                    "password": user.get("password_gov", "Kq1234567890@"),
                    "password-confirm": user.get("password_gov", "Kq1234567890@"),
                    "captchetat-uuid": uuid_re,
                    "captchaFormulaireExtInput": captcha_num,
                }

                regis_res = session.post(reg_post_url, data=registe_form)
                if regis_res.status_code != 200:
                    logger.error(f"注册表单post:{regis_res.status_code}")
                    return False, None
                soup_registe = BeautifulSoup(regis_res.text, "html.parser")
                el_resend = soup_registe.select_one(".primaire")
                if not el_resend:  # 注册提交验证码可能失败了 再获取一次
                    err_msg = extract_alert_msg(regis_res.text)
                    if "The email address already exists" in err_msg:
                        user["username"] = generate_random_email()
                        save_user_2_redis_queue(user)
                    logger.info(f"#注册# 重试识别验证码: {user['username']} {err_msg}")
                    continue
                else:
                    try:
                        bs4_img = re_json["imageb64"]
                        if "data:image" in re_json["imageb64"]:
                            bs4_img = re_json["imageb64"].split(",")[-1]
                        image = base64.b64decode(bs4_img)
                        with open(os.path.join("captcha_imgs", f"{captcha_num}.png"), "wb") as f:
                            f.write(image)
                    except Exception as e:
                        logger.error(f"captcha_img:{e}")
                    break

            if not el_resend:  # 注册提交验证码可能失败了
                return False, None
            url_resend = el_resend.attrs.get("href").replace("amp;", "")
            print(url_resend)
            # 从邮箱获取确认链接，二次确认完成注册。
            confirm_url = get_email_otp(user["username"])
            if not confirm_url:
                logger.error(f"获取邮箱验证码失败: {user['username']}")
                return False, None
            res_confirm = session.get(confirm_url)
            if res_confirm.status_code != 200:
                return False, None
            else:
                return True, res_confirm
        except Exception as e:
            logger.error(f"注册异常, 更换代理重试: {e}")
            proxy = get_login_proxy()
            session.proxies.update({"http": proxy, "https": proxy})
    return False, None


def gov_user_login(user: dict, session: requests.Session):
    logger.info(f"开始登录:{user['username']}")
    for _ in range(3):
        try:
            res_ = session.get("https://application-form.france-visas.gouv.fr/fv-fo-dde/")
            if res_.status_code != 200:
                return False, None
            soup = BeautifulSoup(res_.text, "html.parser")
            el_login = soup.select_one("form#kc-form-login")
            url_login_home = el_login.attrs.get("action").replace("amp;", "")
            login_form = {
                "username": user["username"],
                "password": user["password_gov"],
            }
            res_login = session.post(url_login_home, data=login_form)
            if res_login.status_code != 200:
                logger.error("登录失败")
                return False, None
            logger.success(f"登录成功:{user['username']}")
            return True, res_login
        except Exception as e:
            logger.error(f"登录异常, 更换代理重试: {e}")
            proxy = get_new_proxy()
            # proxy = get_login_proxy()
            proxies = {"http": proxy, "https": proxy}
            session.proxies.update(proxies)
    return False, None


def date_format(date_str: str):
    # 解析字符串为 datetime 对象
    date_obj = datetime.strptime(date_str, "%Y/%m/%d")
    # 格式化 datetime 对象为新的字符串
    new_date_str = date_obj.strftime("%d/%m/%Y")
    return new_date_str


# verifier -> focus -> btnSuivant -> btnValiderModal
def gov_user_edit_form(user: dict, session: requests.Session, home_html: str):
    for _ in range(3):
        try:
            # 解析确认链接
            soup_home = BeautifulSoup(home_html, "html.parser")
            form_step = soup_home.select_one("form#formAccueilUsager")
            accueil_data = {_.get("name"): _.get("value") for _ in form_step.select("input")}
            accueil_data_basic = {
                "jakarta.faces.partial.ajax": True,
                "jakarta.faces.source": "formAccueilUsager:refreshFocus",
                "jakarta.faces.partial.execute": "formAccueilUsager:refreshFocus",
                "formAccueilUsager:remoteRefreshFocus": "formAccueilUsager:refreshFocus",
            }
            accueil_data_post = accueil_data | accueil_data_basic

            res_step1_basic = session.post("https://application-form.france-visas.gouv.fr/fv-fo-dde/accueil.xhtml", data=accueil_data_post)
            if res_step1_basic.status_code != 200:
                logger.error(f"获取step_1失败:{res_step1_basic.status_code}")
                return False, None

            accueil_data["formAccueilUsager:ajouterGroupe"] = ""
            res_step1_basic = session.post("https://application-form.france-visas.gouv.fr/fv-fo-dde/accueil.xhtml", data=accueil_data)
            if res_step1_basic.status_code != 200:
                logger.error(f"获取step_1失败:{res_step1_basic.status_code}")
                return False, None
            else:
                logger.info(f"{res_step1_basic.status_code}:{res_step1_basic.url} 获取step_1成功, 开始填表")

            step1_url = "https://application-form.france-visas.gouv.fr/fv-fo-dde/step1.xhtml"

            soup_basic = BeautifulSoup(res_step1_basic.text, "html.parser")
            form_step_basic = {_.get("name"): _.get("value") for _ in soup_basic.select("input")}
            form_step_basic.pop("dockbarDde-form", None)
            form_step_basic.pop("formHeader", None)
            form_step_basic.pop("formSteps", None)
            post_data_basic = {
                "jakarta.faces.partial.ajax": True,
                "formStep1": "formStep1",
                "formStep1:visas-selected-nationality_input": user["countryCode"],  # 国籍
                "formStep1:hasNationalFamily": False,
                "formStep1:Visas-selected-deposit-country_input": user["countryCode"],  # 居住国
                "formStep1:Visas-selected-stayDuration_input": "C",  # C < 90  D > 90  A airport  访问类型
                "formStep1:Visas-selected-destination_input": "MET",  # 主要目的地 法国
                "formStep1:Visas-selected-deposit-town_input": submissionMap.get(user["centerCode"], "1SH"),
                "formStep1:Visas-selected-authority_input": "CHN",
                "formStep1:Visas-dde-travel-document_input": "10",  # oridinary passport
                "formStep1:Visas-dde-travel-document-number": user["passportNO"],
                "formStep1:Visas-dde-release_date_real_input": date_format(user["passportDate"]),
                "formStep1:Visas-dde-expiration_date_input": date_format(user["expiredDT"]),
                "formStep1:Visas-selected-purposeCategory_input": user["visaTypeCode"],  # TRAV 商务   VISF 探亲 TOUR
                "formStep1:Visas-selected-purpose_input": "1",
                "formStep1:ApplicationWebflow-purposeComplement-div": "",
                "jakarta.faces.ViewState": "",
            }
            post_data_basic["jakarta.faces.ViewState"] = form_step_basic.get("jakarta.faces.ViewState")
            _csrf = form_step_basic["_csrf"]
            for data in step1_dict[:]:
                post_data_tmp = form_step_basic | post_data_basic | data | {"_csrf": _csrf}
                res_step1_temp = session.post(step1_url, data=post_data_tmp, timeout=5)
                if res_step1_temp.status_code != 200:
                    logger.error(f"填表 verify 第一步错误 {data}")
                    return False, res_step1_temp.status_code
                csrf_tmp = parse_xml(res_step1_temp.text)
                # 更新 _csrf
                if csrf_tmp:
                    _csrf = csrf_tmp
                    logger.success(f"step1 csrf_token focus: {_csrf}")

            if res_step1_temp.status_code != 200 or "/fv-fo-dde/step2.xhtml" not in res_step1_temp.text:
                logger.error(f"填表第一步错误: {res_step1_temp.status_code} {res_step1_temp.text}")
                return False, res_step1_temp.status_code
            else:
                logger.success(f"填表第一步完成: {res_step1_temp.text}")

            for _ in range(3):
                res_page_home = session.get("https://application-form.france-visas.gouv.fr/fv-fo-dde/accueil.xhtml", timeout=20)
                if res_page_home.status_code != 200:
                    logger.error(f"返回首页错误，更换代理重试: {res_page_home.status_code}")
                    proxy = get_new_proxy()
                    session.proxies.update({"http": proxy, "https": proxy})
                else:
                    logger.success(f"填表第一步完成: {res_step1_temp.text}")
                    fra_pattern = re.compile(r"FRA\d+[A-Za-z0-9]*")
                    fra_matches = fra_pattern.findall(res_page_home.text)
                    logger.success(f"FRA: {list(set(fra_matches))}")
                    return True, list(set(fra_matches)) if fra_matches else None

            return False, None
        except Exception as e:
            logger.error(f"填表异常, 更换代理重试: {e}")
            proxy = get_new_proxy()
            session.proxies.update({"http": proxy, "https": proxy})
    return False, None


def edit_after_login(user: dict = None, return_info=True):
    user_ = user
    if not user:
        # user_ = get_faker_user("BEIJING")  # 假用户
        return
    if user.get("fra_no"):
        logger.info(f"用户已存在FRA:{user['chnname']} {user.get('username')} {user['status']} {user.get('fra_no')}")
        return
    # proxy_ = get_new_proxy()
    # proxies = {"http": proxy_, "https": proxy_}
    session_curl = create_session()  # requests.Session(impersonate=BrowserType.chrome131, proxies=proxies)
    flag, res_confirm = gov_user_login(user_, session_curl)

    if flag:
        flag_edit, fra_no = gov_user_edit_form(user_, session_curl, res_confirm.text)
        if flag_edit:
            # mission = submissionMap.get(user_["centerCode"])
            user_["fras"] = fra_no
            user_["status"] = "gov_ok"
            user_["fra_no"] = fra_no[0]
            save_user_2_redis_queue(user_)
            for sub_user in user_.get("users", []):
                flag_edit_sub, fra_no = gov_user_edit_form(sub_user, session_curl, res_confirm.text)
                if flag_edit_sub:
                    sub_user["fras"] = fra_no
                    sub_user["status"] = "gov_ok"  # ['FRA1SH20257074918', 'FRA1SH20257074919']
                    sorted(fra_no)  # 升序排列，最后一个就是最新的fra_no
                    sub_user["fra_no"] = fra_no[-1]
                    save_user_2_redis_queue(user_)
                else:
                    return False, user_
            return True, user_
    else:
        logger.debug(f"登录失败:{user_['username']} {user_['chnname']} {user_['issuer']}")
    return False, None


def user_registe_woker(user_, return_info=True):
    # proxy_login = get_login_proxy()
    # session_cb = cloudbypass.SessionV2(apikey=APIKEY, proxy=proxy_login)
    # proxies = {"http": proxy_login, "https": proxy_login}
    # requests.Session(impersonate=BrowserType.chrome131, proxies=proxies)
    session_curl = create_session()
    # 注册
    flag, res_confirm = gov_user_registe(user_, session_curl)

    if return_info:
        return flag, res_confirm
    if flag:
        user_["status"] = "gov_reg"
        user_["updateTime"] = int(time.time())
        save_user_2_redis_queue(user_)
        logger.success(f"注册成功：{username}")
        return user_
    else:
        logger.error(f"注册失败：{username}")
        return None


if __name__ == "__main__":
    users = get_users_with_queue_name()
    for u in users:
        if u["status"] == "visa_reg":
            print(u)
            flag = edit_after_login(u)

    username = generate_random_email()
    centerCode = "FUZHOU"
    user_ = {
        "username": username,
        "password": "Kq123456@",
        "password_gov": generate_complex_pwd(),
        "issuer": issuerMap[centerCode],
        "queue_name": "fraceUserDatas",
        "updateTime": time.time(),
        "status": "pending",
        "centerCode": centerCode,
        "chnname": "徐鑫",
        "xing": "XU",
        "name": "XIN",
        "gender": "男",
        "birthday": "1994/02/10",  # ["Jan", "Feb", "Mar", "Apr", "May", "Jun",  "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        "phone": "18591731647",
        "passportNO": "*********",
        "passportDate": "2018/4/16",
        "expiredDT": "2028/4/15",
        "startDate": "2025/06/22",
        "endDate": "2025/07/13",
        "signLocation": "陕西/SHAANXI",  # "江苏/JIANGSU"  "上海/SHANGHAI" "陕西/SHAANXI"
        "bornplace": "陕西/SHAANXI",
        "visaTypeCode": "TOUR",
        "countryCode": "CHN",
        "maritalStatus": "MAR",  # MAR  DIV
    }
    print(user_)
    # save_user_2_redis_queue(user_, False)
    if user_registe_woker(user_):
        edit_after_login(user_)
    print("注册完成")
