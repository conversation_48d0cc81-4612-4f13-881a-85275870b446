url_get = "https://fr.tlscontact.com/services/customerservice/api/tls/appointment/cn/cnSHA2fr/table?client=fr&formGroupId=********&appointmentType=normal&appointmentStage=appointment"
get_data = {"client": "fr", "formGroupId": "********", "appointmentType": "normal", "appointmentStage": "appointment"}


## 保存编辑的信息
url = "https://fr.tlscontact.com/services/customerservice/api/tls/forms/form/fr/cnSHA2fr/********?accountType=INDI"
put_data = {
    "token": "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jnYxNJcSqh9tGIl8VtPMmT6zZtI2sQ9s0y09Aq2NrcC6F6eWBscoAtB635lo5lX-yKOLxOo61UbFCHpGRXBGAw"
}
{
    "f_cai": "FRA1SH20257041179",
    "f_pers_surnames": "T<PERSON>",
    "f_pers_givennames": "J<PERSON>NKA<PERSON>",
    "f_pers_sex": "M",
    "f_pers_birth_date": "1993-06-16",
    "f_pers_nationality": "cn",
    "f_pers_province": "SHANGHAI",
    "f_identity_type": "ordinary_passport",
    "f_pass_num": "*********",
    "f_pers_mobile_phone": "8617621979539",
    "fi_trav_origin_departure_date": "2025-03-30",
    "f_trav_departure_date": "2025-03-30",
    "f_trav_arrival_date": "2025-04-29",
    "f_trav_go_to_domtom": "f",
    "fi_fingerprints_collected": "f",
    "fi_first_schengen_trip": "f",
    "f_visa_type": "short_stay",
    "f_trav_purpose": "tourism_private_visit",
    "f_status": "done",
    "fg_id": "********",
    "f_xref_fg_id": "********",
    "f_id": ********,
    "f_xref_f_id": ********,
    "iat": 1742224651,
    "exp": 1742224686,
}


# 查询预约状态
url = "https://fr.tlscontact.com/services/customerservice/api/tls/appointment/bookstatus/fr/cnSHA2fr/********"
res = {
    "fg_id": ********,
    "variant": "schengen_vac_without_dataentry",
    "fg_process": "schengen_vac_without_dataentry",
    "stage": "appointment",
    "stage_number": 3,
    "status": "pending",  # taken
    "fg_count_f_id": 1,
    "fg_is_anonymised": False,
    "ug_type": "INDI",
    "ug_xref_i_tag": "cnSHA2fr",
}

#  查询普通预约时间段 appointmentType=normal 、 premium 、 prime time， 两分钟内连续查询 间隔2s，后会404
url_get = "https://fr.tlscontact.com/services/customerservice/api/tls/appointment/cn/cnSHA2fr/table?client=fr&formGroupId=********&appointmentType=normal&appointmentStage=appointment"
query_data = {
    "client": "fr",
    "formGroupId": "********",
    "appointmentType": "normal",
    "appointmentStage": "appointment",
}
res = {
    "2025-04-30": {"10:30": 1, "11:00": 1, "11:30": 1, "13:30": 1},
}


# 确认时间
url = "https://fr.tlscontact.com/services/customerservice/api/tls/appointment/book?client=fr&issuer=cnSHA2fr&formGroupId=********&timeslot=2025-04-30%2013:30&appointmentType=normal&accountType=INDI&lang=zh-cn"
query_string = {
    "issuer": "cnSHA2fr",
    "fg_id": "********",
    "action": " take_appointment",
    "timeslot": "2025-04-30 11:30",
    "appointment_type": "normal",
    "lang": "zh-cn",
}
res = {"status": "success", "message": "take appointment success"}
# 确认付款

url = "https://fr.tlscontact.com/api/account"


# 查询操作记录
url = "https://fr.tlscontact.com/services/customerservice/api/tls/forms/actions/fr/cnSHA2fr/********?includeDeleted=true"
res = [
    {
        "a_form": ********,
        "a_what": "submit",
        "a_who": ********,
        "a_occurence": 1,
        "a_id": *********,
        "a_tech_deleted": False,
        "a_when": "2025-03-17 15:19:16.740144",
        "a_tech_modification": "2025-03-17 15:19:16.740144+00",
        "a_tech_creation": "2025-03-17 15:19:16.740144+00",
    },
    {
        "a_form": ********,
        "a_what": "take_appointment",
        "a_who": ********,
        "a_occurence": 1,
        "a_id": *********,
        "a_result": "2025-04-30 13:30",
        "a_tech_deleted": False,
        "a_when": "2025-03-18 22:29:34",
        "a_tech_modification": "2025-03-18 14:29:34.492059+00",
        "a_tech_creation": "2025-03-18 14:29:34.492059+00",
    },
]
