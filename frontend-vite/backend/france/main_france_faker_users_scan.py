from RedisTool import get_users_with_queue_name, save_user_2_redis_queue, frace_faker_queue, save_logs
from france_gov_func import user_registe_woker, edit_after_login, gov_user_login, gov_user_edit_form
from france_visa_func import fill_tls_user_form, query_open_days, get_user_cookie_dict
from captcha_registe import user_registe
from curl_cffi import requests
from tool import generate_random_email, generate_complex_pwd
from extension.logger import logger
from config import issuerMap
import time
from tool import create_session
from config import issuerMap
from extension.logger import logger
from queue import Queue
from threading import Thread

user_queue = Queue()


def query_slot_days(user):
    session = create_session()
    cookie_dict = get_user_cookie_dict(user)
    if not cookie_dict:
        return None
    session.cookies.update(cookie_dict)
    session.headers.update({"x-xsrf-token": cookie_dict.get("XSRF-TOKEN")})
    visa_info = {
        "updateTime": int(time.time()),
        "normal": [],
        "premium": [],
        "prime time": [],
        "prime time weekend": [],
    }
    for app_type in ["normal", "premium", "prime time", "prime time weekend"]:
        flag, open_days = query_open_days(user, session, app_type)
        if not flag or open_days in [401]:
            logger.error(f"#查询预约时间错误# {user['username']} {user['centerCode']} {app_type} {open_days}")
            user["cookie"] = None
            save_user_2_redis_queue(user)
        else:
            days_open = sorted(open_days.keys())
            visa_info[app_type] = days_open
            logger.info(f"#放号日期# {user['username']} {user['centerCode']} {app_type} {sorted(open_days.keys())}")

    save_logs(user, visa_info)


def user_edit_form_pipline(user: dict):
    if not user:
        return None

    log_msg = f" {user['chnname']} {user['username']} {user['status']} {user['issuer']} {user['passportNO']}"
   
    if user.get("status") == "visa_ok":
        query_slot_days(user)
        pass

    return True


def worker_registe(user: dict):
    while not user_queue.empty():
        user = user_queue.get()
        user_edit_form_pipline(user)
        time.sleep(5)
        user_queue.task_done()


def main_func():
    thread_num = 5
    while True:
        users = get_users_with_queue_name(frace_faker_queue)
        for user in users:
            if user["status"] in ["visa_ok"]:  # "pending", "gov_reg", "gov_ok", "visa_reg",
                log_msg = f" {user['chnname']} {user.get('username')} {user['status']} {user.get('fra_no')} {user.get('slot_status')} {user['centerCode']}-{user['passportNO']}"  # | password:{user['password']} | password_gov:{user['password_gov']}"
                logger.info(f"#用户信息# {log_msg}")
                user_queue.put(user)

        threads = []
        for _ in range(thread_num):
            t = Thread(target=worker_registe, args=(user_queue,))
            t.start()
            threads.append(t)

        for t in threads:
            t.join()

        time.sleep(60 * 10)  # 每10分钟查询一次


if __name__ == "__main__":
    main_func()
