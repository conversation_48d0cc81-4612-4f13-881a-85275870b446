import random
import string
import functools
import time
import base64
from bs4 import BeautifulSoup
from datetime import datetime
from extension.logger import logger
from config import CAPTCHA_KEY, issuerMap, wx_hook_urls
import requests
from curl_cffi import requests
from curl_cffi.requests import BrowserType
import hmac
import hashlib
import urllib.parse


def get_faker_user(centerCode="BEIJING"):
    username = "<EMAIL>"
    username = "<EMAIL>"
    username = "<EMAIL>"
    user_ = {
        "password": "Kq123456@",
        "password_gov": "Kq1234567890@",
        "issuer": issuerMap[centerCode],
        "queue_name": "fraceUserDatas",
        "updateTime": time.time(),
        "status": "gov_reg",
        "passportNO": "*********",
        "centerCode": centerCode,
        "birthday": "1990/11/04",
        "chnname": "方雯",
        "passportDate": "2023/08/31",
        "expiredDT": "2033/08/30",
        "gender": "女",
        "name": "WEN",
        "xing": "FANG",
        "phone": "18351100330",
        "startDate": "2025/06/01",
        "endDate": "2025/06/08",
        "visaTypeCode": "TOUR",
        "countryCode": "CHN",
        "signLocation": "江苏/JIANGSU",
        "bornplace": "江苏/JIANGSU",
        "maritalStatus": "Mar",
        "travelDate": "2025/05/13",
    }
    print(user_)
    return user_


def create_session(proxy=None):
    headers_ = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "cache-control": "no-cache",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "sec-ch-ua": '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "referer": "https://fr.tlscontact.com",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
    }
    if not proxy:
        proxy = get_new_proxy()
    proxies = {"http": proxy, "https": proxy}
    session_curl = requests.Session(impersonate=BrowserType.chrome131, proxies=proxies, headers=headers_)
    return session_curl


# 计算函数耗时的装饰器
def timmer(func):
    functools.wraps(func)

    def wrapper(*args, **kw):
        t_start = time.time()
        res = func(*args, **kw)
        print(time.time() - t_start)
        return res

    return wrapper


def log_catch(func):
    functools.wraps(func)

    def wrapper(*args, **kw):
        try:
            res = func(*args, **kw)
            return res
        except Exception as e:
            logger.error(f"函数执行错误: {func.__name__}: {str(e)}")
            # return None

    return wrapper


# 生成随机的注册邮箱
def generate_random_email(domain=""):
    domains = [
        "nextdomain1.xyz",
        "nextdomain2.xyz",
        "nextdomain3.xyz",
        "nextdomain4.xyz",
        "nextdomain5.xyz",
        "nextdomain6.xyz",
        "nextdomain7.xyz",
        "nextdomain8.xyz",
        "nextdomain9.xyz",
        "nextdomain10.xyz",
        "nextdomain11.xyz",
        "nextdomain12.xyz",
        "nextdomain13.xyz",
        "nextdomain14.xyz",
        "nextdomain15.xyz",
        "nextdomain16.xyz",
        "nextdomain17.xyz",
        "nextdomain18.xyz",
        "nextdomain19.xyz",
        "nextdomain20.xyz",
        "nextdomain21.xyz",
        "nextdomain22.xyz",
        "nextdomain23.xyz",
        "nextdomain24.xyz",
        "nextdomain25.xyz",
        "nextdomain26.xyz",
        "nextdomain27.xyz",
        "nextdomain28.xyz",
        "nextdomain29.xyz",
        "nextdomain30.xyz",
    ]
    # 生成随机的用户名部分，长度为10
    username_length = random.randint(6, 9)
    username = "".join(random.choices(string.ascii_lowercase + string.digits, k=username_length))
    domain = random.choice(domains)
    # 组合成完整的邮箱地址
    email = f"{username}@{domain}"
    return email


def generate_complex_pwd():
    # part1 = "".join(random.choices(string.ascii_uppercase, k=4))
    # part2 = "".join(random.choices(string.ascii_lowercase, k=4))
    # part3 = "".join(random.choices(string.digits, k=4))
    # part4 = "".join(random.choices(["@", "#", "$"], k=1))
    return "Qwer1234567890@"


def generate_phone_number():
    # 第一位固定为1
    first_digit = "1"
    # 第二位可以是3, 4, 5, 6, 7, 8, 9中的一个
    second_digit = random.choice(["3", "4", "5", "6", "7", "8", "9"])
    # 后面的9位数字随机生成
    rest_digits = "".join(random.choices("0123456789", k=9))
    # 组合成完整的手机号
    phone_number = first_digit + second_digit + rest_digits
    return phone_number


def generate_passport_number():
    pre_letter = "".join(random.choices(string.ascii_uppercase, k=2))
    # 生成随机的用户名部分，长度为10
    suff_numbers = "".join(random.choices("0123456789", k=8))

    return pre_letter + suff_numbers


def str_2_timestamp(date_str):
    # 定义字符串的格式
    date_format = "%Y-%m-%d"
    # 将字符串转换为datetime对象
    date_obj = datetime.strptime(date_str, date_format)
    # 将datetime对象转换为时间戳（秒数）
    timestamp = date_obj.timestamp()
    return timestamp


def get_login_proxy():
    proxy_id = "".join(random.choices(string.ascii_letters, k=8))
    proxy = f"http://17710638-res_HK_s{proxy_id}-30m:<EMAIL>:1288"  # 时效代理IP
    return proxy


def get_new_proxy():
    proxy_id = "".join(random.choices(string.ascii_letters, k=8))
    proxy = f"http://t14424785324118-period-15-sid-s{proxy_id}:<EMAIL>:15818"
    return proxy


def soup_parse(html_string):
    soup = BeautifulSoup(html_string, "html.parser")
    soup.find("form#kc-form-login")


area_map = {
    "GUANGZHOU": "广州",
    "BEIJING": "北京",
    "SHANGHAI": "上海",
    "CHENGDU": "成都",
}
wx_msg_sends = {}
def send_wx_msg(msg, centerCode=None):
    try:
        # global msg_sends
        if not centerCode:
            centerCode = "".join(random.choices(string.ascii_letters, k=8))
        if msg not in wx_msg_sends.get(centerCode, ""):
            wx_msg_sends[centerCode] = msg
            url = random.choice(wx_hook_urls)
            res = requests.post(url, json={"msgtype": "text", "text": {"content": msg}}, timeout=5)
            logger.success(f"通知到微信: {msg}, res:{res.text}")

        # msg_dd = f"巴西:{city} 地区放号。{openDays}"
        # send_dd_msg(msg_dd)
    except Exception as e:
        logger.error(f"微信通知失败{e}")


dd_msg_sends = {}
def send_dd_msg(msg="", centerCode=None):
    key = "default"
    if centerCode:
        key = centerCode

    if msg == dd_msg_sends.get(key, ""):
        return
    dd_msg_sends[key] = msg
    try:
        timestamp = str(round(time.time() * 1000))
        secret = "SECfa4fa0ed78848618c5760b2b47dfcf3c3f6b2d9e18d542b0745d4b751676fa99"
        secret_enc = secret.encode("utf-8")
        string_to_sign = "{}\n{}".format(timestamp, secret)
        string_to_sign_enc = string_to_sign.encode("utf-8")
        hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))

        if isinstance(msg, str):
            json_data = {"msgtype": "text", "text": {"content": msg}}
        elif isinstance(msg, dict):
            json_data = msg

        url = f"https://oapi.dingtalk.com/robot/send?access_token=26a07376acadb9cbad9cfc415f0af56c217db861a426defb2e55c5737a6e4bf5&timestamp={timestamp}&sign={sign}"
        response = requests.post(url, json=json_data, timeout=5)
        # logger.success(f"通知到钉钉: {msg}, res:{response.text}")
    except Exception as e:
        logger.error(f"通知到钉钉失败:{e}")


@timmer
def captcha_img(bs4_img):
    if "data:image" in bs4_img:
        bs4_img = bs4_img.split(",")[-1]

    data = {
        "clientKey": CAPTCHA_KEY,
        "task": {
            "type": "ImageToTextTaskTest",
            "body": bs4_img,  # base64编码后的图片
        },
    }
    r = requests.post("https://cn.yescaptcha.com/createTask", json=data)
    if r.status_code == 200:
        r = r.json()
        res = r["solution"]["text"]
        return res
    return None


def calculate_age(birth_date):
    """
    计算周岁年龄
    :param birth_date: 出生日期，格式 'YYYY-MM-DD'
    :param current_date: 当前日期，格式 'YYYY-MM-DD'
    :return: 周岁年龄（整数）
    """
    birth = datetime.strptime(birth_date, "%Y-%m-%d")
    current = datetime.now()

    age = current.year - birth.year

    # 如果当前月份 < 出生月份，或者月份相同但当前日期 < 出生日期，年龄减1
    if (current.month < birth.month) or (current.month == birth.month and current.day < birth.day):
        age -= 1

    return age


if __name__ == "__main__":
    pass
