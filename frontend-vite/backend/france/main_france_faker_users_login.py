from france_visa_func import user_login, user_logout
from RedisTool import save_user_2_redis_queue, get_users_with_queue_name, get_user_info
from queue import Queue
import threading
import time
import datetime
from extension.logger import logger
from tool import get_new_proxy, get_login_proxy
import cloudbypass
from config import APIKEY

user_queue = Queue()


def keep_users_login(user):
    if not user:
        return False
    user = get_user_info(user)
    log_msg = f"{user.get('username')}, {user.get('centerCode')}"
    if not user.get("is_login", False) or time.time() - int(user.get("updateTime", 0)) > 900:
        logger.info(f"##扫号用户登录## {log_msg}")
        proxy = get_login_proxy()
        proxy_login = {"http": proxy, "https": proxy}
        for i in range(5):
            session_cb = cloudbypass.SessionV2(apikey=APIKEY, proxy=proxy)
            # session_cb.proxies.update(proxy_login)
            login_flag, login_info = user_login(user, session_cb)
            if not login_flag:
                logger.error(f"##扫号用户登录## {log_msg} 登录失败,更换代理重试")
                proxy_login = get_login_proxy()
                continue
            else:
                break

        if not login_flag:
            logger.error(f"##扫号用户登录## {log_msg} 登录失败, 等待下次执行")
            return False

        cookies_, headers_ = login_info
        user["cookies"] = cookies_
        user["headers"] = headers_
        user["is_login"] = True
        user["updateTime"] = int(time.time())
        save_user_2_redis_queue(user)
        update_time = datetime.datetime.fromtimestamp(int(user["updateTime"])).strftime("%m-%d %H:%M:%S")
        logger.debug(f"##扫号用户登录## {user.get('username')}, {user.get('centerCode')}, update:{update_time}")


def thread_worker():
    while True:
        user = user_queue.get()
        if not user:
            time.sleep(1)
            continue
        keep_users_login(user)
        user_queue.put(user)
        user_queue.task_done()


def main():
    faker_users = get_users_with_queue_name()
    for user in faker_users:
        user_queue.put(user)

    thread_numer = 2
    threads = []
    for i in range(thread_numer):
        t = threading.Thread(target=thread_worker, daemon=True)
        threads.append(t)
        t.start()

        # 主线程保持活跃
    try:
        while True:
            logger.info("#登录# 主线程运行中...")
            time.sleep(900)
    except KeyboardInterrupt:
        logger.info("主线程收到中断信号，退出程序。")


if __name__ == "__main__":
    main()
