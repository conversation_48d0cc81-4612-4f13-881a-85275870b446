from extension.RedisClient import RedisClient
from extension.logger import logger
import time
import json
from tool import generate_complex_pwd, generate_passport_number, generate_phone_number
from config import issuerMap

redis_client = RedisClient(host="*************")

frace_faker_queue = "franceFakerUsers"
frace_users_queue = "franceUserDatas"
visa_logs_hash = "visa_logs_hash"


def get_email_otp(email):
    try:
        email_otp = None
        for _ in range(200):
            # 从redis获取邮箱的默认密码
            email_otp = redis_client.get(email)
            if email_otp:
                logger.info(f"邮箱: {email} -- 确认码: {email_otp[:40]}")
                redis_client.delete(email)
                break
            time.sleep(0.1)
        return email_otp
    except Exception as e:
        logger.error(f"get_email_otp Error{e}")
        return None


def delete_email_otp(email):
    try:
        res = redis_client.delete(email)
    except Exception as e:
        logger.error(f"delete_email_otp error {e}")
    return res


def get_login_cookie():
    all_cookies = redis_client.hgetall("fr_cookies")
    if len(all_cookies) > 0:
        item = all_cookies[0]
        url = item["login_url"]
        # cookie = item['cookie']
        redis_client.hdel("fr_cookies", url)
        return item
    return None


# 保存、更新扫号用户状态
def save_user_2_redis_queue(user_info, check=True):
    # 存注册好的用户信息
    queue_name = user_info.get("queue_name", None)
    if not queue_name:
        logger.error(f"#保存失败# {user_info}")
        return None

    if user_info.get("passportNO") and user_info.get("centerCode"):
        field = f"{user_info.get('centerCode')}-{user_info.get('passportNO')}"
        if check and queue_name == frace_users_queue and not redis_client.hget(queue_name, field):
            logger.warning(f"{user_info.get('email')}不在 {frace_users_queue} 表中, 更新终止。")
            return None
        res = redis_client.hset(queue_name, field, json.dumps(user_info, ensure_ascii=False))
        return res
    else:
        logger.error(f"{user_info.get('email')}更新redis用户信息失败, 缺失必要信息")
        return None

def delete_user_from_redis_queue(user_info):
    # 存注册好的用户信息
    queue_name = user_info.get("queue_name", None)
    if not queue_name:
        logger.error(f"#保存失败# {user_info}")
        return None

    if user_info.get("passportNO") and user_info.get("centerCode"):
        field = f"{user_info.get('centerCode')}-{user_info.get('passportNO')}"
        res = redis_client.hdel(queue_name, field)
        return res
    else:
        logger.error(f"{user_info.get('email')}更新redis用户信息失败, 缺失必要信息")
        return None

def get_user_info(user_info):
    queue_name = user_info.get("queue_name", None)
    field = f"{user_info.get('centerCode')}-{user_info.get('passportNO')}"
    if not queue_name or not field:
        logger.error(f"#获取信息失败# {user_info}")
        return None
    user = redis_client.hget(queue_name, field)
    if user:
        return json.loads(user)
    return None


def get_users_with_queue_name(queue_name=frace_users_queue):
    # 存注册好的用户信息
    res = redis_client.hgetall(queue_name)
    return res


def save_logs(user, log):

    if user.get("visaTypeCode") and user.get("centerCode"):
        field = f"fance-{user['centerCode']}"
        res = redis_client.hset(visa_logs_hash, field, json.dumps(log, ensure_ascii=False))
        return res
    else:
        logger.error(f"存log信息失败, 缺失必要信息 {user.get('visaTypeCode')} {user.get('centerCode')}")
        return None


def add_user_manually(centerCode="WUHAN"):
    # 存注册好的用户信息
    # username = generate_random_email()
    # centerCode = "WUHAN"
    user_ = {
        "password": "Kq123456@",
        "password_gov": generate_complex_pwd(),
        "issuer": issuerMap[centerCode],
        "queue_name": frace_faker_queue,
        "updateTime": time.time(),
        "status": "pending",
        "centerCode": centerCode,
        "chnname": "爱问熊",
        "xing": "AI",
        "name": "WENXIONG",
        "gender": "女",
        "birthday": "1988/08/29",  # ["Jan", "Feb", "Mar", "Apr", "May", 6-"Jun",  7-"Jul", 8-"Aug", 9-"Sep", 10-"Oct", "Nov", "Dec"]
        "phone": generate_phone_number(),
        "passportNO": generate_passport_number(),
        "passportDate": "2017/05/10",
        "expiredDT": "2027/05/09",
        "startDate": "2025/08/15",
        "endDate": "2025/08/30",
        "travelDate": "2025/09/07",
        "travelBack": "2025/10/05",
        "signLocation": "上海/SHANGHAI",  # "江苏/JIANGSU"  "上海/SHANGHAI" "陕西/SHAANXI"
        "bornplace": "上海/SHANGHAI",  # "贵州/GUIZHOU" "上海/SHANGHAI" "陕西/SHAANXI" "浙江/ZHEJIANG"
        "visaTypeCode": "TOUR",
        "countryCode": "CHN",
        "maritalStatus": "MAR",  # MAR  DIV
        "acceptVIP": 1,
        "users": [],
    }
    save_user_2_redis_queue(user_, check=False)
    print(user_)


if __name__ == "__main__":
    # for centerCode, issuer in issuerMap.items():
    #     print(centerCode, centerCode)
    #     add_user_manually(centerCode)
    users = get_users_with_queue_name(frace_faker_queue)
    ok_user= ["<EMAIL>"]
    for user in users:
        if user.get('username'):
            log_msg = f" {user['chnname']} {user.get('username')} {user['status']} {user['issuer']} {user.get('fra_no')} {user.get('slot_status')} {user['centerCode']}-{user['passportNO']}" # | password:{user['password']} | password_gov:{user['password_gov']}"
            print(log_msg)
            # user['slot_status'] = "no"
            # delete_user_from_redis_queue(user)
    # user_sub = user['users'][0]
    # user_sub = user | user_sub
            # user["status"] = 'gov_reg'
            # save_user_2_redis_queue(user, check=False)
