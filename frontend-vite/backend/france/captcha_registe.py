from curl_cffi import requests
from curl_cffi.requests import BrowserType
from extension.captcha_request import get_result
from bs4 import BeautifulSoup
import cloudbypass
from tool import generate_random_email, get_login_proxy, create_session
from RedisTool import get_email_otp, get_users_with_queue_name, save_user_2_redis_queue
from config import APIKEY, websiteKey_v2
from extension.logger import logger


def user_registe(user: dict):
    log_msg = f"用户信息: {user['username']}  {user['chnname']}"
    try:
        logger.info("开始tls注册: " + log_msg)
        issuer = user.get("issuer")
        session_cb = create_session()
        res_regist_url = session_cb.get(f"https://fr.tlscontact.com/api/register-url?redirectUrl=https:%2F%2Ffr.tlscontact.com%2Fvisa%2Fcn%2F{issuer}%2Fhome&issuer={issuer}")

        # logger.info(f"注册页请求:{res_regist_url.status_code}")
        if res_regist_url.status_code != 200:
            logger.info(f"获取注册页失败: {log_msg}")
            return
        # 解析注册页面
        res_regist_url = res_regist_url.text
        websiteURL = res_regist_url

        res_solution = get_result(websiteURL, websiteKey_v2)
        if not res_solution.get("solution"):
            logger.info(f"任务失败: {log_msg} {res_solution} ")
            return

        logger.success(f"captcha 解决成功: {log_msg} {res_solution['status']}")
        res_registe_page = session_cb.get(websiteURL)

        # 解析登录页面 form#kc-register-form
        registe_soup = BeautifulSoup(res_registe_page.text, "html.parser")
        registe_form = registe_soup.select_one("form#kc-register-form")
        registe_url = registe_form.attrs.get("action").replace("amp;", "")  # 处理请求地址参数 & 转码

        logger.debug(f"注册页请求: {res_registe_page.status_code} {registe_url}")

        user_name = user["username"]  # generate_random_email()
        registe_form = {
            "lastName": user["xing"],
            "firstName": user["name"],
            "email": user_name,
            "emailConfirm": user_name,
            "password": user["password"],
            "password-confirm": user["password"],
            "g-recaptcha-response": res_solution["solution"]["gRecaptchaResponse"],
        }

        res_post_registe = session_cb.post(registe_url, data=registe_form)
        if res_post_registe.status_code != 200:
            logger.error(f"注册失败: {log_msg} {res_post_registe.status_code} {res_post_registe.text[:40]}")
            return False

        logger.info(f"注册成功, 准备二次激活: {log_msg}")

        comfirm_url = get_email_otp(user_name)
        if not comfirm_url:
            logger.error(f"tls获取邮箱验证码失败: {log_msg}")
            return False
        try:
            for _ in range(5):
                res_confirm = session_cb.get(comfirm_url)
                if res_confirm.status_code != 200:
                    logger.info(f"双重确认确认失败: {log_msg} {comfirm_url}")
                else:
                    logger.success(f"激活成功: {log_msg}")
                    return True
        except Exception as e:
            logger.error(f"激活问题: {log_msg} {e}")
            return True
    except Exception as e:
        logger.error(f"注册失败: {log_msg} {e}")
        return False


if __name__ == "__main__":
    users = get_users_with_queue_name()
    for u in users:
        if u["status"] == "gov_ok":
            flag = user_registe(u)
            if flag:
                u["status"] = "visa_reg"
                # 更新用户状态
                save_user_2_redis_queue(u)
                logger.info(f"注册成功: {u}")
    logger.info("注册完成")
