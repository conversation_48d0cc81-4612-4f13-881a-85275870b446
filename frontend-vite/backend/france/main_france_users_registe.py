from RedisTool import get_users_with_queue_name, save_user_2_redis_queue, frace_users_queue, get_user_info
from france_gov_func import user_registe_woker, edit_after_login
from captcha_registe import user_registe
from france_visa_func import fill_tls_user_form
from tool import generate_complex_pwd, generate_random_email, send_dd_msg, send_wx_msg
from config import issuerMap
from extension.logger import logger
import time
from queue import Queue
from threading import Thread
import json

user_queue = Queue()


def user_edit_form_pipline(user: dict):
    if not user:
        return None
    # 处理必填字段
    necessary_keys = [
        "birthday",
        "gender",
        "travelDate",
        "travelBack",
        "visaTypeCode",
        "expiredDT",
        "passportNO",
        "passportDate",
        "signLocation",
        "name",
        "xing",
        "startDate",
        "endDate",
        "bornplace",
        "centerCode",
        "phone",
    ]
    missing_keys = []
    for key in necessary_keys:
        if not user.get(key, None):
            missing_keys.append(key)

    if len(missing_keys) > 0:
        send_dd_msg(f"#法国注册错误# 用户{user.get('chnname')}-{user.get('passportNO')}注册缺少必填字段:{' | '.join(missing_keys)}")
        send_wx_msg(f"#法国注册错误# 用户{user.get('chnname')}-{user.get('passportNO')}注册缺少必填字段:{' | '.join(missing_keys)}", centerCode=user.get("centerCode"))
        return

    if not user.get("password_gov"):
        user["password_gov"] = generate_complex_pwd()
    if not user.get("password"):
        user["password"] = "Kq123456@"
    if not user.get("queue_name"):
        user["queue_name"] = frace_users_queue
    if not user.get("issuer"):
        user["issuer"] = issuerMap.get(user["centerCode"])

    save_user_2_redis_queue(user)
    user = get_user_info(user)
    
    if not user.get("username"):
        user["username"] = generate_random_email()

    log_msg = f" {user['chnname']} {user['username']} {user['status']} {user['issuer']} {user['password_gov']}"
    logger.info("开始用户：" + log_msg)
    if user.get("status") == "pending":
        flag, res_confirm = user_registe_woker(user)
        if flag:
            user["status"] = "gov_reg"
            user["updateTime"] = int(time.time())
            save_user_2_redis_queue(user)
            logger.success(f"注册成功：{log_msg}")
        else:
            logger.error(f"注册失败：{log_msg}")
            return None

    if user.get("status") == "gov_reg":
        flag, res_confirm = edit_after_login(user)
        if flag:
            logger.success(f"编辑成功：{log_msg}")
        else:
            logger.error(f"编辑失败：{log_msg}")

    if user.get("status") == "gov_ok":
        flag = user_registe(user)
        if flag:  # 更新用户状态
            user["status"] = "visa_reg"
            save_user_2_redis_queue(user)
            log_msg = f" {user['chnname']} {user['username']} {user['centerCode']} {user['passportNO']} {user['startDate']}-{user['endDate']} {user['travelDate']}-{user['travelBack']} 价格：{user.get('price')}"
            logger.info(f"TLS注册成功: {log_msg}")
            send_dd_msg(f"#法国#TLS注册成功: {log_msg}")
            return True
        else:
            logger.error(f"#法国#TLS注册失败: {log_msg}")
            return False
    if user.get("status") == "visa_reg":
        pass
        fill_tls_user_form(user)
    return True


def worker_registe(user: dict):
    while not user_queue.empty():
        user = user_queue.get()
        flag = user_edit_form_pipline(user)
        time.sleep(1)
        user_queue.task_done()


def main_func():
    thread_num = 2
    while True:
        users = get_users_with_queue_name()
        for u in users:
            if u["status"] in ["pending", "gov_reg", "gov_ok", "visa_reg"]:
                user_queue.put(u)

        threads = []
        for _ in range(thread_num):
            t = Thread(target=worker_registe, args=(user_queue,))
            t.start()
            threads.append(t)

        for t in threads:
            t.join()

        time.sleep(60)


if __name__ == "__main__":
    main_func()
