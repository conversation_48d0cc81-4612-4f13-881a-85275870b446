from playwright.sync_api import sync_playwright
from playwright_stealth import stealth_sync
import random


def main():
    with sync_playwright() as p:
        browser = p.chromium.launch(
            headless=True,  # 反检测：切换到有头模式更保险
            args=[
                "--no-sandbox",
                "--disable-blink-features=AutomationControlled"
            ],
            proxy={
                "server": "http://h711.kdltpspro.com:15818",
                "username": "t14424785324118",
                "password": "5h4ruqed"
            }
        )
        # 自定义 UA、语言、分辨率，贴近真实用户
        context = browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                       "AppleWebKit/537.36 (KHTML, like Gecko) "
                       "Chrome/114.0.5735.199 Safari/537.36",
            viewport={"width": 1280, "height": 720},
            locale="en-US"
        )

        page = context.new_page()
        # 注入 stealth 补丁，隐藏 webdriver、修改插件、权限、指纹等
        stealth_sync(page)

        # 模拟人为的延迟和滚动
        page.goto("https://fr.tlscontact.com/visa/cn/cnSHA2fr/home")
        page.wait_for_timeout(random.randint(500, 1500))      # 0.5–1.5s 随机等待
        page.mouse.move(100, 100, steps=5)                    # 移动鼠标
        page.wait_for_timeout(random.randint(300, 800))
        page.mouse.wheel(0, 200)                              # 滚动页面

        print(page.content())
        browser.close()


if __name__ == "__main__":
    main()
