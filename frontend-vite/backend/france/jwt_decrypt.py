import jwt
from datetime import datetime
import base64
import json

JWT_FORMS_SECRET_KEY = "D3pXuLn3st2L1RP9TmbOJCgGtRmr3TE7GBR8hJKFnWMd9hUyNRagp_T5cQsdCAzcEEcdOaFJk1ALUFe_DCf-jw=="


def make_jwt_from_json(json_data: dict) -> str:
    # 将密钥编码为字节
    secret = JWT_FORMS_SECRET_KEY.encode("utf-8")

    # 复制原始数据并添加声明
    payload = json_data.copy()
    payload.update(
        {
            "exp": json_data["exp"],  # 设置过期时间
            "iat": json_data["iat"],  # 设置签发时间
        }
    )

    # 生成并返回JWT
    return jwt.encode(payload=payload, key=secret, algorithm="HS512", headers={"typ": None}, sort_headers=False)


def decode_jwt_token(token):
    try:
        # 解码令牌
        parts = token.split(".")
        if len(parts) != 3:
            raise ValueError("无效的 JWT 格式")

        # 提取 payload 部分（第二部分）
        payload_encoded = parts[1]

        # Base64Url 解码
        # 添加可能的缺失填充字符
        payload_encoded += "=" * (-len(payload_encoded) % 4)
        payload_decoded = base64.urlsafe_b64decode(payload_encoded.encode("utf-8"))

        # 转换为 JSON 对象
        payload = json.loads(payload_decoded)
        # print(payload)
        return payload
    except jwt.ExpiredSignatureError:
        print("令牌已过期")
        raise
    except jwt.InvalidTokenError as e:
        print(f"无效令牌: {e}")
        raise


token_1 = "eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mBPD7jCNDbVWPxzY0eZ6Bio_wKb_OrC-a_Jtwj8NEni5kR3OHZOfBVjszgKchv83DewLexXjcrFXi5LydaTEaw"
token_2 = "eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VA4A4HBSxJNf3G1Pfr1O6EBPlUGGDLAStybSbPMw-aRSA8v9LkSE2b3HZzs1O1YnZyPLbQ-Qhu37uwcqF62h8Q"
token_3 = "eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UkSmRtftzz20ohlq9x9teHlID6Ae3kabTEw_uimRL4bg2LZ3cqCCAropALjalvs7TkWhZPrz9M0KtBVTLv56kA"
token_4 = "eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dy1YT0xuBh-AjqE0qRwW6bsAHl0N0Ty4T13Ct7ctgVpC_GexrbAg1ZCtldmj0Ahs2LJVOg1-dB2pmhE3sZ5cPQ"
token_5 = "eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dka4BVZ0jVgsY-1NE1h5Yt6vOMAezoWzusP08erpdSNfjaVu50h0tlQOUb0hPuNs2QpYazm_fa0K0I20pOas4Q"
token_6 = "eyJhbGciOiJIUzUxMiJ9.eyJmX2NhaSI6IkZSQTFOQTIwMjU3MDEyNjAwIiwiZl9wZXJzX3N1cm5hbWVzIjoiVEFOIiwiZl9wZXJzX2dpdmVubmFtZXMiOiJYSU5HIiwiZl9wZXJzX3NleCI6Ik0iLCJmX3BlcnNfYmlydGhfZGF0ZSI6IjIwMDItMDEtMjkiLCJmX3BlcnNfbmF0aW9uYWxpdHkiOiJjbiIsImZfcGVyc19wcm92aW5jZSI6IlNIQU5HSEFJIiwiZl9pZGVudGl0eV90eXBlIjoib3JkaW5hcnlfcGFzc3BvcnQiLCJmX3Bhc3NfbnVtIjoiRUMxMDU1MTRDIiwiZl9wZXJzX21vYmlsZV9waG9uZSI6Ijg2MTc4OTgzMDI0MzIiLCJmaV90cmF2X29yaWdpbl9kZXBhcnR1cmVfZGF0ZSI6IjIwMjUtMDQtMzAiLCJmX3RyYXZfZGVwYXJ0dXJlX2RhdGUiOiIyMDI1LTA0LTMwIiwiZl90cmF2X2Fycml2YWxfZGF0ZSI6IjIwMjUtMDUtMzAiLCJmX3RyYXZfZ29fdG9fZG9tdG9tIjoiZiIsImZpX2ZpbmdlcnByaW50c19jb2xsZWN0ZWQiOiJmIiwiZmlfZmlyc3Rfc2NoZW5nZW5fdHJpcCI6ImYiLCJmX3Zpc2FfdHlwZSI6InNob3J0X3N0YXkiLCJmX3RyYXZfcHVycG9zZSI6InRvdXJpc21fcHJpdmF0ZV92aXNpdCIsImZfc3RhdHVzIjoiZG9uZSIsImZnX2lkIjoiMTkzMTUxNjQiLCJmX3hyZWZfZmdfaWQiOiIxOTMxNTE2NCIsImZfaWQiOjI3MTg0NTA0LCJmX3hyZWZfZl9pZCI6MjcxODQ1MDQsImlhdCI6MTc0NTY3NTE5OSwiZXhwIjoxNzQ1Njc1MjM0fQ.OMAuJAMW6akNxSK35ceW-lBEb3oA4IJRQkhPFTiCI-4_TrfzEYCT7mx24R1Hp73rayQ-3h0d27UGmado5NOB5w"

# all_req = []
# for token in [token_1, token_2, token_3, token_4, token_5, token_6]:
#     req = decode_jwt_token(token)
#     all_req.append(dict(req))

# with open("req.json", "w", encoding="utf-8") as f:
#     json.dump(all_req, f, ensure_ascii=False)
