#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美国签证极简版API测试脚本
测试新的数据结构：只需要姓名、美签账户信息和验证问题
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8000"

def test_us_visa_minimal_api():
    """测试美国签证极简版API"""
    print("🇺🇸 开始测试美国签证极简版API...")
    
    # 测试数据（极简版）
    test_data = {
        "clients": [
            {
                "name": "张三",  # 用户姓名
                # 美国签证特殊字段
                "us_username": "test_username",  # 美签账户用户名
                "us_password": "test_password",  # 美签账户密码
                "us_qa": [  # 验证问题和答案
                    {
                        "DISP": "您母亲的姓氏是什么?",
                        "VAL": "张"
                    },
                    {
                        "DISP": "您的第一个/目前/最喜欢的宠物名称是什么?",
                        "VAL": "小白"
                    },
                    {
                        "DISP": "您出生在哪个城市?",
                        "VAL": "北京"
                    }
                ]
            }
        ],
        "visaType": [["usa", "US_DEFAULT", "TOURIST", "B1_B2"]],
        "dateRangeList": [["2024-03-01", "2024-03-31"]],
        "travel_date": "2024-03-15",
        "customer": "测试客户",
        "remark": "美国签证极简版测试"
    }
    
    # 设置请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test_token"  # 根据实际情况调整
    }
    
    try:
        # 发送POST请求
        print("📤 发送请求到:", f"{BASE_URL}/api/submit_us_visa_info")
        print("📋 请求数据:")
        print(json.dumps(test_data, indent=2, ensure_ascii=False))
        
        response = requests.post(
            f"{BASE_URL}/api/submit_us_visa_info",
            json=test_data,
            headers=headers,
            timeout=30
        )
        
        print(f"📨 响应状态码: {response.status_code}")
        print("📄 响应内容:")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            # 检查响应
            if response.status_code == 200:
                if response_data.get("code") == 1:
                    print("✅ 美国签证极简版API测试成功!")
                    submission_id = response_data.get("data", {}).get("submission_id")
                    if submission_id:
                        print(f"📝 提交ID: {submission_id}")
                        return submission_id
                else:
                    print(f"❌ API返回错误: {response_data.get('message', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        else:
            print("📄 非JSON响应:")
            print(response.text)
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")
    
    return None

def test_invalid_data():
    """测试无效数据"""
    print("\n🧪 测试无效数据...")
    
    # 缺少必填字段的测试数据
    invalid_data = {
        "clients": [
            {
                "name": "",  # 空姓名
                "us_username": "test_username",
                "us_password": "",  # 空密码
                "us_qa": [
                    {"DISP": "", "VAL": ""},  # 空问题和答案
                    {"DISP": "问题2", "VAL": ""},  # 缺少答案
                    {"DISP": "", "VAL": "答案3"}  # 缺少问题
                ]
            }
        ],
        "visaType": [["usa", "US_DEFAULT", "TOURIST", "B1_B2"]],
        "dateRangeList": [["2024-03-01", "2024-03-31"]]
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test_token"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/submit_us_visa_info",
            json=invalid_data,
            headers=headers,
            timeout=30
        )
        
        print(f"📨 响应状态码: {response.status_code}")
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print("📄 响应内容:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            if response_data.get("code") == 0:
                print("✅ 无效数据验证测试通过 - 正确拒绝了无效数据")
            else:
                print("❌ 无效数据验证测试失败 - 应该拒绝无效数据")
        else:
            print("📄 非JSON响应:")
            print(response.text)
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_get_submissions():
    """测试获取提交记录"""
    print("\n📋 测试获取提交记录...")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test_token"
    }
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/get_us_visa_submissions",
            headers=headers,
            timeout=30
        )
        
        print(f"📨 响应状态码: {response.status_code}")
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print("📄 响应内容:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            if response.status_code == 200 and response_data.get("code") == 1:
                submissions = response_data.get("data", [])
                print(f"✅ 获取到 {len(submissions)} 条提交记录")
                
                # 检查数据结构
                for i, submission in enumerate(submissions):
                    print(f"\n📝 提交记录 {i+1}:")
                    print(f"   ID: {submission.get('submission_id')}")
                    print(f"   用户: {submission.get('username')}")
                    print(f"   客户数量: {len(submission.get('clients', []))}")
                    
                    # 检查客户数据结构
                    for j, client in enumerate(submission.get('clients', [])):
                        print(f"   客户 {j+1}: {client.get('name')}")
                        print(f"     美签账户: {client.get('us_username')}")
                        print(f"     验证问题数量: {len(client.get('us_qa', []))}")
            else:
                print("❌ 获取提交记录失败")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🇺🇸 美国签证极简版API测试")
    print("=" * 60)
    print("📋 测试内容:")
    print("   1. 只需要4个字段：姓名、美签账户用户名、密码、验证问题")
    print("   2. 验证问题使用qa数组格式")
    print("   3. 移除所有其他不需要的字段")
    print("=" * 60)
    
    # 测试正常提交
    submission_id = test_us_visa_minimal_api()
    
    # 测试无效数据
    test_invalid_data()
    
    # 测试获取提交记录
    test_get_submissions()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)
    
    if submission_id:
        print(f"✅ 成功提交ID: {submission_id}")
    else:
        print("❌ 提交失败，请检查服务器状态")

if __name__ == "__main__":
    main()
