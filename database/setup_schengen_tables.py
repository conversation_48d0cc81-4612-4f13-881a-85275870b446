#!/usr/bin/env python3
"""
申根签证数据库表创建脚本
使用此脚本来创建申根签证相关的PostgreSQL表
"""

import psycopg2
import os
from pathlib import Path

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'visa_booking_sys',  # 请根据您的数据库名称修改
    'user': 'your_username',         # 请根据您的用户名修改
    'password': 'your_password'      # 请根据您的密码修改
}

def read_sql_file():
    """读取SQL文件内容"""
    sql_file_path = Path(__file__).parent / 'schengen_tables.sql'
    
    if not sql_file_path.exists():
        raise FileNotFoundError(f"SQL文件不存在: {sql_file_path}")
    
    with open(sql_file_path, 'r', encoding='utf-8') as f:
        return f.read()

def execute_sql_script():
    """执行SQL脚本"""
    try:
        # 连接数据库
        print("正在连接数据库...")
        conn = psycopg2.connect(**DB_CONFIG)
        conn.autocommit = True
        
        with conn.cursor() as cur:
            # 读取SQL文件
            print("正在读取SQL文件...")
            sql_content = read_sql_file()
            
            # 执行SQL脚本
            print("正在创建申根签证表...")
            cur.execute(sql_content)
            
            print("✅ 申根签证表创建成功！")
            
            # 验证表是否创建成功
            print("\n正在验证表创建结果...")
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'schengen_%'
                ORDER BY table_name;
            """)
            
            tables = cur.fetchall()
            print(f"✅ 成功创建 {len(tables)} 个申根签证相关表:")
            for table in tables:
                print(f"   - {table[0]}")
                
    except psycopg2.Error as e:
        print(f"❌ 数据库错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()
            print("\n数据库连接已关闭")
    
    return True

def check_existing_tables():
    """检查是否已存在申根签证表"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        with conn.cursor() as cur:
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'schengen_%';
            """)
            existing_tables = cur.fetchall()
            
        conn.close()
        return [table[0] for table in existing_tables]
        
    except psycopg2.Error as e:
        print(f"❌ 检查表时出错: {e}")
        return []

def main():
    """主函数"""
    print("=" * 60)
    print("申根签证数据库表创建脚本")
    print("=" * 60)
    
    # 检查现有表
    existing_tables = check_existing_tables()
    if existing_tables:
        print(f"⚠️  检测到已存在的申根签证表: {', '.join(existing_tables)}")
        response = input("是否继续执行？这将重新创建表 (y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            return
    
    # 执行创建脚本
    if execute_sql_script():
        print("\n🎉 申根签证表创建完成！")
        print("\n📋 创建的表包括:")
        print("   1. schengen_visa_applications - 主申请表")
        print("   2. schengen_passport_info - 护照信息表")
        print("   3. schengen_applicant_info - 申请人信息表")
        print("   4. schengen_travel_info - 旅行信息表")
        print("   5. schengen_accommodation_info - 住宿信息表")
        print("   6. schengen_additional_info - 附加信息表")
        print("\n✨ 现在可以使用申根签证表单功能了！")
    else:
        print("\n❌ 表创建失败，请检查错误信息")

if __name__ == "__main__":
    main()
