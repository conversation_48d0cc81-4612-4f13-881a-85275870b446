-- ==================== 申根签证数据库表结构 ====================
-- PostgreSQL版本
-- 执行顺序：按照文件中的顺序依次执行

-- 1. 申根签证表单主表
CREATE TABLE schengen_visa_applications (
    id BIGSERIAL PRIMARY KEY,
    application_id VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'processing', 'approved', 'rejected')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP NULL,
    
    -- 第一步：基本资格信息
    route_id INTEGER DEFAULT 1,
    web_ref_no VARCHAR(100),
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    gender_id VARCHAR(10) NOT NULL CHECK (gender_id IN ('MALE', 'FEMALE')),
    date_of_birth_year INTEGER NOT NULL,
    date_of_birth_month INTEGER NOT NULL,
    date_of_birth_day INTEGER NOT NULL,
    nationality_id VARCHAR(10) NOT NULL,
    passport_number VARCHAR(50) NOT NULL,
    expiry_date_year INTEGER NOT NULL,
    expiry_date_month INTEGER NOT NULL,
    expiry_date_day INTEGER NOT NULL,
    is_draft BOOLEAN DEFAULT FALSE,
    stage VARCHAR(50) DEFAULT 'eligibilityCriteria'
);

-- 创建索引
CREATE INDEX idx_schengen_user_id ON schengen_visa_applications(user_id);
CREATE INDEX idx_schengen_application_id ON schengen_visa_applications(application_id);
CREATE INDEX idx_schengen_status ON schengen_visa_applications(status);
CREATE INDEX idx_schengen_created_at ON schengen_visa_applications(created_at);

-- 添加表注释
COMMENT ON TABLE schengen_visa_applications IS '申根签证申请主表';
COMMENT ON COLUMN schengen_visa_applications.application_id IS '申请ID';
COMMENT ON COLUMN schengen_visa_applications.user_id IS '用户ID';
COMMENT ON COLUMN schengen_visa_applications.status IS '申请状态';
COMMENT ON COLUMN schengen_visa_applications.first_name IS '名';
COMMENT ON COLUMN schengen_visa_applications.last_name IS '姓';
COMMENT ON COLUMN schengen_visa_applications.gender_id IS '性别';
COMMENT ON COLUMN schengen_visa_applications.nationality_id IS '国籍代码';
COMMENT ON COLUMN schengen_visa_applications.passport_number IS '护照号码';

-- 2. 护照详细信息表
CREATE TABLE schengen_passport_info (
    id BIGSERIAL PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    sur_name VARCHAR(100) NOT NULL,
    surname_at_birth VARCHAR(100),
    given_name VARCHAR(100) NOT NULL,
    date_of_birth_year INTEGER NOT NULL,
    date_of_birth_month INTEGER NOT NULL,
    date_of_birth_day INTEGER NOT NULL,
    country_of_birth VARCHAR(10) NOT NULL,
    place_of_birth VARCHAR(200) NOT NULL,
    nationality_id VARCHAR(10) NOT NULL,
    nationality_at_birth_id VARCHAR(10) NOT NULL,
    gender_id VARCHAR(10) NOT NULL CHECK (gender_id IN ('MALE', 'FEMALE')),
    marital_status_id VARCHAR(10) NOT NULL,
    is_minor_applicant INTEGER NOT NULL,
    id_number VARCHAR(50) NOT NULL,
    passport_type_id VARCHAR(10) NOT NULL,
    passport_number VARCHAR(50) NOT NULL,
    reenter_number_of_passport VARCHAR(50) NOT NULL,
    issue_date_year INTEGER NOT NULL,
    issue_date_month INTEGER NOT NULL,
    issue_date_day INTEGER NOT NULL,
    issued_country VARCHAR(10) NOT NULL,
    expiry_date_year INTEGER NOT NULL,
    expiry_date_month INTEGER NOT NULL,
    expiry_date_day INTEGER NOT NULL,
    issued_by VARCHAR(200) NOT NULL,
    is_draft BOOLEAN DEFAULT FALSE,
    stage VARCHAR(50) DEFAULT 'applicantInformation',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_passport_application FOREIGN KEY (application_id) 
        REFERENCES schengen_visa_applications(application_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_passport_application_id ON schengen_passport_info(application_id);

-- 添加注释
COMMENT ON TABLE schengen_passport_info IS '申根签证护照详细信息表';
COMMENT ON COLUMN schengen_passport_info.sur_name IS '护照姓';
COMMENT ON COLUMN schengen_passport_info.surname_at_birth IS '出生时姓';
COMMENT ON COLUMN schengen_passport_info.given_name IS '护照名';
COMMENT ON COLUMN schengen_passport_info.country_of_birth IS '出生国';
COMMENT ON COLUMN schengen_passport_info.place_of_birth IS '出生地';
COMMENT ON COLUMN schengen_passport_info.nationality_id IS '现国籍';
COMMENT ON COLUMN schengen_passport_info.nationality_at_birth_id IS '出生时国籍';
COMMENT ON COLUMN schengen_passport_info.marital_status_id IS '婚姻状况';
COMMENT ON COLUMN schengen_passport_info.is_minor_applicant IS '是否未成年';
COMMENT ON COLUMN schengen_passport_info.id_number IS '身份证号';
COMMENT ON COLUMN schengen_passport_info.passport_type_id IS '护照类型';
COMMENT ON COLUMN schengen_passport_info.issued_country IS '签发国';
COMMENT ON COLUMN schengen_passport_info.issued_by IS '签发机关';

-- 3. 申请人信息表
CREATE TABLE schengen_applicant_info (
    id BIGSERIAL PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    application_date DATE NULL,
    applicant_country VARCHAR(10) NOT NULL,
    applicant_address TEXT NOT NULL,
    occupation_id VARCHAR(10) NOT NULL,
    occupation_others VARCHAR(200) NULL,
    applicant_email VARCHAR(200) NOT NULL,
    applicant_telephone_isd_code VARCHAR(10) DEFAULT '+86',
    applicant_telephone_number VARCHAR(50) NOT NULL,
    residence_other_nationality INTEGER NOT NULL,
    residence_country_permit_no VARCHAR(100) NULL,
    residence_country_permit_valid_until_year INTEGER NULL,
    residence_country_permit_valid_until_month INTEGER NULL,
    residence_country_permit_valid_until_day INTEGER NULL,
    employer_name VARCHAR(200) NULL,
    employer_address TEXT NULL,
    employer_mobile VARCHAR(50) NULL,
    employer_city VARCHAR(100) NULL,
    employer_home_postal_code VARCHAR(20) NULL,
    employer_home_country VARCHAR(10) NULL,
    fingerprints_collected INTEGER NOT NULL,
    date_of_collection_year INTEGER NULL,
    date_of_collection_month INTEGER NULL,
    date_of_collection_day INTEGER NULL,
    previous_application_number VARCHAR(100) NULL,
    is_draft BOOLEAN DEFAULT FALSE,
    stage VARCHAR(50) DEFAULT 'travelInformation',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_applicant_application FOREIGN KEY (application_id)
        REFERENCES schengen_visa_applications(application_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_applicant_application_id ON schengen_applicant_info(application_id);

-- 添加注释
COMMENT ON TABLE schengen_applicant_info IS '申根签证申请人信息表';
COMMENT ON COLUMN schengen_applicant_info.applicant_country IS '申请者居住国';
COMMENT ON COLUMN schengen_applicant_info.applicant_address IS '申请者住址';
COMMENT ON COLUMN schengen_applicant_info.occupation_id IS '职业代码';
COMMENT ON COLUMN schengen_applicant_info.occupation_others IS '其他职业描述';
COMMENT ON COLUMN schengen_applicant_info.applicant_email IS '申请者邮箱';
COMMENT ON COLUMN schengen_applicant_info.residence_other_nationality IS '是否居住在现国籍以外';
COMMENT ON COLUMN schengen_applicant_info.residence_country_permit_no IS '居留许可编号';
COMMENT ON COLUMN schengen_applicant_info.employer_name IS '雇主名称';
COMMENT ON COLUMN schengen_applicant_info.employer_address IS '雇主地址';
COMMENT ON COLUMN schengen_applicant_info.employer_mobile IS '雇主电话';
COMMENT ON COLUMN schengen_applicant_info.employer_city IS '雇主城市';
COMMENT ON COLUMN schengen_applicant_info.employer_home_postal_code IS '雇主邮编';
COMMENT ON COLUMN schengen_applicant_info.employer_home_country IS '雇主国家';
COMMENT ON COLUMN schengen_applicant_info.fingerprints_collected IS '是否采集过指纹';
COMMENT ON COLUMN schengen_applicant_info.previous_application_number IS '上次申请号';

-- 4. 旅行信息表
CREATE TABLE schengen_travel_info (
    id BIGSERIAL PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    purpose_of_travel VARCHAR(10) NOT NULL,
    purpose_of_travel_others VARCHAR(200) NULL,
    purpose_of_travel_add_info TEXT NOT NULL,
    number_of_entries VARCHAR(10) NOT NULL,
    is_schengen_visa_issued INTEGER NOT NULL,
    valid_from_year INTEGER NULL,
    valid_from_month INTEGER NULL,
    valid_from_day INTEGER NULL,
    valid_till_year INTEGER NULL,
    valid_till_month INTEGER NULL,
    valid_till_day INTEGER NULL,
    is_adequate_medical_insurance INTEGER NOT NULL,
    is_draft BOOLEAN DEFAULT FALSE,
    stage VARCHAR(50) DEFAULT 'accommodationInformation',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_travel_application FOREIGN KEY (application_id)
        REFERENCES schengen_visa_applications(application_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_travel_application_id ON schengen_travel_info(application_id);

-- 添加注释
COMMENT ON TABLE schengen_travel_info IS '申根签证旅行信息表';
COMMENT ON COLUMN schengen_travel_info.purpose_of_travel IS '旅行目的';
COMMENT ON COLUMN schengen_travel_info.purpose_of_travel_add_info IS '停留目的补充信息';
COMMENT ON COLUMN schengen_travel_info.number_of_entries IS '入境次数';
COMMENT ON COLUMN schengen_travel_info.is_schengen_visa_issued IS '是否有申根签证';
COMMENT ON COLUMN schengen_travel_info.is_adequate_medical_insurance IS '是否有旅行保险';

-- 5. 住宿信息表
CREATE TABLE schengen_accommodation_info (
    id BIGSERIAL PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    inviting_party_id INTEGER NOT NULL,

    -- 邀请机构信息
    name_of_organisation VARCHAR(200) NULL,
    address_of_organisation TEXT NULL,
    name_of_enterprise VARCHAR(200) NULL,
    address_of_enterprise TEXT NULL,
    postal_code_of_enterprise VARCHAR(20) NULL,
    email_enterprise VARCHAR(200) NULL,
    street_enterprise VARCHAR(200) NULL,
    city_enterprise VARCHAR(100) NULL,
    country_enterprise VARCHAR(10) NULL,
    enterprise_telephone_isd_code VARCHAR(10) NULL,
    enterprise_telephone_number VARCHAR(50) NULL,

    -- 邀请人信息
    sur_name_of_contact_inviting_person VARCHAR(100) NULL,
    first_name_of_contact_inviting_person VARCHAR(100) NULL,
    address_of_inviting_person TEXT NULL,
    postal_code_of_inviting_person VARCHAR(20) NULL,
    email_inviting_person VARCHAR(200) NULL,
    street_inviting_person VARCHAR(200) NULL,
    city_inviting_person VARCHAR(100) NULL,
    country_inviting_person VARCHAR(10) NULL,

    -- 酒店信息
    name_of_inviting_hotel VARCHAR(200) NULL,
    address_of_inviting_hotel TEXT NULL,
    postal_code_of_inviting_hotel VARCHAR(20) NULL,
    email_inviting_hotel VARCHAR(200) NULL,
    street_inviting_hotel VARCHAR(200) NULL,
    city_inviting_hotel VARCHAR(100) NULL,
    country_inviting_hotel VARCHAR(10) NULL,
    inviting_hotel_telephone_isd_code VARCHAR(10) NULL,
    inviting_hotel_telephone_number VARCHAR(50) NULL,

    is_draft BOOLEAN DEFAULT FALSE,
    stage VARCHAR(50) DEFAULT 'vafInformation',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_accommodation_application FOREIGN KEY (application_id)
        REFERENCES schengen_visa_applications(application_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_accommodation_application_id ON schengen_accommodation_info(application_id);

-- 添加注释
COMMENT ON TABLE schengen_accommodation_info IS '申根签证住宿信息表';
COMMENT ON COLUMN schengen_accommodation_info.inviting_party_id IS '邀请方类型ID';

-- 6. 附加信息表
CREATE TABLE schengen_additional_info (
    id BIGSERIAL PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    final_destination INTEGER NOT NULL,
    arrival_date_year INTEGER NOT NULL,
    arrival_date_month INTEGER NOT NULL,
    arrival_date_day INTEGER NOT NULL,
    departure_date_year INTEGER NOT NULL,
    departure_date_month INTEGER NOT NULL,
    departure_date_day INTEGER NOT NULL,
    duration_of_stay VARCHAR(10) NOT NULL,
    cost_of_travelling_covered_by JSONB NOT NULL,
    cost_of_travelling_covered_by_others VARCHAR(200) NULL,
    means_of_support_id JSONB NOT NULL,
    means_of_support_others VARCHAR(200) NULL,
    is_citizen_id INTEGER NOT NULL,
    eu_surname VARCHAR(100) NULL,
    eu_first_name VARCHAR(100) NULL,
    eu_nationality_id VARCHAR(10) NULL,
    eu_date_of_birth_year INTEGER NULL,
    eu_date_of_birth_month INTEGER NULL,
    eu_date_of_birth_day INTEGER NULL,
    eu_passport_number VARCHAR(50) NULL,
    eu_relationship_id VARCHAR(10) NULL,
    schengen_state_first_entry VARCHAR(10) NOT NULL,
    country_of_destination JSONB NOT NULL,
    inviting_person_covered_costs INTEGER NOT NULL,
    is_draft BOOLEAN DEFAULT FALSE,
    stage VARCHAR(50) DEFAULT 'ReceivedAtAC',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_additional_application FOREIGN KEY (application_id)
        REFERENCES schengen_visa_applications(application_id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_additional_application_id ON schengen_additional_info(application_id);

-- 添加注释
COMMENT ON TABLE schengen_additional_info IS '申根签证附加信息表';
COMMENT ON COLUMN schengen_additional_info.final_destination IS '过境签证';
COMMENT ON COLUMN schengen_additional_info.duration_of_stay IS '停留天数';
COMMENT ON COLUMN schengen_additional_info.cost_of_travelling_covered_by IS '费用承担方';
COMMENT ON COLUMN schengen_additional_info.means_of_support_id IS '支付方式';
COMMENT ON COLUMN schengen_additional_info.is_citizen_id IS '是否欧盟公民家庭成员';
COMMENT ON COLUMN schengen_additional_info.schengen_state_first_entry IS '首入申根国';
COMMENT ON COLUMN schengen_additional_info.country_of_destination IS '目的地国家';
COMMENT ON COLUMN schengen_additional_info.inviting_person_covered_costs IS '邀请人支付费用';

-- 7. 创建更新时间的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为每个表创建触发器
CREATE TRIGGER update_schengen_visa_applications_updated_at
    BEFORE UPDATE ON schengen_visa_applications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schengen_passport_info_updated_at
    BEFORE UPDATE ON schengen_passport_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schengen_applicant_info_updated_at
    BEFORE UPDATE ON schengen_applicant_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schengen_travel_info_updated_at
    BEFORE UPDATE ON schengen_travel_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schengen_accommodation_info_updated_at
    BEFORE UPDATE ON schengen_accommodation_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_schengen_additional_info_updated_at
    BEFORE UPDATE ON schengen_additional_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==================== 执行完成 ====================
-- 所有申根签证相关表已创建完成
--
-- 执行顺序：
-- 1. schengen_visa_applications (主表)
-- 2. schengen_passport_info
-- 3. schengen_applicant_info
-- 4. schengen_travel_info
-- 5. schengen_accommodation_info
-- 6. schengen_additional_info
-- 7. 触发器函数和触发器
--
-- 特性：
-- - 外键约束确保数据完整性
-- - 自动更新时间戳
-- - JSONB字段支持复杂数据
-- - 完整的索引优化
-- - 详细的字段注释
