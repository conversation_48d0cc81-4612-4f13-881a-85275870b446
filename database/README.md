# 申根签证数据库表创建指南

## 📋 概述

本目录包含创建申根签证表单功能所需的PostgreSQL数据库表结构和设置脚本。

## 📁 文件说明

- `schengen_tables.sql` - PostgreSQL表结构定义文件
- `setup_schengen_tables.py` - Python自动化创建脚本
- `README.md` - 本说明文件

## 🗄️ 数据库表结构

申根签证表单系统包含以下6个主要表：

### 1. schengen_visa_applications (主申请表)
- 存储基本资格信息（第一步）
- 包含申请ID、用户ID、状态等核心字段
- 作为其他表的外键引用

### 2. schengen_passport_info (护照信息表)
- 存储护照详细信息（第二步）
- 包含护照号、签发信息、个人详细资料

### 3. schengen_applicant_info (申请人信息表)
- 存储申请人信息（第三步）
- 包含职业、联系方式、雇主信息、指纹采集等

### 4. schengen_travel_info (旅行信息表)
- 存储旅行计划信息（第四步）
- 包含旅行目的、入境次数、签证历史、保险信息

### 5. schengen_accommodation_info (住宿信息表)
- 存储住宿安排信息（第五步）
- 包含邀请方信息（个人/企业/酒店/机构）

### 6. schengen_additional_info (附加信息表)
- 存储附加信息及确认（第六步）
- 包含费用承担、欧盟关系、目的地国家等

## 🚀 快速开始

### 方法一：使用Python脚本（推荐）

1. **配置数据库连接**
   ```bash
   # 编辑 setup_schengen_tables.py 文件
   # 修改 DB_CONFIG 中的数据库连接信息
   DB_CONFIG = {
       'host': 'localhost',
       'port': 5432,
       'database': 'your_database_name',
       'user': 'your_username',
       'password': 'your_password'
   }
   ```

2. **运行创建脚本**
   ```bash
   cd database
   python setup_schengen_tables.py
   ```

3. **验证创建结果**
   脚本会自动验证表是否创建成功并显示结果。

### 方法二：手动执行SQL

1. **连接到PostgreSQL数据库**
   ```bash
   psql -h localhost -U your_username -d your_database_name
   ```

2. **执行SQL文件**
   ```sql
   \i schengen_tables.sql
   ```

3. **验证表创建**
   ```sql
   \dt schengen_*
   ```

## 🔧 表特性

### 数据完整性
- ✅ 外键约束确保数据一致性
- ✅ 检查约束验证数据有效性
- ✅ 非空约束保证必填字段

### 性能优化
- ✅ 主键和外键索引
- ✅ 常用查询字段索引
- ✅ 复合索引优化查询

### 自动化功能
- ✅ 自动更新时间戳
- ✅ 触发器维护数据一致性
- ✅ JSONB字段支持复杂数据

### 数据类型
- ✅ 日期分解为年/月/日字段
- ✅ JSONB存储数组和对象
- ✅ TEXT字段支持长文本
- ✅ 枚举约束确保数据规范

## 📊 表关系图

```
schengen_visa_applications (主表)
├── schengen_passport_info
├── schengen_applicant_info
├── schengen_travel_info
├── schengen_accommodation_info
└── schengen_additional_info
```

## 🔍 验证安装

创建表后，可以运行以下查询验证：

```sql
-- 检查所有申根表
SELECT table_name, table_comment 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'schengen_%'
ORDER BY table_name;

-- 检查表结构
\d+ schengen_visa_applications

-- 检查外键约束
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name LIKE 'schengen_%';
```

## ⚠️ 注意事项

1. **备份数据库**：在执行脚本前请备份现有数据库
2. **权限要求**：确保数据库用户有CREATE TABLE权限
3. **依赖检查**：确保PostgreSQL版本支持JSONB类型（9.4+）
4. **字符编码**：建议使用UTF-8编码

## 🐛 故障排除

### 常见错误

1. **连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数是否正确
   - 确认防火墙设置

2. **权限不足**
   ```sql
   GRANT CREATE ON DATABASE your_database TO your_user;
   ```

3. **表已存在**
   - 脚本会提示是否覆盖现有表
   - 可以手动删除后重新创建

4. **外键约束错误**
   - 确保按照正确顺序创建表
   - 检查引用的表是否存在

## 📞 支持

如果遇到问题，请检查：
1. PostgreSQL日志文件
2. Python脚本输出的错误信息
3. 数据库连接配置

## 🎯 下一步

表创建完成后，您可以：
1. 启动后端API服务
2. 测试申根表单功能
3. 查看前端表单界面
4. 验证数据保存功能
